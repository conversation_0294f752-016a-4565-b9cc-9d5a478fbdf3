[Log] SimulationViewport unmounting (chunk-3CEHBUVK.js, line 16764)
[Log] Cleaning up canvas render loop (SimulationViewport.tsx, line 143)
[Log] SimulationViewport mounting (SimulationViewport.tsx, line 37)
[Debug] SimulationViewport props: – {isRunning: false, currentGCodeFile: "square.nc", simulatedPoints: "1 points", …} (SimulationViewport.tsx, line 38)
{isRunning: false, currentGCodeFile: "square.nc", simulatedPoints: "1 points", currentToolPosition: {x: 0, y: 0, z: 0}, isSimulated: false, …}Object
[Log] Setting up canvas render loop (SimulationViewport.tsx, line 60)
[Log] Current tool position: – {x: 0, y: 0, z: 0} (SimulationViewport.tsx, line 61)
[Log] Simulated points: – "1 points" (SimulationViewport.tsx, line 62)
[Log] Rendering single frame - simulation not running (SimulationViewport.tsx, line 140)
[Log] Simulation started with currentLineRef = – 0 (index.tsx, line 315)
[Log] SimulationViewport unmounting (chunk-3CEHBUVK.js, line 16764)
[Log] Cleaning up canvas render loop (SimulationViewport.tsx, line 143)
[Log] SimulationViewport mounting (SimulationViewport.tsx, line 37)
[Debug] SimulationViewport props: – {isRunning: true, currentGCodeFile: "square.nc", simulatedPoints: "1 points", …} (SimulationViewport.tsx, line 38)
{isRunning: true, currentGCodeFile: "square.nc", simulatedPoints: "1 points", currentToolPosition: {x: 0, y: 0, z: 0}, isSimulated: false, …}Object
[Log] Setting up canvas render loop (SimulationViewport.tsx, line 60)
[Log] Current tool position: – {x: 0, y: 0, z: 0} (SimulationViewport.tsx, line 61)
[Log] Simulated points: – "1 points" (SimulationViewport.tsx, line 62)
[Log] Starting continuous render loop - simulation is running (SimulationViewport.tsx, line 137)
[Log] Run effect triggered: isRunning=true, isPaused=false, isSimulated=false, speed=1 (index.tsx, line 409)
[Log] Starting simulation run loop (index.tsx, line 417)
[Log] Cleaning up canvas render loop (SimulationViewport.tsx, line 143)
[Log] Setting up canvas render loop (SimulationViewport.tsx, line 60)
[Log] Current tool position: – {x: 0, y: 0, z: 0} (SimulationViewport.tsx, line 61)
[Log] Simulated points: – "1 points" (SimulationViewport.tsx, line 62)
[Log] Starting continuous render loop - simulation is running (SimulationViewport.tsx, line 137)
[Log] Rendering tool at position: (0, 0, 0) (canvas2DRenderer.ts, line 129)
[Log] Run function called, currentLine=0, total=11, speed=1.0x (index.tsx, line 239)
[Log] Processing line 1/11 (index.tsx, line 255)
[Log] Step function called: isSimulated=false, isProcessing=true, currentLine=0, totalLines=11 (index.tsx, line 144)
[Log] Checking for M30 in line: "(SAMPLE SQUARE TOOLPATH FOR VIZION3D - 2D TEST)" (index.tsx, line 174)
[Log] Skipping comment or empty line: "(Sample square toolpath for Vizion3D - 2D test)" (index.tsx, line 209)
[Log] Tool moved from (0, 0, 0) to (0, 0, 0) (index.tsx, line 211)
[Log] Advanced to next line: 1 (index.tsx, line 216)
[Log] Setting timeout for next line: 500ms (1.0x speed) (index.tsx, line 261)
[Log] Continuing simulation after timeout (index.tsx, line 268)
[Log] Run function called, currentLine=1, total=11, speed=1.0x (index.tsx, line 239)
[Log] Processing line 2/11 (index.tsx, line 255)
[Log] Step function called: isSimulated=false, isProcessing=true, currentLine=1, totalLines=11 (index.tsx, line 144)
[Log] Checking for M30 in line: "G90 G54 G17 ; ABSOLUTE POSITIONING, WORK OFFSET, XY PLANE SELECTION" (index.tsx, line 174)
[Log] Processing line 2: G90 G54 G17 ; Absolute positioning, work offset, XY plane selection (index.tsx, line 185)
[Log] Tool position unchanged, skipping state update (index.tsx, line 206)
[Log] Tool moved from (0, 0, 0) to (0, 0, 0) (index.tsx, line 211)
[Log] Advanced to next line: 2 (index.tsx, line 216)
[Log] Setting timeout for next line: 500ms (1.0x speed) (index.tsx, line 261)
[Log] Continuing simulation after timeout (index.tsx, line 268)
[Log] Run function called, currentLine=2, total=11, speed=1.0x (index.tsx, line 239)
[Log] Processing line 3/11 (index.tsx, line 255)
[Log] Step function called: isSimulated=false, isProcessing=true, currentLine=2, totalLines=11 (index.tsx, line 144)
[Log] Checking for M30 in line: "G0 X0 Y0 ; RAPID MOVE TO STARTING POINT" (index.tsx, line 174)
[Log] Processing line 3: G0 X0 Y0 ; Rapid move to starting point (index.tsx, line 185)
[Log] Tool position changed: (0, 0, 0) -> (0, 0, null) (index.tsx, line 188)
[Log] Updated tool position state: – {x: 0, y: 0, z: 0} (index.tsx, line 197)
[Log] Tool moved from (0, 0, 0) to (0, 0, null) (index.tsx, line 211)
[Log] Advanced to next line: 3 (index.tsx, line 216)
[Log] Updated path with new point. Path now has 2 points (index.tsx, line 200)
[Log] Updated path with new point. Path now has 2 points (index.tsx, line 200)
[Log] SimulationViewport unmounting (chunk-3CEHBUVK.js, line 16764)
[Log] Cleaning up canvas render loop (SimulationViewport.tsx, line 143)
[Log] SimulationViewport mounting (SimulationViewport.tsx, line 37)
[Debug] SimulationViewport props: – {isRunning: true, currentGCodeFile: "square.nc", simulatedPoints: "2 points", …} (SimulationViewport.tsx, line 38)
{isRunning: true, currentGCodeFile: "square.nc", simulatedPoints: "2 points", currentToolPosition: {x: 0, y: 0, z: 0}, isSimulated: false, …}Object
[Log] Setting up canvas render loop (SimulationViewport.tsx, line 60)
[Log] Current tool position: – {x: 0, y: 0, z: 0} (SimulationViewport.tsx, line 61)
[Log] Simulated points: – "2 points" (SimulationViewport.tsx, line 62)
[Log] Starting continuous render loop - simulation is running (SimulationViewport.tsx, line 137)
[Log] Setting timeout for next line: 500ms (1.0x speed) (index.tsx, line 261)
[Log] Rendering tool at position: (0, 0, 0) (canvas2DRenderer.ts, line 129)
[Log] Continuing simulation after timeout (index.tsx, line 268)
[Log] Run function called, currentLine=3, total=11, speed=1.0x (index.tsx, line 239)
[Log] Processing line 4/11 (index.tsx, line 255)
[Log] Step function called: isSimulated=false, isProcessing=true, currentLine=3, totalLines=11 (index.tsx, line 144)
[Log] Checking for M30 in line: "" (index.tsx, line 174)
[Log] Skipping comment or empty line: "" (index.tsx, line 209)
[Log] Tool moved from (0, 0, 0) to (0, 0, 0) (index.tsx, line 211)
[Log] Advanced to next line: 4 (index.tsx, line 216)
[Log] Setting timeout for next line: 500ms (1.0x speed) (index.tsx, line 261)
[Log] Rendering tool at position: (0, 0, 0) (canvas2DRenderer.ts, line 129)
[Log] Continuing simulation after timeout (index.tsx, line 268)
[Log] Run function called, currentLine=4, total=11, speed=1.0x (index.tsx, line 239)
[Log] Processing line 5/11 (index.tsx, line 255)
[Log] Step function called: isSimulated=false, isProcessing=true, currentLine=4, totalLines=11 (index.tsx, line 144)
[Log] Checking for M30 in line: "G1 X50 Y0 F1000 ; MOVE TO RIGHT (FEED MOVE)" (index.tsx, line 174)
[Log] Processing line 5: G1 X50 Y0 F1000 ; Move to right (feed move) (index.tsx, line 185)
[Log] Tool position changed: (0, 0, 0) -> (50, 0, null) (index.tsx, line 188)
[Log] Updated tool position state: – {x: 50, y: 0, z: 0} (index.tsx, line 197)
[Log] Tool moved from (0, 0, 0) to (50, 0, null) (index.tsx, line 211)
[Log] Advanced to next line: 5 (index.tsx, line 216)
[Log] Updated path with new point. Path now has 3 points (index.tsx, line 200)
[Log] Updated path with new point. Path now has 3 points (index.tsx, line 200)
[Log] SimulationViewport unmounting (chunk-3CEHBUVK.js, line 16764)
[Log] Cleaning up canvas render loop (SimulationViewport.tsx, line 143)
[Log] SimulationViewport mounting (SimulationViewport.tsx, line 37)
[Debug] SimulationViewport props: – {isRunning: true, currentGCodeFile: "square.nc", simulatedPoints: "3 points", …} (SimulationViewport.tsx, line 38)
{isRunning: true, currentGCodeFile: "square.nc", simulatedPoints: "3 points", currentToolPosition: {x: 50, y: 0, z: 0}, isSimulated: false, …}Object
[Log] Setting up canvas render loop (SimulationViewport.tsx, line 60)
[Log] Current tool position: – {x: 50, y: 0, z: 0} (SimulationViewport.tsx, line 61)
[Log] Simulated points: – "3 points" (SimulationViewport.tsx, line 62)
[Log] Starting continuous render loop - simulation is running (SimulationViewport.tsx, line 137)
[Log] Setting timeout for next line: 500ms (1.0x speed) (index.tsx, line 261)
[Log] Continuing simulation after timeout (index.tsx, line 268)
[Log] Run function called, currentLine=5, total=11, speed=1.0x (index.tsx, line 239)
[Log] Processing line 6/11 (index.tsx, line 255)
[Log] Step function called: isSimulated=false, isProcessing=true, currentLine=5, totalLines=11 (index.tsx, line 144)
[Log] Checking for M30 in line: "G1 X50 Y50 ; MOVE UP" (index.tsx, line 174)
[Log] Processing line 6: G1 X50 Y50 ; Move up (index.tsx, line 185)
[Log] Tool position changed: (0, 0, 0) -> (50, 50, null) (index.tsx, line 188)
[Log] Updated tool position state: – {x: 50, y: 50, z: 0} (index.tsx, line 197)
[Log] Tool moved from (0, 0, 0) to (50, 50, null) (index.tsx, line 211)
[Log] Advanced to next line: 6 (index.tsx, line 216)
[Log] Updated path with new point. Path now has 4 points (index.tsx, line 200)
[Log] Updated path with new point. Path now has 4 points (index.tsx, line 200)
[Log] SimulationViewport unmounting (chunk-3CEHBUVK.js, line 16764)
[Log] Cleaning up canvas render loop (SimulationViewport.tsx, line 143)
[Log] SimulationViewport mounting (SimulationViewport.tsx, line 37)
[Debug] SimulationViewport props: – {isRunning: true, currentGCodeFile: "square.nc", simulatedPoints: "4 points", …} (SimulationViewport.tsx, line 38)
{isRunning: true, currentGCodeFile: "square.nc", simulatedPoints: "4 points", currentToolPosition: {x: 50, y: 50, z: 0}, isSimulated: false, …}Object
[Log] Setting up canvas render loop (SimulationViewport.tsx, line 60)
[Log] Current tool position: – {x: 50, y: 50, z: 0} (SimulationViewport.tsx, line 61)
[Log] Simulated points: – "4 points" (SimulationViewport.tsx, line 62)
[Log] Starting continuous render loop - simulation is running (SimulationViewport.tsx, line 137)
[Log] Setting timeout for next line: 500ms (1.0x speed) (index.tsx, line 261)
[Log] Continuing simulation after timeout (index.tsx, line 268)
[Log] Run function called, currentLine=6, total=11, speed=1.0x (index.tsx, line 239)
[Log] Processing line 7/11 (index.tsx, line 255)
[Log] Step function called: isSimulated=false, isProcessing=true, currentLine=6, totalLines=11 (index.tsx, line 144)
[Log] Checking for M30 in line: "G1 X0 Y50 ; MOVE LEFT" (index.tsx, line 174)
[Log] Processing line 7: G1 X0 Y50 ; Move left (index.tsx, line 185)
[Log] Tool position changed: (0, 0, 0) -> (0, 50, null) (index.tsx, line 188)
[Log] Updated tool position state: – {x: 0, y: 50, z: 0} (index.tsx, line 197)
[Log] Tool moved from (0, 0, 0) to (0, 50, null) (index.tsx, line 211)
[Log] Advanced to next line: 7 (index.tsx, line 216)
[Log] Updated path with new point. Path now has 5 points (index.tsx, line 200)
[Log] Updated path with new point. Path now has 5 points (index.tsx, line 200)
[Log] SimulationViewport unmounting (chunk-3CEHBUVK.js, line 16764)
[Log] Cleaning up canvas render loop (SimulationViewport.tsx, line 143)
[Log] SimulationViewport mounting (SimulationViewport.tsx, line 37)
[Debug] SimulationViewport props: – {isRunning: true, currentGCodeFile: "square.nc", simulatedPoints: "5 points", …} (SimulationViewport.tsx, line 38)
{isRunning: true, currentGCodeFile: "square.nc", simulatedPoints: "5 points", currentToolPosition: {x: 0, y: 50, z: 0}, isSimulated: false, …}Object
[Log] Setting up canvas render loop (SimulationViewport.tsx, line 60)
[Log] Current tool position: – {x: 0, y: 50, z: 0} (SimulationViewport.tsx, line 61)
[Log] Simulated points: – "5 points" (SimulationViewport.tsx, line 62)
[Log] Starting continuous render loop - simulation is running (SimulationViewport.tsx, line 137)
[Log] Rendering tool at position: (0, 50, 0) (canvas2DRenderer.ts, line 129)
[Log] Setting timeout for next line: 500ms (1.0x speed) (index.tsx, line 261)
[Log] Rendering tool at position: (0, 50, 0) (canvas2DRenderer.ts, line 129)
[Log] Continuing simulation after timeout (index.tsx, line 268)
[Log] Run function called, currentLine=7, total=11, speed=1.0x (index.tsx, line 239)
[Log] Processing line 8/11 (index.tsx, line 255)
[Log] Step function called: isSimulated=false, isProcessing=true, currentLine=7, totalLines=11 (index.tsx, line 144)
[Log] Checking for M30 in line: "G1 X0 Y0 ; MOVE DOWN - BACK TO START" (index.tsx, line 174)
[Log] Processing line 8: G1 X0 Y0 ; Move down - back to start (index.tsx, line 185)
[Log] Tool position changed: (0, 0, 0) -> (0, 0, null) (index.tsx, line 188)
[Log] Updated tool position state: – {x: 0, y: 0, z: 0} (index.tsx, line 197)
[Log] Tool moved from (0, 0, 0) to (0, 0, null) (index.tsx, line 211)
[Log] Advanced to next line: 8 (index.tsx, line 216)
[Log] Updated path with new point. Path now has 6 points (index.tsx, line 200)
[Log] Updated path with new point. Path now has 6 points (index.tsx, line 200)
[Log] SimulationViewport unmounting (chunk-3CEHBUVK.js, line 16764)
[Log] Cleaning up canvas render loop (SimulationViewport.tsx, line 143)
[Log] SimulationViewport mounting (SimulationViewport.tsx, line 37)
[Debug] SimulationViewport props: – {isRunning: true, currentGCodeFile: "square.nc", simulatedPoints: "6 points", …} (SimulationViewport.tsx, line 38)
{isRunning: true, currentGCodeFile: "square.nc", simulatedPoints: "6 points", currentToolPosition: {x: 0, y: 0, z: 0}, isSimulated: false, …}Object
[Log] Setting up canvas render loop (SimulationViewport.tsx, line 60)
[Log] Current tool position: – {x: 0, y: 0, z: 0} (SimulationViewport.tsx, line 61)
[Log] Simulated points: – "6 points" (SimulationViewport.tsx, line 62)
[Log] Starting continuous render loop - simulation is running (SimulationViewport.tsx, line 137)
[Log] Setting timeout for next line: 500ms (1.0x speed) (index.tsx, line 261)
[Log] Rendering tool at position: (0, 0, 0) (canvas2DRenderer.ts, line 129, x2)
[Log] Continuing simulation after timeout (index.tsx, line 268)
[Log] Run function called, currentLine=8, total=11, speed=1.0x (index.tsx, line 239)
[Log] Processing line 9/11 (index.tsx, line 255)
[Log] Step function called: isSimulated=false, isProcessing=true, currentLine=8, totalLines=11 (index.tsx, line 144)
[Log] Checking for M30 in line: "" (index.tsx, line 174)
[Log] Skipping comment or empty line: "" (index.tsx, line 209)
[Log] Tool moved from (0, 0, 0) to (0, 0, 0) (index.tsx, line 211)
[Log] Advanced to next line: 9 (index.tsx, line 216)
[Log] Setting timeout for next line: 500ms (1.0x speed) (index.tsx, line 261)
[Log] Continuing simulation after timeout (index.tsx, line 268)
[Log] Run function called, currentLine=9, total=11, speed=1.0x (index.tsx, line 239)
[Log] Processing line 10/11 (index.tsx, line 255)
[Log] Step function called: isSimulated=false, isProcessing=true, currentLine=9, totalLines=11 (index.tsx, line 144)
[Log] Checking for M30 in line: "M30 ; END OF PROGRAM" (index.tsx, line 174)
[Log] M30 command detected - ending program (index.tsx, line 176)
[Log] Run loop stopping: isRunning=true, isPaused=false, isSimulated=false, currentLine=11 (index.tsx, line 279)
[Log] SimulationViewport unmounting (chunk-3CEHBUVK.js, line 16764)
[Log] Cleaning up canvas render loop (SimulationViewport.tsx, line 143)
[Log] SimulationViewport mounting (SimulationViewport.tsx, line 37)
[Debug] SimulationViewport props: – {isRunning: false, currentGCodeFile: "square.nc", simulatedPoints: "6 points", …} (SimulationViewport.tsx, line 38)
{isRunning: false, currentGCodeFile: "square.nc", simulatedPoints: "6 points", currentToolPosition: {x: 0, y: 0, z: 0}, isSimulated: true, …}Object
[Log] Setting up canvas render loop (SimulationViewport.tsx, line 60)
[Log] Current tool position: – {x: 0, y: 0, z: 0} (SimulationViewport.tsx, line 61)
[Log] Simulated points: – "6 points" (SimulationViewport.tsx, line 62)
[Log] Rendering single frame - simulation not running (SimulationViewport.tsx, line 140)
[Log] Run effect triggered: isRunning=false, isPaused=false, isSimulated=true, speed=1 (index.tsx, line 409)
[Log] Cleaning up canvas render loop (SimulationViewport.tsx, line 143)
[Log] Setting up canvas render loop (SimulationViewport.tsx, line 60)
[Log] Current tool position: – {x: 0, y: 0, z: 0} (SimulationViewport.tsx, line 61)
[Log] Simulated points: – "6 points" (SimulationViewport.tsx, line 62)
[Log] Rendering single frame - simulation not running (SimulationViewport.tsx, line 140)
