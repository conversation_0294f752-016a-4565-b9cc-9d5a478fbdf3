#!/bin/bash

# Test Enhanced Clang-Tidy Configuration
# This script tests the new modernize-* and cppcoreguidelines-* checks
# on a subset of the Vizion3D codebase to evaluate their effectiveness.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CLANG_TIDY_CONFIG=".clang-tidy"
BUILD_DIR="build"
RESULTS_DIR="clang-tidy-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Test files - start with a representative subset
TEST_FILES=(
    "src/engine/simulation_engine.cpp"
    "src/engine/gcode_interpreter.cpp"
    "src/ui/simulation_view.cpp"
    "src/ui/mainwindow.cpp"
    "src/utils/result.cpp"
    "include/utils/result.h"
    "include/engine/simulation_engine.h"
)

echo -e "${BLUE}=== Enhanced Clang-Tidy Configuration Test ===${NC}"
echo "Testing modernize-* and cppcoreguidelines-* checks"
echo "Timestamp: $TIMESTAMP"
echo ""

# Check prerequisites
echo -e "${YELLOW}Checking prerequisites...${NC}"

if ! command -v clang-tidy &> /dev/null; then
    echo -e "${RED}Error: clang-tidy not found${NC}"
    exit 1
fi

if [ ! -f "$CLANG_TIDY_CONFIG" ]; then
    echo -e "${RED}Error: $CLANG_TIDY_CONFIG not found${NC}"
    exit 1
fi

if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${RED}Error: Build directory $BUILD_DIR not found${NC}"
    echo "Please run 'make' or 'cmake --build build' first"
    exit 1
fi

if [ ! -f "$BUILD_DIR/compile_commands.json" ]; then
    echo -e "${RED}Error: compile_commands.json not found in $BUILD_DIR${NC}"
    echo "Please ensure CMAKE_EXPORT_COMPILE_COMMANDS is ON"
    exit 1
fi

echo -e "${GREEN}Prerequisites OK${NC}"
echo ""

# Create results directory
mkdir -p "$RESULTS_DIR"

# Function to run clang-tidy on a file and categorize results
analyze_file() {
    local file="$1"
    local output_file="$RESULTS_DIR/$(basename "$file" .cpp)_$(basename "$file" .h)_$TIMESTAMP.txt"

    echo -e "${BLUE}Analyzing: $file${NC}" >&2

    if [ ! -f "$file" ]; then
        echo -e "${RED}  File not found: $file${NC}" >&2
        return 1
    fi

    # Run clang-tidy and capture output
    clang-tidy \
        --config-file="$CLANG_TIDY_CONFIG" \
        -p "$BUILD_DIR" \
        "$file" \
        > "$output_file" 2>&1 || true

    # Analyze results - use more specific patterns to avoid false matches
    local total_warnings=$(grep -c ": warning:" "$output_file" 2>/dev/null | head -1 || echo "0")
    local modernize_warnings=$(grep -c "\[modernize-" "$output_file" 2>/dev/null | head -1 || echo "0")
    local guidelines_warnings=$(grep -c "\[cppcoreguidelines-" "$output_file" 2>/dev/null | head -1 || echo "0")

    # Ensure we have clean numbers
    total_warnings=${total_warnings//[^0-9]/}
    modernize_warnings=${modernize_warnings//[^0-9]/}
    guidelines_warnings=${guidelines_warnings//[^0-9]/}

    # Default to 0 if empty
    total_warnings=${total_warnings:-0}
    modernize_warnings=${modernize_warnings:-0}
    guidelines_warnings=${guidelines_warnings:-0}



    # Note: modernize and guidelines warnings are subsets of total warnings, not additive
    local other_warnings=$((total_warnings - modernize_warnings - guidelines_warnings))
    # Ensure other_warnings is not negative (in case of overlap)
    if [ $other_warnings -lt 0 ]; then
        other_warnings=0
    fi

    echo "  Total warnings: $total_warnings" >&2
    echo "  Modernize warnings: $modernize_warnings" >&2
    echo "  Core Guidelines warnings: $guidelines_warnings" >&2
    echo "  Other warnings: $other_warnings" >&2
    echo "  Results saved to: $output_file" >&2
    echo "" >&2

    # Return counts for summary (only this goes to stdout)
    echo "$total_warnings $modernize_warnings $guidelines_warnings $other_warnings"
}

# Analyze each test file
echo -e "${YELLOW}Analyzing test files...${NC}"
echo ""

total_files=0
total_warnings=0
total_modernize=0
total_guidelines=0
total_other=0

for file in "${TEST_FILES[@]}"; do
    if [ -f "$file" ]; then
        result=$(analyze_file "$file")
        if [ $? -eq 0 ]; then
            read -r file_total file_modernize file_guidelines file_other <<< "$result"
            total_files=$((total_files + 1))
            total_warnings=$((total_warnings + file_total))
            total_modernize=$((total_modernize + file_modernize))
            total_guidelines=$((total_guidelines + file_guidelines))
            total_other=$((total_other + file_other))
        fi
    else
        echo -e "${YELLOW}Skipping missing file: $file${NC}"
    fi
done

# Generate summary
echo -e "${GREEN}=== Analysis Summary ===${NC}"
echo "Files analyzed: $total_files"
echo "Total warnings: $total_warnings"
echo "Modernize warnings: $total_modernize"
echo "Core Guidelines warnings: $total_guidelines"
echo "Other warnings: $total_other"
echo ""

# Generate detailed report
SUMMARY_FILE="$RESULTS_DIR/summary_$TIMESTAMP.txt"
echo "Enhanced Clang-Tidy Analysis Summary" > "$SUMMARY_FILE"
echo "Generated: $(date)" >> "$SUMMARY_FILE"
echo "Configuration: $CLANG_TIDY_CONFIG" >> "$SUMMARY_FILE"
echo "" >> "$SUMMARY_FILE"
echo "Files analyzed: $total_files" >> "$SUMMARY_FILE"
echo "Total warnings: $total_warnings" >> "$SUMMARY_FILE"
echo "Modernize warnings: $total_modernize" >> "$SUMMARY_FILE"
echo "Core Guidelines warnings: $total_guidelines" >> "$SUMMARY_FILE"
echo "Other warnings: $total_other" >> "$SUMMARY_FILE"
echo "" >> "$SUMMARY_FILE"

# Extract most common warnings
echo "Most common modernize warnings:" >> "$SUMMARY_FILE"
grep "modernize-" "$RESULTS_DIR"/*_$TIMESTAMP.txt 2>/dev/null | \
    sed 's/.*\(modernize-[^:]*\).*/\1/' | \
    sort | uniq -c | sort -nr | head -10 >> "$SUMMARY_FILE" 2>/dev/null || echo "None found" >> "$SUMMARY_FILE"

echo "" >> "$SUMMARY_FILE"
echo "Most common Core Guidelines warnings:" >> "$SUMMARY_FILE"
grep "cppcoreguidelines-" "$RESULTS_DIR"/*_$TIMESTAMP.txt 2>/dev/null | \
    sed 's/.*\(cppcoreguidelines-[^:]*\).*/\1/' | \
    sort | uniq -c | sort -nr | head -10 >> "$SUMMARY_FILE" 2>/dev/null || echo "None found" >> "$SUMMARY_FILE"

echo -e "${GREEN}Summary saved to: $SUMMARY_FILE${NC}"
echo ""

# Recommendations
echo -e "${YELLOW}=== Recommendations ===${NC}"
if [ $total_modernize -gt 0 ]; then
    echo "• Found $total_modernize modernize warnings - consider addressing high-impact ones"
fi
if [ $total_guidelines -gt 0 ]; then
    echo "• Found $total_guidelines Core Guidelines warnings - review for safety improvements"
fi
if [ $total_warnings -gt 50 ]; then
    echo "• High warning count ($total_warnings) - consider excluding more aggressive checks"
elif [ $total_warnings -lt 10 ]; then
    echo "• Low warning count ($total_warnings) - configuration may be too conservative"
else
    echo "• Warning count ($total_warnings) appears reasonable for gradual adoption"
fi

echo ""
echo -e "${BLUE}Test completed. Review results in $RESULTS_DIR/${NC}"
