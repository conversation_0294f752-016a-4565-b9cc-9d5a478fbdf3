#!/bin/bash

# Comprehensive clang-tidy analysis script for Vizion3D
# This script runs clang-tidy on all source files and provides a summary

set -e

echo "🔍 Running comprehensive clang-tidy analysis on Vizion3D codebase..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_FILES=0
CLEAN_FILES=0
FILES_WITH_WARNINGS=0
TOTAL_WARNINGS=0

# Create temporary file for results
TEMP_RESULTS=$(mktemp)

echo "📁 Finding C++ source files..."
FILES=$(find src include -name "*.cpp" -o -name "*.h" -o -name "*.hpp" | sort)
TOTAL_FILES=$(echo "$FILES" | wc -l)

echo "📊 Found $TOTAL_FILES files to analyze"
echo ""

# Process each file
while IFS= read -r file; do
    if [[ -f "$file" ]]; then
        echo -n "🔍 Analyzing: $file ... "

        # Run clang-tidy and capture output
        RESULT=$(clang-tidy -p build "$file" 2>&1 | grep -E "warning:|error:" | grep -v "warnings generated" | grep -v "Suppressed.*warnings" || true)

        if [[ -z "$RESULT" ]]; then
            echo -e "${GREEN}✅ CLEAN${NC}"
            ((CLEAN_FILES++))
        else
            echo -e "${YELLOW}⚠️  HAS WARNINGS${NC}"
            ((FILES_WITH_WARNINGS++))

            # Count warnings for this file
            WARNING_COUNT=$(echo "$RESULT" | wc -l)
            ((TOTAL_WARNINGS += WARNING_COUNT))

            # Store results for detailed report
            echo "=== $file ===" >> "$TEMP_RESULTS"
            echo "$RESULT" >> "$TEMP_RESULTS"
            echo "" >> "$TEMP_RESULTS"
        fi
    fi
done <<< "$FILES"

echo ""
echo "📈 ANALYSIS SUMMARY"
echo "=================================================="
echo -e "📁 Total files analyzed: ${BLUE}$TOTAL_FILES${NC}"
echo -e "✅ Clean files: ${GREEN}$CLEAN_FILES${NC}"
echo -e "⚠️  Files with warnings: ${YELLOW}$FILES_WITH_WARNINGS${NC}"
echo -e "🚨 Total warnings: ${RED}$TOTAL_WARNINGS${NC}"

# Calculate percentage
if [[ $TOTAL_FILES -gt 0 ]]; then
    CLEAN_PERCENTAGE=$((CLEAN_FILES * 100 / TOTAL_FILES))
    echo -e "📊 Clean percentage: ${GREEN}$CLEAN_PERCENTAGE%${NC}"
fi

echo ""

# Show detailed results if there are warnings
if [[ $FILES_WITH_WARNINGS -gt 0 ]]; then
    echo "📋 DETAILED WARNINGS REPORT"
    echo "=================================================="
    cat "$TEMP_RESULTS"

    echo ""
    echo "💡 RECOMMENDATIONS"
    echo "=================================================="
    echo "• Review the warnings above to determine if they are:"
    echo "  - Real issues that need fixing"
    echo "  - False positives that should be disabled in .clang-tidy"
    echo "  - Style preferences that can be ignored"
    echo ""
    echo "• To disable specific warnings, add them to the .clang-tidy file"
    echo "• To fix issues automatically, consider creating fix scripts"
else
    echo -e "${GREEN}🎉 CONGRATULATIONS!${NC}"
    echo "All files are clean! No clang-tidy warnings found."
fi

# Cleanup
rm -f "$TEMP_RESULTS"

echo ""
echo "✨ Analysis complete!"

# Exit with appropriate code
if [[ $TOTAL_WARNINGS -eq 0 ]]; then
    exit 0
else
    exit 1
fi
