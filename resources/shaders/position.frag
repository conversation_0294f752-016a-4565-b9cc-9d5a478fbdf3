#version 410 core
// OpenGL 4.1 Core Profile - Vizion3D standard

in vec3 fragNormal;
in vec3 fragPosition;
in vec4 fragColor;

out vec4 outColor;

uniform vec3 lightPosition;

void main() {
    // Ambient light
    float ambientStrength = 0.3;
    vec3 ambient = ambientStrength * vec3(1.0, 1.0, 1.0);

    // Diffuse light
    vec3 norm = normalize(fragNormal);
    vec3 lightDir = normalize(lightPosition - fragPosition);
    float diff = max(dot(norm, lightDir), 0.0);
    vec3 diffuse = diff * vec3(1.0, 1.0, 1.0);

    // Specular light
    float specularStrength = 0.5;
    vec3 viewDir = normalize(-fragPosition);
    vec3 reflectDir = reflect(-lightDir, norm);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32);
    vec3 specular = specularStrength * spec * vec3(1.0, 1.0, 1.0);

    // Final color
    vec3 result = (ambient + diffuse + specular) * fragColor.rgb;
    outColor = vec4(result, fragColor.a);
}
