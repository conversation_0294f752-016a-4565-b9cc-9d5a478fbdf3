#version 410 core
// OpenGL 4.1 Core Profile - Vizion3D standard

layout(location = 0) in vec3 position;
layout(location = 1) in vec3 normal;

uniform mat4 modelViewProjection;
uniform mat4 modelView;
uniform vec4 color;

out vec3 fragNormal;
out vec3 fragPosition;
out vec4 fragColor;

void main() {
    gl_Position = modelViewProjection * vec4(position, 1.0);
    fragNormal = mat3(modelView) * normal;
    fragPosition = vec3(modelView * vec4(position, 1.0));
    fragColor = color;
}
