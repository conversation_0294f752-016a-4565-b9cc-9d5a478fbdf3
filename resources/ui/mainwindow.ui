<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>768</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Vizion3D - CNC Simulation</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QSplitter" name="splitter">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QWidget" name="leftPanel" native="true">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>1</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <widget class="QLabel" name="label">
          <property name="text">
           <string>G-code Editor (Placeholder)</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPlainTextEdit" name="gCodeEditor"/>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="rightPanel" native="true">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
         <horstretch>2</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <item>
         <widget class="QLabel" name="label_2">
          <property name="text">
           <string>3D Viewport (Placeholder)</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="viewportContainer" native="true">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color: #2a2a2a;</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QFrame" name="controlsFrame">
          <property name="frameShape">
           <enum>QFrame::StyledPanel</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <item>
            <widget class="QPushButton" name="startButton">
             <property name="text">
              <string>Start</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pauseButton">
             <property name="text">
              <string>Pause</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="stopButton">
             <property name="text">
              <string>Stop</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="stepButton">
             <property name="text">
              <string>Step</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="label_3">
             <property name="text">
              <string>Speed:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QSlider" name="speedSlider">
             <property name="minimum">
              <number>1</number>
             </property>
             <property name="maximum">
              <number>10</number>
             </property>
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1024</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>File</string>
    </property>
    <addaction name="actionNew"/>
    <addaction name="actionOpen"/>
    <addaction name="actionSave"/>
    <addaction name="actionSaveAs"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuEdit">
    <property name="title">
     <string>Edit</string>
    </property>
    <addaction name="actionUndo"/>
    <addaction name="actionRedo"/>
    <addaction name="separator"/>
    <addaction name="actionCut"/>
    <addaction name="actionCopy"/>
    <addaction name="actionPaste"/>
   </widget>
   <widget class="QMenu" name="menuView">
    <property name="title">
     <string>View</string>
    </property>
    <addaction name="actionTopView"/>
    <addaction name="actionFrontView"/>
    <addaction name="actionSideView"/>
    <addaction name="actionIsometricView"/>
    <addaction name="separator"/>
    <addaction name="actionZoomIn"/>
    <addaction name="actionZoomOut"/>
    <addaction name="actionZoomFit"/>
   </widget>
   <widget class="QMenu" name="menuSimulation">
    <property name="title">
     <string>Simulation</string>
    </property>
    <addaction name="actionStart"/>
    <addaction name="actionPause"/>
    <addaction name="actionStop"/>
    <addaction name="actionStep"/>
    <addaction name="separator"/>
    <addaction name="actionSettings"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>Help</string>
    </property>
    <addaction name="actionAbout"/>
    <addaction name="actionDocumentation"/>
   </widget>
   <widget class="QMenu" name="menuDebug">
    <property name="title">
     <string>Debug</string>
    </property>
    <addaction name="actionDebugConsole"/>
    <addaction name="actionLoggingSettings"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuEdit"/>
   <addaction name="menuView"/>
   <addaction name="menuSimulation"/>
   <addaction name="menuDebug"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionNew"/>
   <addaction name="actionOpen"/>
   <addaction name="actionSave"/>
   <addaction name="separator"/>
   <addaction name="actionStart"/>
   <addaction name="actionPause"/>
   <addaction name="actionStop"/>
   <addaction name="actionStep"/>
   <addaction name="separator"/>
   <addaction name="actionSettings"/>
   <addaction name="separator"/>
   <addaction name="actionDebugConsole"/>
  </widget>
  <action name="actionNew">
   <property name="text">
    <string>New</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+N</string>
   </property>
  </action>
  <action name="actionOpen">
   <property name="text">
    <string>Open</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+O</string>
   </property>
  </action>
  <action name="actionSave">
   <property name="text">
    <string>Save</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+S</string>
   </property>
  </action>
  <action name="actionSaveAs">
   <property name="text">
    <string>Save As...</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Shift+S</string>
   </property>
  </action>
  <action name="actionExit">
   <property name="text">
    <string>Exit</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Q</string>
   </property>
  </action>
  <action name="actionUndo">
   <property name="text">
    <string>Undo</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Z</string>
   </property>
  </action>
  <action name="actionRedo">
   <property name="text">
    <string>Redo</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Shift+Z</string>
   </property>
  </action>
  <action name="actionCut">
   <property name="text">
    <string>Cut</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+X</string>
   </property>
  </action>
  <action name="actionCopy">
   <property name="text">
    <string>Copy</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+C</string>
   </property>
  </action>
  <action name="actionPaste">
   <property name="text">
    <string>Paste</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+V</string>
   </property>
  </action>
  <action name="actionTopView">
   <property name="text">
    <string>Top View</string>
   </property>
   <property name="shortcut">
    <string>1</string>
   </property>
  </action>
  <action name="actionFrontView">
   <property name="text">
    <string>Front View</string>
   </property>
   <property name="shortcut">
    <string>2</string>
   </property>
  </action>
  <action name="actionSideView">
   <property name="text">
    <string>Side View</string>
   </property>
   <property name="shortcut">
    <string>3</string>
   </property>
  </action>
  <action name="actionIsometricView">
   <property name="text">
    <string>Isometric View</string>
   </property>
   <property name="shortcut">
    <string>4</string>
   </property>
  </action>
  <action name="actionZoomIn">
   <property name="text">
    <string>Zoom In</string>
   </property>
   <property name="shortcut">
    <string>Ctrl++</string>
   </property>
  </action>
  <action name="actionZoomOut">
   <property name="text">
    <string>Zoom Out</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+-</string>
   </property>
  </action>
  <action name="actionZoomFit">
   <property name="text">
    <string>Zoom Fit</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+0</string>
   </property>
  </action>
  <action name="actionStart">
   <property name="text">
    <string>Start</string>
   </property>
   <property name="shortcut">
    <string>F5</string>
   </property>
  </action>
  <action name="actionPause">
   <property name="text">
    <string>Pause</string>
   </property>
   <property name="shortcut">
    <string>F6</string>
   </property>
  </action>
  <action name="actionStop">
   <property name="text">
    <string>Stop</string>
   </property>
   <property name="shortcut">
    <string>F7</string>
   </property>
  </action>
  <action name="actionStep">
   <property name="text">
    <string>Step</string>
   </property>
   <property name="shortcut">
    <string>F8</string>
   </property>
  </action>
  <action name="actionSettings">
   <property name="text">
    <string>Settings</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>About</string>
   </property>
  </action>
  <action name="actionDocumentation">
   <property name="text">
    <string>Documentation</string>
   </property>
   <property name="shortcut">
    <string>F1</string>
   </property>
  </action>
  <action name="actionDebugConsole">
   <property name="text">
    <string>Debug Console</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+D</string>
   </property>
  </action>
  <action name="actionLoggingSettings">
   <property name="text">
    <string>Logging Settings</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+L</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
