#!/bin/bash

# Apply Modernization Fixes Script
# This script helps apply common modernization fixes identified by the enhanced clang-tidy configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BUILD_DIR="build"
BACKUP_DIR="modernization-backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo -e "${BLUE}=== Vizion3D Modernization Fix Application ===${NC}"
echo "This script helps apply common modernization fixes"
echo "Timestamp: $TIMESTAMP"
echo ""

# Check prerequisites
echo -e "${YELLOW}Checking prerequisites...${NC}"

if ! command -v clang-tidy &> /dev/null; then
    echo -e "${RED}Error: clang-tidy not found${NC}"
    exit 1
fi

if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${RED}Error: Build directory $BUILD_DIR not found${NC}"
    echo "Please run 'make' or 'cmake --build build' first"
    exit 1
fi

if [ ! -f "$BUILD_DIR/compile_commands.json" ]; then
    echo -e "${RED}Error: compile_commands.json not found in $BUILD_DIR${NC}"
    exit 1
fi

echo -e "${GREEN}Prerequisites OK${NC}"
echo ""

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Function to create backup
create_backup() {
    local file="$1"
    local backup_file="$BACKUP_DIR/$(basename "$file")_$TIMESTAMP.backup"
    cp "$file" "$backup_file"
    echo "  Backup created: $backup_file"
}

# Function to apply fixes to a file
apply_fixes() {
    local file="$1"
    local fix_types="$2"

    echo -e "${BLUE}Processing: $file${NC}"

    if [ ! -f "$file" ]; then
        echo -e "${RED}  File not found: $file${NC}"
        return 1
    fi

    # Create backup
    create_backup "$file"

    # Apply fixes
    echo "  Applying fixes: $fix_types"
    clang-tidy \
        --config-file=".clang-tidy" \
        -p "$BUILD_DIR" \
        --fix \
        --checks="$fix_types" \
        "$file" || true

    echo -e "${GREEN}  Fixes applied to: $file${NC}"
    echo ""
}

# Phase 1: Quick wins - safe and high-impact fixes
apply_phase1_fixes() {
    echo -e "${YELLOW}=== Phase 1: Quick Wins (Safe Fixes) ===${NC}"
    echo "Applying variable initialization and member initialization fixes"
    echo ""

    local phase1_checks="-*,cppcoreguidelines-init-variables,modernize-use-default-member-init,modernize-use-override"

    # Apply to engine files first
    for file in src/engine/*.cpp include/engine/*.h; do
        if [ -f "$file" ]; then
            apply_fixes "$file" "$phase1_checks"
        fi
    done

    # Apply to utils files
    for file in src/utils/*.cpp include/utils/*.h; do
        if [ -f "$file" ]; then
            apply_fixes "$file" "$phase1_checks"
        fi
    done
}

# Phase 2: Modernization - style and readability improvements
apply_phase2_fixes() {
    echo -e "${YELLOW}=== Phase 2: Modernization (Style Fixes) ===${NC}"
    echo "Applying auto usage and braced initialization fixes"
    echo ""

    local phase2_checks="-*,modernize-use-auto,modernize-return-braced-init-list,modernize-use-equals-default"

    # Apply to engine files
    for file in src/engine/*.cpp include/engine/*.h; do
        if [ -f "$file" ]; then
            apply_fixes "$file" "$phase2_checks"
        fi
    done
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [phase1|phase2|file <filename>|help]"
    echo ""
    echo "Options:"
    echo "  phase1     Apply Phase 1 fixes (variable init, member init, override)"
    echo "  phase2     Apply Phase 2 fixes (auto usage, braced init, = default)"
    echo "  file <f>   Apply Phase 1 fixes to specific file"
    echo "  help       Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 phase1                           # Apply safe fixes to all files"
    echo "  $0 file src/engine/simulation_engine.cpp  # Fix specific file"
    echo ""
}

# Function to apply fixes to specific file
apply_file_fixes() {
    local file="$1"

    if [ ! -f "$file" ]; then
        echo -e "${RED}Error: File not found: $file${NC}"
        exit 1
    fi

    echo -e "${YELLOW}=== Applying fixes to specific file ===${NC}"
    echo "File: $file"
    echo ""

    local safe_checks="-*,cppcoreguidelines-init-variables,modernize-use-default-member-init,modernize-use-override,modernize-use-equals-default"
    apply_fixes "$file" "$safe_checks"
}

# Function to verify compilation after fixes
verify_compilation() {
    echo -e "${YELLOW}Verifying compilation...${NC}"

    if make -C build -j$(nproc) > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Compilation successful${NC}"
        return 0
    else
        echo -e "${RED}✗ Compilation failed${NC}"
        echo "Please review the changes and fix any compilation errors"
        return 1
    fi
}

# Main script logic
case "${1:-help}" in
    "phase1")
        apply_phase1_fixes
        echo -e "${YELLOW}Verifying changes...${NC}"
        verify_compilation
        ;;
    "phase2")
        apply_phase2_fixes
        echo -e "${YELLOW}Verifying changes...${NC}"
        verify_compilation
        ;;
    "file")
        if [ -z "$2" ]; then
            echo -e "${RED}Error: Please specify a file${NC}"
            show_usage
            exit 1
        fi
        apply_file_fixes "$2"
        echo -e "${YELLOW}Verifying changes...${NC}"
        verify_compilation
        ;;
    "help"|*)
        show_usage
        ;;
esac

echo ""
echo -e "${GREEN}=== Modernization Complete ===${NC}"
echo "Backups saved in: $BACKUP_DIR"
echo ""
echo "Next steps:"
echo "1. Review the changes with 'git diff'"
echo "2. Test the application thoroughly"
echo "3. Commit the changes if satisfied"
echo "4. Consider running Phase 2 if you only ran Phase 1"
echo ""
echo "To restore from backup if needed:"
echo "  cp $BACKUP_DIR/<file>_$TIMESTAMP.backup <original-file>"
