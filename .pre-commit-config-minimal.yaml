repos:
  - repo: local
    hooks:
      - id: vizion3d-logging-compliance
        name: Vizion3D Logging Compliance with Auto-fix
        entry: scripts/pre-commit/logging-precommit-hook.sh
        language: script
        pass_filenames: false
        stages: [commit]
        always_run: true

default_stages: [commit]
exclude: |
  (?x)^(
    build/.*|
    \.git/.*|
    test_opengl.*|
    .*\.autogen/.*
  )$
