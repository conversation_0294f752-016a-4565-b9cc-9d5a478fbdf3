# Vizion3D Qt Development Environment Setup

This document provides instructions for setting up the development environment for the Vizion3D Qt application.

## Prerequisites

The following software is required to build and run the Vizion3D Qt application:

- **Qt 6.2+**: The Qt framework and development tools
- **C++17 compatible compiler**: GCC 7+, Clang 5+, or MSVC 2019+
- **CMake 3.16+**: Build system generator
- **OpenCASCADE 7.8.0**: For 3D geometry processing (optional for initial development)
- **Git**: For version control

## Platform-Specific Setup

### Windows

1. **Install Qt 6.2+**
   - Download and install Qt from the [Qt website](https://www.qt.io/download)
   - Select Qt 6.2.0 or later during installation
   - Make sure to include the following components:
     - MSVC 2019 64-bit
     - Qt WebEngine
     - Qt Charts
     - Qt OpenGL

2. **Install Visual Studio 2019 or 2022**
   - Download and install Visual Studio from the [Visual Studio website](https://visualstudio.microsoft.com/downloads/)
   - Select the "Desktop development with C++" workload during installation

3. **Install CMake**
   - Download and install CMake from the [CMake website](https://cmake.org/download/)
   - Add CMake to the system PATH during installation

4. **Install OpenCASCADE 7.8.0 (Optional)**
   - Download and install OpenCASCADE 7.8.0 from the [OpenCASCADE website](https://www.opencascade.com/content/latest-release)
   - Alternatively, install using vcpkg:
     ```
     vcpkg install opencascade:x64-windows --version=7.8.0
     ```

### macOS

1. **Install Xcode**
   - Install Xcode from the App Store
   - Install the Xcode command-line tools:
     ```
     xcode-select --install
     ```

2. **Install Homebrew**
   - Install Homebrew by following the instructions on the [Homebrew website](https://brew.sh/)

3. **Install Qt 6.2+**
   - Install Qt using Homebrew:
     ```
     brew install qt@6
     ```
   - Add Qt to your PATH:
     ```
     echo 'export PATH="/usr/local/opt/qt@6/bin:$PATH"' >> ~/.zshrc
     ```
     or
     ```
     echo 'export PATH="/usr/local/opt/qt@6/bin:$PATH"' >> ~/.bash_profile
     ```

4. **Install CMake**
   - Install CMake using Homebrew:
     ```
     brew install cmake
     ```

5. **Install OpenCASCADE 7.8.0 (Optional)**
   - Install OpenCASCADE 7.8.0 using Homebrew:
     ```
     brew install opencascade@7.8.0
     ```
   - If the specific version is not available in Homebrew, you may need to download and build from source:
     ```
     # Download OpenCASCADE 7.8.0 source from the official website
     # Extract and build following the instructions in the documentation
     ```

### Linux (Ubuntu/Debian)

1. **Install Qt 6.2+**
   - Add the Qt repository:
     ```
     sudo add-apt-repository ppa:beineri/opt-qt-6.2.0-focal
     sudo apt-get update
     ```
   - Install Qt packages:
     ```
     sudo apt-get install qt62-full
     ```
   - Set up the environment:
     ```
     source /opt/qt62/bin/qt62-env.sh
     ```

2. **Install build tools**
   - Install GCC, CMake, and other build tools:
     ```
     sudo apt-get install build-essential cmake git
     ```

3. **Install OpenCASCADE 7.8.0 (Optional)**
   - For Ubuntu/Debian, you may need to build from source to get version 7.8.0 specifically:
     ```
     # Download OpenCASCADE 7.8.0 source from the official website
     wget https://dev.opencascade.org/system/files/occt-7.8.0.tgz
     tar -xzf occt-7.8.0.tgz
     cd occt-7.8.0
     mkdir build && cd build
     cmake ..
     make -j$(nproc)
     sudo make install
     ```
   - Alternatively, if your package manager has the exact version:
     ```
     sudo apt-get install libocct-7.8.0-dev
     ```

## Building the Project

1. **Clone the repository**
   ```
   git clone https://github.com/yourusername/Vizion3D.git
   cd Vizion3D
   ```

2. **Create a build directory**
   ```
   mkdir build
   cd build
   ```

3. **Configure the project with CMake**
   ```
   cmake ..
   ```

4. **Build the project**
   - On Windows:
     ```
     cmake --build . --config Release
     ```
   - On macOS/Linux:
     ```
     cmake --build . -- -j$(nproc)
     ```

5. **Run the application**
   - On Windows:
     ```
     .\bin\Release\Vizion3D.exe
     ```
   - On macOS/Linux:
     ```
     ./bin/Vizion3D
     ```

## Development with Qt Creator

1. **Open Qt Creator**

2. **Open the project**
   - Select "Open Project" from the File menu
   - Navigate to the Vizion3D directory and select the `CMakeLists.txt` file
   - Configure the project when prompted

3. **Build and run**
   - Click the "Build" button (hammer icon) to build the project
   - Click the "Run" button (play icon) to run the application

## Troubleshooting

### CMake can't find Qt

If CMake can't find Qt, you may need to set the `CMAKE_PREFIX_PATH` to point to your Qt installation:

```
cmake -DCMAKE_PREFIX_PATH=/path/to/qt ..
```

For example:
- Windows: `-DCMAKE_PREFIX_PATH=C:\Qt\6.2.0\msvc2019_64`
- macOS: `-DCMAKE_PREFIX_PATH=/usr/local/opt/qt@6`
- Linux: `-DCMAKE_PREFIX_PATH=/opt/qt62`

### CMake can't find OpenCASCADE

If CMake can't find OpenCASCADE, you may need to set the `OpenCASCADE_DIR` variable:

```
cmake -DOpenCASCADE_DIR=/path/to/opencascade/cmake ..
```

For example:
- Windows: `-DOpenCASCADE_DIR=C:\OpenCASCADE\cmake`
- macOS: `-DOpenCASCADE_DIR=/usr/local/lib/cmake/opencascade`
- Linux: `-DOpenCASCADE_DIR=/usr/lib/cmake/opencascade`

### Build errors

If you encounter build errors, make sure you have the correct version of Qt and a C++17 compatible compiler. Check the CMake output for any warnings or errors that might indicate missing dependencies.

## Next Steps

After setting up the development environment, you can:

1. Familiarize yourself with the project structure
2. Review the code in the `src` directory
3. Run the application and explore its features
4. Start implementing new features or fixing bugs

For more information, refer to the other documentation files in the `docs` directory.
