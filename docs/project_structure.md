# Qt Project Structure Design

## Overview

This document outlines the directory organization, naming conventions, module boundaries, and build system configuration for the Vizion3D Qt project. The goal is to create a maintainable, scalable, and modular codebase that follows Qt best practices.

## Directory Structure

The project follows a modular directory structure organized by functionality:

```
Vizion3D/
├── CMakeLists.txt          # Main CMake configuration file
├── .clang-format           # Code formatting configuration
├── include/                # Public header files
│   ├── ui/                 # UI component headers
│   ├── engine/             # Simulation engine interface headers
│   ├── models/             # Data model headers
│   └── utils/              # Utility headers
├── src/                    # Source files
│   ├── main.cpp            # Application entry point
│   ├── ui/                 # UI component implementations
│   │   ├── mainwindow.cpp  # Main window implementation
│   │   ├── widgets/        # Custom widgets
│   │   └── dialogs/        # Dialog implementations
│   ├── engine/             # Simulation engine integration
│   │   ├── gcode/          # G-code processing
│   │   ├── simulation/     # Simulation control
│   │   └── visualization/  # 3D visualization
│   ├── models/             # Data models
│   │   ├── gcode_model.cpp # G-code data model
│   │   ├── toolpath_model.cpp # Toolpath data model
│   │   └── simulation_model.cpp # Simulation state model
│   └── utils/              # Utility implementations
├── resources/              # Application resources
│   ├── icons/              # Icons and images
│   ├── shaders/            # GLSL shader files
│   ├── styles/             # Style sheets
│   └── ui/                 # UI definition files
├── tests/                  # Test files
│   ├── unit/               # Unit tests
│   ├── integration/        # Integration tests
│   └── fixtures/           # Test fixtures
├── docs/                   # Documentation
│   ├── setup.md            # Development environment setup guide
│   ├── project_structure.md # This document
│   └── engine_integration.md # Engine integration plan
└── build/                  # Build output (not in version control)
```

## Module Organization

The project is organized into the following key modules:

### 1. UI Module

**Responsibility**: Provides the user interface components, including the main window, docking system, and custom widgets.

**Key Components**:
- Main window
- Docking system
- G-code editor
- 3D viewport
- Simulation controls
- Settings dialogs

**Directory**: `include/ui/` and `src/ui/`

### 2. Engine Module

**Responsibility**: Integrates the C++ simulation engine with the Qt application, providing interfaces for G-code processing, simulation control, and 3D visualization.

**Key Components**:
- G-code interpreter
- Simulation controller
- Toolpath generator
- 3D visualization renderer

**Directory**: `include/engine/` and `src/engine/`

### 3. Models Module

**Responsibility**: Provides data models for the application, using Qt's model/view architecture.

**Key Components**:
- G-code model
- Toolpath model
- Simulation state model
- Project model

**Directory**: `include/models/` and `src/models/`

### 4. Utils Module

**Responsibility**: Provides utility functions and classes used throughout the application.

**Key Components**:
- File utilities
- Math utilities
- Logging
- Configuration

**Directory**: `include/utils/` and `src/utils/`

## Naming Conventions

### Files

- Header files: `snake_case.h`
- Source files: `snake_case.cpp`
- UI files: `snake_case.ui`
- Resource files: `snake_case.qrc`

### Classes

- Class names: `PascalCase`
- Method names: `camelCase`
- Member variables: `m_camelCase`
- Static variables: `s_camelCase`
- Constants: `k_CamelCase`
- Enums: `PascalCase` with values in `PascalCase`

### Namespaces

- Namespace names: `PascalCase`
- Use namespaces to organize code by module:
  ```cpp
  namespace Vizion3D {
  namespace Engine {
  // Engine code
  }
  }
  ```

## Build System Configuration

The project uses CMake as its build system with the following configuration:

### Main CMakeLists.txt

The main `CMakeLists.txt` file sets up the project, finds required dependencies, and includes subdirectories for each module.

### Module CMakeLists.txt

Each module has its own `CMakeLists.txt` file that defines the sources, headers, and dependencies for that module.

### Build Targets

- `Vizion3D`: Main application executable
- `Vizion3DTests`: Test executable
- `Vizion3DLib`: Static library containing the core functionality (optional)

### Dependencies

- Qt 6.2+
- OpenCASCADE 7.8.0 (optional for initial development)
- C++17 compatible compiler
- CMake 3.16+

## Coding Standards

### Code Style

The project uses a `.clang-format` file to enforce consistent code style. Key style guidelines:

- 4 spaces for indentation
- 120 character line limit
- Opening braces on the same line for functions and control statements
- Spaces around operators
- No spaces between function name and opening parenthesis
- One space after keywords like `if`, `for`, `while`, etc.

### Documentation

- Use Doxygen-style comments for classes, functions, and member variables
- Document public APIs thoroughly
- Keep implementation details in the source files
- Update documentation when making significant changes

## Conclusion

This project structure is designed to provide a clear organization of code, separation of concerns, and a maintainable codebase. By following these guidelines, we can ensure that the Vizion3D Qt application is well-structured and easy to maintain as it grows in complexity.
