# Git Pre-commit Hook Workflow with Auto-fix Integration

## Overview

Vizion3D implements an automated Git pre-commit hook workflow that enforces logging compliance standards and automatically fixes violations where possible. This system ensures code quality while maintaining development velocity.

## How It Works

### Automatic Workflow

1. **Developer stages files**: `git add <files>`
2. **Developer attempts commit**: `git commit -m "message"`
3. **Pre-commit hook runs automatically**:
   - Checks only staged C++ files for logging violations
   - If violations found, automatically applies fixes using `fix-logging-violations.py`
   - Unstages modified files for developer review
   - Blocks commit until developer reviews and re-stages files

### Developer Experience

```bash
# Normal workflow
git add src/my_file.cpp
git commit -m "Add new feature"

# If violations detected:
# 1. Hook runs and shows violations
# 2. Hook applies auto-fixes
# 3. Hook unstages modified files
# 4. Developer reviews fixes
git diff src/my_file.cpp  # Review the auto-fixes
git add src/my_file.cpp   # Re-stage if fixes look good
git commit -m "Add new feature"  # Commit again
```

## Implementation Details

### Core Components

1. **`scripts/pre-commit/logging-precommit-hook.sh`**
   - Main orchestration script
   - Handles staged file detection
   - Coordinates check-and-fix workflow
   - Manages file unstaging after fixes

2. **`scripts/pre-commit/check-logging.sh`**
   - Detects logging violations in staged files
   - Respects infrastructure exceptions
   - Provides detailed violation reports

3. **`scripts/pre-commit/fix-logging-violations.py`**
   - Automatically fixes common logging patterns
   - Handles Qt logging, fprintf, and stream patterns
   - Adds required includes for VLOG_* usage

### Pre-commit Configuration

The system is integrated via `.pre-commit-config.yaml`:

```yaml
- id: vizion3d-logging-compliance
  name: Vizion3D Logging Compliance with Auto-fix
  entry: scripts/pre-commit/logging-precommit-hook.sh
  language: script
  pass_filenames: false
  stages: [commit]
  always_run: true
```

## Supported Auto-fixes

### Automatic Conversions

| Original Pattern | Auto-fix Result |
|-----------------|----------------|
| `qDebug() << "msg"` | `VLOG_DEBUG("Category", "msg")` |
| `qWarning() << "msg"` | `VLOG_WARNING("Category", "msg")` |
| `qCritical() << "msg"` | `VLOG_ERROR("Category", "msg")` |
| `fprintf(stdout, "msg")` | `VLOG_INFO("Category", "msg")` |
| `fprintf(stderr, "msg")` | `VLOG_ERROR("Category", "msg")` |

### Manual Fixes Required

Some patterns require manual attention:
- Complex `std::cout`/`std::cerr` usage
- `printf()` calls with complex formatting
- Context-dependent logging decisions

## Workflow Examples

### Successful Auto-fix

```bash
$ git add src/engine.cpp
$ git commit -m "Update engine logging"

🔍 Running logging compliance pre-commit hook...
📁 Checking 1 staged C++ files for logging compliance

⚠️  Logging violations detected in staged files
🔧 Attempting automatic fixes...
✅ Auto-fix completed successfully!

📝 Modified files:
   - src/engine.cpp

🔄 Unstaging modified files for your review...
   📤 Unstaged: src/engine.cpp

🎯 Next steps:
   1. Review the auto-fixes in the modified files above
   2. If the fixes look correct, re-stage the files:
      git add <files>
   3. Commit again:
      git commit

# Developer reviews and re-stages
$ git diff src/engine.cpp  # Review changes
$ git add src/engine.cpp   # Re-stage
$ git commit -m "Update engine logging"  # Success!
```

### Manual Fixes Required

```bash
$ git add src/complex.cpp
$ git commit -m "Add complex feature"

🔍 Running logging compliance pre-commit hook...
⚠️  Logging violations detected in staged files
🔧 Attempting automatic fixes...
ℹ️  No files were modified by auto-fix (violations may require manual attention)
🚫 Commit blocked - please review and fix violations manually

# Developer must fix manually
$ vim src/complex.cpp      # Fix remaining violations
$ git add src/complex.cpp  # Re-stage
$ git commit -m "Add complex feature"  # Success!
```

## Configuration

### Enabling/Disabling

The pre-commit hook is automatically installed when running:
```bash
./scripts/setup-quality-checks.sh
```

### Bypassing (Not Recommended)

In exceptional cases, you can bypass the hook:
```bash
git commit --no-verify -m "Emergency fix"
```

**Warning**: Only use `--no-verify` for critical hotfixes. All bypassed commits should be fixed in follow-up commits.

## Troubleshooting

### Hook Not Running

1. Check if pre-commit is installed: `pre-commit --version`
2. Reinstall hooks: `pre-commit install --hook-type pre-commit`
3. Verify configuration: `pre-commit run --all-files`

### Auto-fix Issues

1. Check script permissions: `ls -la scripts/pre-commit/logging-precommit-hook.sh`
2. Test fix script manually: `./scripts/pre-commit/fix-logging-violations.py --help`
3. Review infrastructure exceptions in `check-logging.sh`

### Performance

The hook only processes staged files, making it fast even in large codebases:
- Typical runtime: 1-3 seconds for 1-10 files
- Scales linearly with number of staged files
- No impact when no C++ files are staged

## Integration with CI/CD

The pre-commit hook complements CI/CD checks:
- **Pre-commit**: Fast feedback on staged files
- **CI/CD**: Comprehensive checks on entire codebase
- **Both**: Maintain consistent logging standards

## Best Practices

1. **Review auto-fixes**: Always review changes before re-staging
2. **Commit frequently**: Smaller commits = faster hook execution
3. **Fix violations early**: Don't accumulate logging debt
4. **Use proper categories**: Ensure VLOG_* calls use valid categories
5. **Test after fixes**: Verify functionality after auto-fixes

## Related Documentation

- [Logging System Overview](../features/logging_system.md)
- [Error Handling Implementation](../implementation/direct_error_handling_implementation.md)
- [Quality Checks Setup](../development/quality_checks.md)
