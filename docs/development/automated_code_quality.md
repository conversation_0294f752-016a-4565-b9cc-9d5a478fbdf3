# Automated Code Quality Systems

This document describes the automated code quality systems implemented for the Vizion3D project, including pre-commit hooks and CI/CD pipeline integration.

## Overview

Vizion3D implements a comprehensive automated code quality system with two main components:

1. **Pre-commit Hooks** - Fast, local quality checks that run before commits
2. **CI/CD Pipeline** - Comprehensive build verification for pull requests

## Pre-commit Hooks

### 1. Logging Compliance Hook

**File**: `scripts/pre-commit/logging-precommit-hook.sh`

Automatically enforces logging pattern compliance:
- Detects deprecated logging patterns (qDebug, qWarning, std::cout, etc.)
- Automatically fixes violations where possible
- Unstages modified files for developer review
- Blocks commits until violations are resolved

### 2. Clang-Tidy Analysis Hook

**File**: `scripts/pre-commit/clang-tidy-precommit-hook.sh`

Runs clang-tidy analysis on staged files:
- Analyzes only staged C++ files for efficiency
- Uses existing `.clang-tidy` configuration
- Requires existing build directory with `compile_commands.json`
- Provides clear error messages and bypass instructions
- Blocks commits if code quality issues are found

#### Usage

The clang-tidy hook runs automatically on commit. If it fails:

```bash
# Fix the issues reported by clang-tidy
vim src/your_file.cpp

# Re-stage the fixed files
git add src/your_file.cpp

# Commit again
git commit -m "Your commit message"
```

#### Bypass (Not Recommended)

```bash
git commit --no-verify
```

#### Prerequisites

The clang-tidy hook requires a built project:

```bash
# Build the project first
mkdir build && cd build
cmake .. && cmake --build .

# Then commit your changes
git add . && git commit -m "Your changes"
```

### Configuration

Pre-commit hooks are configured in `.pre-commit-config.yaml`:

```yaml
- id: vizion3d-clang-tidy
  name: Vizion3D Clang-Tidy Analysis
  entry: scripts/pre-commit/clang-tidy-precommit-hook.sh
  language: script
  pass_filenames: false
  stages: [commit]
  always_run: true
```

## CI/CD Pipeline

### Pull Request Build Verification

**File**: `.github/workflows/pr-build-verification.yml`

Comprehensive build verification that runs on pull request creation/updates:

#### Features

- **Multi-Platform Testing**: macOS and Windows
- **Multi-Configuration**: Debug and Release builds
- **Application Launch Verification**: Ensures the built application starts successfully
- **Log Analysis**: Verifies successful startup using logging patterns
- **Artifact Upload**: Saves build logs for debugging

#### Trigger Conditions

The workflow triggers on:
- Pull request opened
- Pull request synchronized (new commits)
- Pull request reopened
- Changes to source code, CMake files, or workflow files

#### Build Matrix

| Platform | Build Types | Qt Version |
|----------|-------------|------------|
| macOS-latest | Debug, Release | 6.5.0 |
| windows-latest | Debug, Release | 6.5.0 |

#### Application Launch Verification

The pipeline includes sophisticated application launch verification:

1. **Build Verification**: Ensures executable is created
2. **Launch Test**: Starts the application with timeout
3. **Log Analysis**: Checks for successful startup patterns:
   - "Logger initialized"
   - "MainWindow created successfully"
   - "Entering main event loop"
4. **Error Detection**: Fails if critical errors are found
5. **Cleanup**: Properly terminates processes

#### Headless GUI Testing

- **macOS**: Uses offscreen Qt platform or Xvfb
- **Windows**: Runs in headless mode with timeout
- **Linux**: Uses Xvfb virtual display (if needed)

### Helper Scripts

#### Application Launch Verification

**File**: `scripts/ci/verify-app-launch.sh`

Reusable script for application launch verification:

```bash
# Basic usage
./scripts/ci/verify-app-launch.sh ./build/Vizion3D

# With custom timeout
./scripts/ci/verify-app-launch.sh ./build/Vizion3D 45
```

Features:
- Cross-platform support (macOS, Windows, Linux)
- Virtual display setup for headless testing
- Comprehensive log analysis
- Proper cleanup of processes
- Detailed error reporting

## Integration with Existing Systems

### Logging System Integration

Both systems integrate with the existing Vizion3D logging system:
- Use VLOG_* macros for consistent logging
- Respect logging categories and levels
- Analyze startup logs for verification

### Build System Integration

- Uses existing CMake configuration
- Respects Vizion3D build options
- Works with Qt6 setup
- Generates `compile_commands.json` for clang-tidy

### Error Handling Integration

- Compatible with Result<T> error handling patterns
- Respects existing error codes and categories
- Maintains consistency with project standards

## Troubleshooting

### Pre-commit Hook Issues

#### Clang-Tidy Hook Fails

```bash
❌ No build directory with compile_commands.json found

🔧 To fix this issue:
   1. Build the project first:
      mkdir build && cd build
      cmake .. && cmake --build .
```

**Solution**: Build the project before committing.

#### Permission Issues

```bash
# Make scripts executable
chmod +x scripts/pre-commit/clang-tidy-precommit-hook.sh
chmod +x scripts/ci/verify-app-launch.sh
```

### CI/CD Issues

#### Qt Installation Failures

Check the GitHub Actions logs for Qt installation issues. The workflow uses `jurplel/install-qt-action@v3` which should handle most Qt setup automatically.

#### Application Launch Timeouts

If the application launch verification times out:
1. Check for missing dependencies
2. Verify Qt platform plugins are available
3. Review application startup logs in artifacts

#### Build Failures

1. Check CMake configuration
2. Verify all dependencies are available
3. Review compiler errors in build logs

## Best Practices

### For Developers

1. **Build Before Committing**: Always build the project before committing to ensure clang-tidy can run
2. **Review Auto-fixes**: When logging compliance auto-fixes are applied, review them before re-staging
3. **Fix Issues Promptly**: Address clang-tidy warnings rather than bypassing checks
4. **Test Locally**: Run pre-commit hooks manually to catch issues early

### For Maintainers

1. **Monitor CI/CD**: Regularly check CI/CD pipeline health
2. **Update Dependencies**: Keep Qt and other dependencies up to date
3. **Review Configurations**: Periodically review `.clang-tidy` and workflow configurations
4. **Analyze Trends**: Use build logs to identify common issues

## Configuration Files

### Key Files

- `.pre-commit-config.yaml` - Pre-commit hook configuration
- `.clang-tidy` - Clang-tidy analysis rules
- `.github/workflows/pr-build-verification.yml` - CI/CD workflow
- `scripts/pre-commit/clang-tidy-precommit-hook.sh` - Clang-tidy hook script
- `scripts/ci/verify-app-launch.sh` - Application launch verification

### Customization

#### Adjusting Clang-Tidy Rules

Edit `.clang-tidy` to modify analysis rules:

```yaml
Checks: >
  -*,
  readability-*,
  performance-*,
  bugprone-*,
  # Add or remove checks as needed
```

#### Modifying CI/CD Behavior

Edit `.github/workflows/pr-build-verification.yml` to:
- Change Qt versions
- Add/remove platforms
- Modify timeout values
- Adjust build configurations

## Performance Considerations

### Pre-commit Hooks

- **Clang-Tidy Hook**: Only analyzes staged files (typically 1-5 files)
- **Typical Runtime**: 10-30 seconds for small changes
- **Scaling**: Linear with number of staged files

### CI/CD Pipeline

- **Build Time**: 5-15 minutes per platform/configuration
- **Parallel Execution**: All matrix jobs run in parallel
- **Caching**: Qt installation is cached to reduce setup time
- **Resource Usage**: Optimized for GitHub Actions free tier

## Future Enhancements

### Planned Improvements

1. **Incremental Analysis**: Only analyze changed lines in clang-tidy
2. **Performance Benchmarking**: Add performance regression detection
3. **Code Coverage**: Integrate code coverage reporting
4. **Security Scanning**: Add security vulnerability scanning
5. **Documentation Checks**: Verify API documentation completeness

### Integration Opportunities

1. **IDE Integration**: Provide IDE-specific configurations
2. **Local Development**: Enhance local development workflow
3. **Release Automation**: Integrate with release pipeline
4. **Quality Metrics**: Add code quality dashboards
