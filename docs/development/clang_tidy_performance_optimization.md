# Clang-Tidy Pre-commit Hook Performance Optimization

## Overview

The clang-tidy pre-commit hook has been optimized to significantly reduce commit times while maintaining code quality standards. This document details the optimizations implemented and their performance impact.

## Performance Results

### Before Optimization
- **Original hook**: ~7+ seconds for single file analysis
- **No parallel processing**: Sequential analysis only
- **Full check scope**: All enhanced modernize-* and cppcoreguidelines-* checks
- **No performance options**: Fixed configuration

### After Optimization
- **Optimized hook (fast mode)**: ~4.3 seconds for single file analysis
- **Parallel processing**: Configurable parallel analysis
- **Flexible check scope**: Fast mode with reduced checks
- **Performance tracking**: Built-in timing and reporting

### Performance Improvement
- **~40% faster** in fast mode
- **Scalable**: Parallel processing for multiple files
- **Configurable**: Multiple performance modes available

## Optimization Techniques Implemented

### 1. Parallel File Analysis
```bash
# Enable parallel processing (default: true)
export CLANG_TIDY_PARALLEL=true

# Set number of parallel jobs (default: number of CPU cores)
export CLANG_TIDY_JOBS=4
```

**Benefits:**
- Multiple files analyzed simultaneously
- Scales with available CPU cores
- Significant speedup for commits with multiple files

### 2. Fast Mode Configuration
```bash
# Enable fast mode for reduced check scope
export CLANG_TIDY_FAST=true
```

**Fast mode checks:**
- `bugprone-*` - Critical bug detection
- `clang-analyzer-*` - Static analysis
- `readability-identifier-naming` - Naming conventions
- `modernize-use-nullptr` - Essential modernization
- `modernize-use-override` - Virtual function safety

**Benefits:**
- ~40% faster analysis
- Focuses on critical issues
- Suitable for frequent commits

### 3. Optimized Clang-Tidy Arguments
```bash
# Header filtering to reduce scope
--header-filter='^(?!.*/build/)(?!.*moc_)(?!.*ui_).*\.(h|hpp)$'

# Performance optimizations
--use-color=false
--quiet
```

**Benefits:**
- Excludes build artifacts and Qt auto-generated files
- Reduces output processing overhead
- Focuses analysis on project source code

### 4. Performance Tracking
The optimized hook provides detailed timing information:
```
Clang-Tidy Analysis Summary:
  - Total files analyzed: 1
  - Files with issues: 0
  - Clean files: 1
  - Analysis time: 4s
  - Total hook time: 4s
```

## Configuration Options

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `CLANG_TIDY_PARALLEL` | `true` | Enable parallel processing |
| `CLANG_TIDY_JOBS` | CPU cores | Number of parallel jobs |
| `CLANG_TIDY_FAST` | `false` | Enable fast mode (reduced checks) |

### Usage Examples

```bash
# Standard optimized mode (recommended)
git commit

# Fast mode for quick commits
CLANG_TIDY_FAST=true git commit

# Disable parallel processing
CLANG_TIDY_PARALLEL=false git commit

# Custom parallel job count
CLANG_TIDY_JOBS=8 git commit

# Combined optimizations
CLANG_TIDY_FAST=true CLANG_TIDY_JOBS=4 git commit
```

## Performance Recommendations

### For Different Scenarios

#### Daily Development (Frequent Commits)
```bash
# Add to your shell profile
export CLANG_TIDY_FAST=true
export CLANG_TIDY_PARALLEL=true
```
- **Target time**: <10 seconds
- **Focus**: Critical issues only
- **Benefit**: Faster development cycle

#### Code Review Preparation
```bash
# Full analysis before creating PR
CLANG_TIDY_FAST=false git commit
```
- **Target time**: <30 seconds
- **Focus**: Complete code quality analysis
- **Benefit**: Comprehensive issue detection

#### Large Commits (Multiple Files)
```bash
# Maximize parallel processing
CLANG_TIDY_JOBS=8 git commit
```
- **Target time**: Scales with file count
- **Focus**: Efficient multi-file analysis
- **Benefit**: Consistent performance regardless of commit size

### Performance Targets

| Scenario | Target Time | Configuration |
|----------|-------------|---------------|
| Single file commit | <10 seconds | Fast mode enabled |
| 2-5 files commit | <20 seconds | Parallel processing |
| Large commit (5+ files) | <30 seconds | Max parallel jobs |
| Full analysis | <60 seconds | All checks enabled |

## File Scope Verification

The hook analyzes **only staged files**, not the entire codebase:

### Included Files
- Staged C++ source files (`.cpp`, `.cxx`, `.cc`, `.c++`)
- Staged C++ header files (`.h`, `.hpp`)
- Project source code in `src/` and `include/` directories

### Excluded Files
- Build artifacts (`build/`, `cmake-build-*/`)
- Qt auto-generated files (`ui_*.h`, `moc_*.cpp`, `qrc_*.cpp`)
- Files not staged for commit
- Non-C++ files

### Verification Command
```bash
# Check which files will be analyzed
git diff --cached --name-only | grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$'
```

## Troubleshooting Performance Issues

### If Hook is Still Slow

1. **Check file count**:
   ```bash
   git diff --cached --name-only | wc -l
   ```

2. **Enable fast mode**:
   ```bash
   CLANG_TIDY_FAST=true git commit
   ```

3. **Check system resources**:
   ```bash
   # Monitor CPU usage during analysis
   top -p $(pgrep clang-tidy)
   ```

4. **Verify build directory**:
   ```bash
   # Ensure compile_commands.json exists and is recent
   ls -la build/compile_commands.json
   ```

### Performance Debugging

Enable verbose output for debugging:
```bash
# Add debug output to hook
set -x  # Add to beginning of hook script
```

## Benchmarking Tools

### Performance Comparison Script
```bash
# Compare original vs optimized performance
./scripts/pre-commit/compare-performance.sh
```

### Benchmark Individual Configurations
```bash
# Test different configurations
./scripts/pre-commit/benchmark-clang-tidy.sh
```

## Future Optimizations

### Potential Improvements
1. **Compilation database caching**: Cache parsed compilation database
2. **Incremental analysis**: Only analyze changed functions/classes
3. **Check result caching**: Cache results for unchanged files
4. **Distributed analysis**: Analyze files on multiple machines

### Monitoring
- Track hook execution times over time
- Identify performance regressions
- Optimize based on usage patterns

## Conclusion

The optimized clang-tidy pre-commit hook provides:
- **40% performance improvement** in fast mode
- **Scalable parallel processing** for multiple files
- **Flexible configuration** for different development scenarios
- **Maintained code quality** with focused check selection

The optimization ensures that code quality checks remain practical for daily development while providing comprehensive analysis when needed.
