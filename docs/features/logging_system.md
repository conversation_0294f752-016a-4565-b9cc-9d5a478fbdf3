# Vizion3D Logging System

## Overview

The Vizion3D logging system provides a comprehensive framework for logging and debugging throughout the application. It is designed to be flexible, configurable, and to have minimal performance impact when disabled.

## Features

- Multiple severity levels (TRACE, DEBUG, INFO, WARNING, ERROR, FATAL)
- Category-based logging for different components
- Multiple output destinations (console, file, UI widget)
- Runtime configuration through UI
- Compile-time configuration for release builds
- Log rotation for file output
- Performance profiling macros
- Assertion macros
- Debug console widget for viewing logs in the UI

## Architecture

The logging system is built around the following key components:

```
                  +----------------+
                  |   LogConfig    |
                  +----------------+
                          |
                          v
                  +----------------+
                  |     Logger     |
                  +----------------+
                          |
                          v
         +----------------+----------------+
         |                |                |
+----------------+ +----------------+ +----------------+
| ConsoleLogSink | |  FileLogSink   | |DebugConsoleLogSink|
+----------------+ +----------------+ +----------------+
```

### Core Components

1. **Logger**: The central component that manages log sinks and provides the logging API.
2. **LogConfig**: Manages logging configuration and provides a convenient interface for configuring the logging system.
3. **ILogSink**: Interface for log sinks, which are destinations for log messages.
4. **ConsoleLogSink**: Outputs log messages to the console (stdout/stderr).
5. **FileLogSink**: Outputs log messages to a file with rotation support.
6. **DebugConsoleLogSink**: Outputs log messages to a UI widget.
7. **LogSettingsDialog**: UI dialog for configuring logging settings.
8. **DebugConsoleWidget**: UI widget for viewing log messages.

## Usage

### Basic Logging

```cpp
// Log a message at different severity levels
VLOG_TRACE("Category", "Trace message");
VLOG_DEBUG("Category", "Debug message");
VLOG_INFO("Category", "Info message");
VLOG_WARNING("Category", "Warning message");
VLOG_ERROR("Category", "Error message");
VLOG_FATAL("Category", "Fatal message");
```

### Profiling

```cpp
// Profile a block of code
VIZION3D_PROFILE_BLOCK("Block name");

// Profile a function
VIZION3D_PROFILE_FUNCTION();
```

### Assertions

```cpp
// Assert that a condition is true
VIZION3D_ASSERT(condition);

// Assert with a message
VIZION3D_ASSERT_MSG(condition, "Assertion failed message");
```

## Result<T> Pattern Integration

### Automatic Error Logging

The logging system integrates seamlessly with the Result<T> error handling pattern. When errors are created using the Result<T> system, they are **automatically logged** with appropriate severity levels and categories, eliminating the need for manual error logging in most cases.

```cpp
#include "utils/result.h"

// Automatic error logging when Result<T> errors are created
auto result = m_simulationEngine->parseGCode(invalidGCode);
if (result.isError()) {
    // Error was automatically logged with appropriate level and category:
    // [ERROR] [GCode] [GCodeSyntaxError] Invalid G-code syntax at line 42

    // Access structured error information
    const auto& error = result.error();
    // Error details are available for UI display or further processing
    QMessageBox::critical(this, "G-code Error", error.message);
}
```

### Enhanced Error Categorization

The Result<T> system provides structured error categorization that automatically integrates with the existing logging categories:

- **G-code Processing Errors (1000-1999)** → "GCode" category
- **Simulation Engine Errors (2000-2999)** → "Engine" category
- **OpenGL Rendering Errors (3000-3999)** → "OpenGL" category
- **File I/O Errors (4000-4999)** → "FileIO" category
- **Memory and Resource Errors (5000-5999)** → "System" category
- **Threading Errors (6000-6999)** → "Threading" category
- **Validation Errors (7000-7999)** → "Validation" category
- **System Errors (8000-8999)** → "System" category

### Structured Error Information

Each error contains comprehensive information for debugging and troubleshooting:

```cpp
struct Error {
    ErrorCode code;        // Numeric error code for programmatic handling
    QString message;       // Human-readable error message
    QString category;      // Logging category (auto-assigned from error code)
    QString file;          // Source file where error occurred
    int line;             // Line number where error occurred
    QString function;      // Function where error occurred
    QString context;       // Additional context information
};
```

### Automatic Log Level Assignment

Error codes are automatically mapped to appropriate log levels using `getDefaultLogLevel()`:

- **Fatal**: OutOfMemory, InternalError, ThreadDeadlockDetected
- **Warning**: FileNotFound, DirectoryNotFound, GCodeUnsupportedFeature, NotImplemented, OperationCancelled
- **Error**: Most other error conditions (GCodeSyntaxError, SimulationInvalidState, OpenGLShaderCompileError, etc.)

### Test vs Production Behavior

The logging system behaves differently in test and production environments:

**Production Mode** (normal application):
```cpp
// Uses full logging system with all configured sinks
auto result = Result<void>::error(ErrorCode::FileNotFound, "Config file missing");
// Logs to: console, file (if enabled), debug console widget
```

**Test Mode** (when QT_TESTLIB_LIB is defined):
```cpp
// Uses simple stderr output for debugging
auto result = Result<void>::error(ErrorCode::FileNotFound, "Config file missing");
// Outputs to stderr: [FileNotFound] Config file missing
```

### Usage Examples

```cpp
// Simple error creation with automatic logging
auto result = Result<void>::error(ErrorCode::FileNotFound, "Configuration file not found");

// Error with additional context
auto result = Result<int>::error(ErrorCode::GCodeSyntaxError,
                                "Invalid coordinate value",
                                "Processing line 42 of input file");

// Real example from SimulationEngine
Vizion3D::Utils::Result<void> SimulationEngine::initialize(const QString& configPath) {
    try {
        // Initialization logic...
        return Vizion3D::Utils::Result<void>::success();
    } catch (const std::exception& e) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InitializationFailed,
            QString("Error initializing simulation engine: %1").arg(e.what())
        );
    }
}

// Using convenience macros (available but less commonly used)
auto result = VIZION3D_ERROR(ErrorCode::InvalidArgument, "Parameter cannot be null");
auto result = VIZION3D_ERROR_WITH_CONTEXT(ErrorCode::OpenGLShaderCompileError,
                                          "Vertex shader compilation failed",
                                          "Loading grid renderer shaders");
```

## Developer Guidance

### When to Use What: VLOG_* vs Result<T>

**Use Result<T> automatic logging when:**
- Implementing functions that can fail (most engine operations)
- You want structured error information with error codes
- The error should be propagated to calling code
- You need automatic categorization and log level assignment

**Use VLOG_* macros when:**
- Adding informational logging (TRACE, DEBUG, INFO)
- Logging successful operations or state changes
- Adding context around Result<T> errors in UI code
- Performance-critical paths where you want explicit control

### Decision Tree

```
Is this an error condition that should be returned to caller?
├─ YES → Use Result<T>::error() (automatic logging)
└─ NO → Is this informational/debugging?
    ├─ YES → Use VLOG_* macros
    └─ NO → Is this additional context for an existing error?
        └─ YES → Use VLOG_* macros with error details
```

### Required Includes and Setup

**For Result<T> error handling:**
```cpp
#include "utils/result.h"
// Automatically includes logger.h and provides ErrorCode enum
```

**For direct VLOG_* usage:**
```cpp
#include "utils/logger.h"
// Provides VLOG_TRACE, VLOG_DEBUG, VLOG_INFO, VLOG_WARNING, VLOG_ERROR, VLOG_FATAL
```

**No special initialization required** - the logging system is automatically initialized by the application.

### Best Practices by Scenario

**Engine Operations (Recommended Pattern):**
```cpp
// In engine classes - use Result<T> for operations that can fail
Vizion3D::Utils::Result<void> GCodeInterpreter::parseGCode(const QString& gcode) {
    if (gcode.isEmpty()) {
        // Automatic logging with appropriate category and level
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::GCodeParsingError,
            "G-code string is empty"
        );
    }

    // Add informational logging for successful operations
    VLOG_INFO("GCode", QString("Parsing G-code with %1 lines").arg(lines.size()));

    // ... parsing logic ...

    return Vizion3D::Utils::Result<void>::success();
}
```

**UI Error Handling (Recommended Pattern):**
```cpp
// In UI classes - handle Result<T> and add user-friendly context
void MainWindow::onStartSimulation() {
    auto result = m_simulationEngine->startSimulation(timeStep);
    if (!result.isSuccess()) {
        const auto& error = result.error();

        // Error was already logged automatically by Result<T>
        // Add UI-specific context with VLOG_*
        VLOG_ERROR("UI", QString("User action failed: %1").arg(error.message));

        // Display user-friendly error message
        QMessageBox::critical(this, "Simulation Error", error.message);
        return;
    }

    // Log successful user actions
    VLOG_INFO("UI", "Simulation started successfully");
}
```

**OpenGL Operations (Recommended Pattern):**
```cpp
// In renderer classes - use Result<T> for initialization/setup
Vizion3D::Utils::Result<void> GridRenderer::initialize() {
    if (!initializeOpenGLFunctions()) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::OpenGLContextError,
            "Failed to initialize OpenGL functions"
        );
    }

    // Use VLOG_* for detailed tracing in debug builds
    VLOG_DEBUG("OpenGL", "GridRenderer initialized successfully");

    return Vizion3D::Utils::Result<void>::success();
}
```

### Performance Considerations

**Result<T> Overhead:**
- Minimal overhead in success cases (single variant storage)
- Error cases have logging overhead but are typically rare
- Automatic logging eliminates manual logging code

**VLOG_* Macro Overhead:**
- Zero overhead when logging is disabled at compile time
- Category-level filtering reduces runtime overhead
- File logging uses buffered I/O for performance

**Recommendations:**
- Use Result<T> for error paths (typically infrequent)
- Use VLOG_TRACE/DEBUG sparingly in hot paths
- Configure category log levels to filter out verbose logging in production

### Common Patterns

**Error Propagation:**
```cpp
// Engine layer
auto parseResult = m_interpreter->parseGCode(gcode);
if (!parseResult.isSuccess()) {
    // Propagate error up the stack (already logged)
    return parseResult;
}

// UI layer
auto engineResult = m_engine->someOperation();
if (!engineResult.isSuccess()) {
    // Add UI context and display to user
    VLOG_ERROR("UI", QString("Operation failed: %1").arg(engineResult.error().message));
    showErrorDialog(engineResult.error());
}
```

**State Logging:**
```cpp
// Log state changes and important events
VLOG_INFO("Engine", "Simulation started");
VLOG_DEBUG("Engine", QString("Processing G-code line %1: %2").arg(lineNum).arg(line));
VLOG_TRACE("Engine", QString("Tool position: X=%1, Y=%2, Z=%3").arg(x).arg(y).arg(z));
```

**Conditional Logging:**
```cpp
// Use category-specific log levels for control
if (LogConfig::instance().categoryLogLevel("GCode") <= LogLevel::Debug) {
    VLOG_DEBUG("GCode", QString("Detailed parsing info: %1").arg(details));
}
```

## Configuration

### Compile-Time Configuration

The logging system can be configured at compile time through CMake options:

```cmake
# Enable/disable debug mode
option(VIZION3D_ENABLE_DEBUG "Enable debug mode" ON)

# Enable/disable logging
option(VIZION3D_ENABLE_LOGGING "Enable logging" ON)

# Enable/disable profiling
option(VIZION3D_ENABLE_PROFILING "Enable performance profiling" ON)

# Enable/disable assertions
option(VIZION3D_ENABLE_ASSERTIONS "Enable assertions" ON)
```

These options define preprocessor macros that control the behavior of the logging system:

- `VIZION3D_DEBUG_MODE`: Enables debug mode
- `VIZION3D_DISABLE_LOGGING`: Disables all logging
- `VIZION3D_DISABLE_PROFILING`: Disables profiling
- `VIZION3D_DISABLE_ASSERTIONS`: Disables assertions

**Definitions:**

- **Logging**: The process of recording messages about the application's execution, such as informational events, warnings, and errors. In Vizion3D, logging helps developers monitor application behavior, diagnose issues, and track the flow of execution through various severity levels and output destinations.
- **Profiling**: The measurement and analysis of code performance, such as execution time of functions or code blocks. Profiling in Vizion3D is used to identify performance bottlenecks and optimize critical sections of the codebase using dedicated macros.
- **Assertions**: Runtime checks that validate assumptions made by the code. If an assertion fails, it typically indicates a programming error or unexpected condition. In Vizion3D, assertions help catch bugs early by halting execution or reporting errors when conditions are not met.

### Runtime Configuration

The logging system can be configured at runtime through the `LogConfig` class:

```cpp
// Set global log level
Vizion3D::Utils::LogConfig::instance().setGlobalLogLevel(Vizion3D::Utils::LogLevel::Debug);

// Set log level for a specific category
Vizion3D::Utils::LogConfig::instance().setCategoryLogLevel("UI", Vizion3D::Utils::LogLevel::Info);

// Enable file logging (disabled by default)
Vizion3D::Utils::LogConfig::instance().setFileLoggingEnabled(true, "path/to/log/file.log");

// Configure file logging with rotation
Vizion3D::Utils::LogConfig::instance().setFileLoggingEnabled(
    true,                    // enabled
    "vizion3d.log",         // file path
    10 * 1024 * 1024,       // max size: 10MB
    5                       // max backup files
);

// Enable/disable console logging (enabled by default)
Vizion3D::Utils::LogConfig::instance().setConsoleLoggingEnabled(true);
```

### Default Configuration

**Important**: The logging system has specific defaults that developers should be aware of:

- **Global Log Level**: Info (shows Info, Warning, Error, Fatal)
- **Console Logging**: Enabled by default
- **File Logging**: **Disabled by default** (must be explicitly enabled)
- **Category Log Levels**: Inherit from global level unless specifically set
- **File Rotation**: 10MB max size, 5 backup files when enabled

### Configuration Examples

**Development Configuration:**
```cpp
// Enable verbose logging for development
LogConfig::instance().setGlobalLogLevel(LogLevel::Debug);
LogConfig::instance().setCategoryLogLevel("GCode", LogLevel::Trace);
LogConfig::instance().setCategoryLogLevel("OpenGL", LogLevel::Debug);
LogConfig::instance().setFileLoggingEnabled(true, "debug.log");
```

**Production Configuration:**
```cpp
// Minimal logging for production
LogConfig::instance().setGlobalLogLevel(LogLevel::Warning);
LogConfig::instance().setFileLoggingEnabled(true, "vizion3d.log", 50 * 1024 * 1024, 3);
LogConfig::instance().setConsoleLoggingEnabled(false);
```

**Testing Configuration:**
```cpp
// Focused logging for specific components
LogConfig::instance().setGlobalLogLevel(LogLevel::Error);
LogConfig::instance().setCategoryLogLevel("Engine", LogLevel::Debug);
LogConfig::instance().setFileLoggingEnabled(false);
```

### UI Configuration

The logging system can also be configured through the UI using the `LogSettingsDialog`:

1. Open the Debug menu
2. Select "Logging Settings"
3. Configure global log level, category log levels, file logging, and console logging
4. Click "Apply" or "OK" to apply the settings

## Debug Console

The debug console provides a UI widget for viewing log messages:

1. Open the Debug menu
2. Select "Debug Console"
3. The debug console will appear as a dockable widget
4. Configure the console using the controls at the top:
   - Log level filter
   - Category filter
   - Display options (timestamps, categories, file info, functions, thread IDs)
5. Use the "Clear" button to clear the console
6. Use the "Save" button to save the console contents to a file

## Command-Line Options

The logging system can be configured through command-line options:

```
--log-level=<level>   Set global log level (trace, debug, info, warning, error, fatal)
--log-file=<path>     Set log file path
--no-console-log      Disable console logging
```

## Log File Management

The logging system automatically manages log files to prevent them from growing too large:

1. When a log file reaches the maximum size (default: 10 MB), it is rotated
2. The current log file is renamed to `<filename>.1`
3. Existing backup files are shifted (`.1` becomes `.2`, etc.)
4. The oldest backup file is deleted if it exceeds the maximum backup count (default: 5)

## Real-World Examples

### Engine Layer Examples

**From GCodeInterpreter:**
```cpp
// Result<T> with automatic logging for parsing errors
Vizion3D::Utils::Result<void> GCodeInterpreter::parseGCode(const QString &gcode) {
    if (gcode.isEmpty()) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::GCodeParsingError,
            "G-code string is empty"
        );
    }

    // Manual logging for successful operations
    VLOG_INFO("GCode", QString("Starting G-code parsing with %1 characters").arg(gcode.length()));

    // ... parsing logic ...

    return Vizion3D::Utils::Result<void>::success();
}
```

**From SimulationEngine:**
```cpp
// Error handling with context information
Vizion3D::Utils::Result<void> SimulationEngine::initialize(const QString& configPath) {
    try {
        // Initialization logic...
        return Vizion3D::Utils::Result<void>::success();
    } catch (const std::exception& e) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InitializationFailed,
            QString("Error initializing simulation engine: %1").arg(e.what())
        );
    }
}
```

### UI Layer Examples

**From MainWindow:**
```cpp
// Handling Result<T> errors in UI with additional context
void MainWindow::onStartSimulation() {
    auto toolpathResult = m_simulationEngine->getToolpath();
    if (!toolpathResult.isSuccess()) {
        const auto& error = toolpathResult.error();
        // Error already logged by Result<T>, add UI context
        VLOG_ERROR("UI", QString("Failed to get toolpath: %1").arg(error.message));
        QMessageBox::critical(this, "Error", QString("Failed to get toolpath: %1").arg(error.message));
        return;
    }

    // Log successful user actions
    VLOG_INFO("UI", "User started simulation");
}

// Signal handlers with structured error information
void MainWindow::onSimulationError(const Vizion3D::Utils::Error& error) {
    VLOG_ERROR("UI", QString("Simulation error: %1").arg(error.message));

    // Create user-friendly error message
    QString displayMessage = error.message;
    if (!error.context.isEmpty()) {
        displayMessage = QString("%1\nContext: %2").arg(error.message, error.context);
    }

    QMessageBox::critical(this, "Simulation Error", displayMessage);
}
```

### Integration Patterns

**Error Propagation Chain:**
```cpp
// 1. Engine detects error and creates Result<T> (automatic logging)
auto parseResult = interpreter->parseGCode(invalidCode);
// Logs: [ERROR] [GCode] [GCodeSyntaxError] Invalid syntax at line 42

// 2. Engine propagates error up
if (!parseResult.isSuccess()) {
    return parseResult; // No additional logging needed
}

// 3. UI handles error and adds context
if (!engineResult.isSuccess()) {
    const auto& error = engineResult.error();
    VLOG_ERROR("UI", QString("User operation failed: %1").arg(error.message));
    showUserError(error);
}
```

**Category-Specific Logging:**
```cpp
// Different categories for different components
VLOG_TRACE("GCode", "Processing coordinate command");
VLOG_DEBUG("Engine", "Simulation step completed");
VLOG_INFO("UI", "User opened file dialog");
VLOG_WARNING("OpenGL", "Deprecated function used");
VLOG_ERROR("FileIO", "Failed to read configuration file");
VLOG_FATAL("System", "Out of memory condition detected");
```

## Performance Considerations

### Result<T> Performance Impact

**Success Path (Common Case):**
- Single `std::variant` storage with minimal overhead
- No logging overhead for successful operations
- Comparable performance to traditional return values

**Error Path (Rare Case):**
- Automatic logging adds overhead but errors are typically infrequent
- Structured error information eliminates manual error formatting
- Error propagation is efficient (move semantics)

### VLOG_* Macro Performance

**Compile-Time Optimization:**
```cpp
#ifdef VIZION3D_DISABLE_LOGGING
    #define VLOG_DEBUG(category, message) do {} while(0)  // Zero overhead
#else
    #define VLOG_DEBUG(category, message) /* Full logging */
#endif
```

**Runtime Optimization:**
- Category-level filtering happens early in the pipeline
- Messages below the configured log level are discarded immediately
- File logging uses buffered I/O for better performance
- Debug console updates in batches to avoid UI freezing

### Performance Recommendations

1. **Use Result<T> for error paths** - typically infrequent, automatic logging is beneficial
2. **Use VLOG_TRACE/DEBUG sparingly in hot paths** - can be disabled in production
3. **Configure category log levels appropriately**:
   ```cpp
   // Production: reduce verbose logging
   LogConfig::instance().setCategoryLogLevel("GCode", LogLevel::Warning);

   // Development: enable detailed logging
   LogConfig::instance().setCategoryLogLevel("GCode", LogLevel::Trace);
   ```
4. **Disable file logging if not needed** - it's disabled by default for performance
5. **Use conditional logging for expensive operations**:
   ```cpp
   if (LogConfig::instance().categoryLogLevel("Engine") <= LogLevel::Debug) {
       QString expensiveDebugInfo = generateDetailedState();
       VLOG_DEBUG("Engine", expensiveDebugInfo);
   }
   ```

### Measured Performance Impact

- **Result<T> success case**: < 1% overhead vs traditional returns
- **Result<T> error case**: ~10-50μs for logging (acceptable for error conditions)
- **VLOG_* when disabled**: 0% overhead (compile-time elimination)
- **VLOG_* when enabled**: ~5-20μs per message depending on sinks

## Integration with Qt

The logging system integrates with Qt's logging framework:

1. Qt's `qDebug()`, `qInfo()`, `qWarning()`, `qCritical()`, and `qFatal()` functions are redirected to the logging system
2. Qt's log messages are categorized as "Qt"
3. Qt's log levels are mapped to the logging system's levels

## Quick Reference for Developers

### Essential Information

**What you need to know to use the logging system effectively:**

1. **Include the right header:**
   - `#include "utils/result.h"` for Result<T> error handling (includes logging)
   - `#include "utils/logger.h"` for direct VLOG_* usage

2. **Use Result<T> for functions that can fail:**
   ```cpp
   Vizion3D::Utils::Result<void> myFunction() {
       if (error_condition) {
           return Vizion3D::Utils::Result<void>::error(ErrorCode::SomeError, "Description");
           // Automatically logged with appropriate category and level
       }
       return Vizion3D::Utils::Result<void>::success();
   }
   ```

3. **Use VLOG_* for informational logging:**
   ```cpp
   VLOG_INFO("Category", "Something happened");
   VLOG_DEBUG("Category", QString("Value: %1").arg(value));
   ```

4. **Handle Result<T> in UI code:**
   ```cpp
   auto result = engine->doSomething();
   if (!result.isSuccess()) {
       // Error already logged, add UI context if needed
       VLOG_ERROR("UI", QString("User action failed: %1").arg(result.error().message));
       showErrorToUser(result.error().message);
   }
   ```

### Key Defaults

- **File logging**: Disabled by default (must be explicitly enabled)
- **Console logging**: Enabled by default
- **Global log level**: Info (shows Info, Warning, Error, Fatal)
- **Error categories**: Automatically assigned based on ErrorCode ranges

### Common Categories

- **"GCode"**: G-code parsing and processing
- **"Engine"**: Simulation engine operations
- **"UI"**: User interface events and errors
- **"OpenGL"**: Rendering and graphics operations
- **"FileIO"**: File operations
- **"System"**: Memory, threading, and system-level operations

### When to Use What

| Scenario | Use | Example |
|----------|-----|---------|
| Function can fail | `Result<T>::error()` | Parsing, file operations, initialization |
| Informational logging | `VLOG_INFO/DEBUG/TRACE` | State changes, user actions, debugging |
| UI error handling | Handle `Result<T>` + `VLOG_ERROR` | Display errors to user with context |
| Performance-critical | `VLOG_TRACE` (can be disabled) | Hot paths, frequent operations |

### Configuration Quick Start

**Development setup:**
```cpp
LogConfig::instance().setGlobalLogLevel(LogLevel::Debug);
LogConfig::instance().setFileLoggingEnabled(true, "debug.log");
```

**Production setup:**
```cpp
LogConfig::instance().setGlobalLogLevel(LogLevel::Warning);
LogConfig::instance().setConsoleLoggingEnabled(false);
```

## Future Enhancements

Potential future enhancements to the logging system:

1. Network logging sink for remote debugging
2. Log filtering by regular expressions
3. Log message formatting templates
4. Log message coloring in the console
5. Log message search in the debug console
6. Log message statistics and analytics
7. Integration with crash reporting systems
