# Direct Standardized Error Handling Implementation

## Overview

This document provides a direct implementation plan for standardized error handling using the Result<T, Error> pattern. This approach **completely replaces** existing error handling patterns without backward compatibility layers, creating a clean, consistent, and production-ready solution.

## Core Design

### Error System Architecture

```cpp
// include/utils/result.h
#ifndef VIZION3D_RESULT_H
#define VIZION3D_RESULT_H

#include "utils/logger.h"
#include <QString>
#include <QDebug>
#include <variant>
#include <memory>

namespace Vizion3D::Utils {

enum class ErrorCode {
    // Success
    Success = 0,

    // G-code Processing Errors (1000-1999)
    GCodeParsingError = 1000,
    GCodeSyntaxError = 1001,
    GCodeFileNotFound = 1002,
    GCodeInvalidCommand = 1003,
    GCodeInvalidCoordinate = 1004,
    GCodeUnsupportedFeature = 1005,

    // Simulation Engine Errors (2000-2999)
    SimulationNotInitialized = 2000,
    SimulationAlreadyRunning = 2001,
    SimulationInvalidState = 2002,
    SimulationNoGCode = 2003,
    SimulationThreadError = 2004,

    // OpenGL Rendering Errors (3000-3999)
    OpenGLContextError = 3000,
    OpenGLShaderCompileError = 3001,
    OpenGLShaderLinkError = 3002,
    OpenGLBufferError = 3003,
    OpenGLVAOError = 3004,
    OpenGLTextureError = 3005,

    // File I/O Errors (4000-4999)
    FileNotFound = 4000,
    FilePermissionDenied = 4001,
    FileCorrupted = 4002,
    FileWriteError = 4003,
    FileReadError = 4004,

    // Memory and Resource Errors (5000-5999)
    OutOfMemory = 5000,
    ResourceAllocationFailed = 5001,
    ResourceNotAvailable = 5002,

    // Threading Errors (6000-6999)
    ThreadCreationFailed = 6000,
    ThreadSynchronizationError = 6001,
    ThreadTimeoutError = 6002,

    // Validation Errors (7000-7999)
    InvalidArgument = 7000,
    InvalidState = 7001,
    InvalidConfiguration = 7002,

    // System Errors (8000-8999)
    InternalError = 8000,
    NotImplemented = 8001,
    OperationCancelled = 8002
};

struct Error {
    ErrorCode code;
    QString message;
    QString category;
    QString file;
    int line;
    QString function;
    QString context;  // Additional context information

    Error(ErrorCode code, const QString& message, const QString& category,
          const QString& context = QString(),
          const char* file = __FILE__, int line = __LINE__, const char* function = __FUNCTION__)
        : code(code), message(message), category(category), context(context),
          file(QString::fromUtf8(file)), line(line), function(QString::fromUtf8(function)) {}

    // Automatic logging when error is created
    Error& log(LogLevel level = LogLevel::Error) {
        QString fullMessage = context.isEmpty() ? message : QString("%1: %2").arg(context, message);
        Logger::instance().log(level, category,
            QString("[%1] %2").arg(errorCodeToString(code), fullMessage),
            file, line, function);
        return *this;
    }
};

// Helper functions
QString errorCodeToString(ErrorCode code);
LogLevel getDefaultLogLevel(ErrorCode code);
QString getCategoryForErrorCode(ErrorCode code);

// Result template
template<typename T>
class Result {
private:
    std::variant<T, Error> m_data;

public:
    // Success constructors
    static Result success(T value) { return Result(std::move(value)); }
    static Result success() requires std::is_void_v<T> { return Result(); }

    // Error constructors
    static Result error(Error error) {
        error.log(getDefaultLogLevel(error.code));
        return Result(std::move(error));
    }

    static Result error(ErrorCode code, const QString& message,
                       const QString& context = QString()) {
        Error err(code, message, getCategoryForErrorCode(code), context);
        return error(std::move(err));
    }

    // Query methods
    bool isSuccess() const { return std::holds_alternative<T>(m_data); }
    bool isError() const { return !isSuccess(); }

    // Value access
    const T& value() const {
        if (isError()) {
            const auto& err = std::get<Error>(m_data);
            VLOG_FATAL(err.category, QString("Attempted to access value of failed Result: %1").arg(err.message));
            throw std::runtime_error("Attempted to access value of failed Result");
        }
        return std::get<T>(m_data);
    }

    T& value() {
        if (isError()) {
            const auto& err = std::get<Error>(m_data);
            VLOG_FATAL(err.category, QString("Attempted to access value of failed Result: %1").arg(err.message));
            throw std::runtime_error("Attempted to access value of failed Result");
        }
        return std::get<T>(m_data);
    }

    // Error access
    const Error& error() const {
        if (isSuccess()) {
            throw std::runtime_error("Attempted to access error of successful Result");
        }
        return std::get<Error>(m_data);
    }

    // Convenience methods
    T valueOr(T defaultValue) const {
        return isSuccess() ? value() : defaultValue;
    }

    template<typename F>
    auto map(F&& func) -> Result<decltype(func(value()))> {
        if (isError()) {
            return Result<decltype(func(value()))>::error(error());
        }
        return Result<decltype(func(value()))>::success(func(value()));
    }

    template<typename F>
    Result<T> mapError(F&& func) {
        if (isError()) {
            return Result<T>::error(func(error()));
        }
        return *this;
    }

private:
    explicit Result(T value) : m_data(std::move(value)) {}
    explicit Result(Error error) : m_data(std::move(error)) {}
    Result() requires std::is_void_v<T> : m_data(std::monostate{}) {}
};

// Specialization for void
template<>
class Result<void> {
private:
    std::optional<Error> m_error;

public:
    static Result success() { return Result(); }

    static Result error(Error error) {
        error.log(getDefaultLogLevel(error.code));
        return Result(std::move(error));
    }

    static Result error(ErrorCode code, const QString& message,
                       const QString& context = QString()) {
        Error err(code, message, getCategoryForErrorCode(code), context);
        return error(std::move(err));
    }

    bool isSuccess() const { return !m_error.has_value(); }
    bool isError() const { return m_error.has_value(); }

    const Error& error() const {
        if (!m_error) {
            throw std::runtime_error("Attempted to access error of successful Result");
        }
        return *m_error;
    }

    template<typename F>
    Result<void> mapError(F&& func) {
        if (isError()) {
            return Result<void>::error(func(error()));
        }
        return *this;
    }

private:
    Result() = default;
    explicit Result(Error error) : m_error(std::move(error)) {}
};

} // namespace Vizion3D::Utils

// Convenience macros for creating errors with automatic location capture
#define VIZION3D_ERROR(code, message) \
    Vizion3D::Utils::Result<void>::error(code, message, QString(), __FILE__, __LINE__, __FUNCTION__)

#define VIZION3D_ERROR_WITH_CONTEXT(code, message, context) \
    Vizion3D::Utils::Result<void>::error(Vizion3D::Utils::Error(code, message, \
        Vizion3D::Utils::getCategoryForErrorCode(code), context, __FILE__, __LINE__, __FUNCTION__))

#endif // VIZION3D_RESULT_H
```

## Complete Interface Transformations

### 1. GCodeInterpreter Interface

```cpp
// include/engine/gcode_interpreter.h
class GCodeInterpreter : public QObject {
    Q_OBJECT

public:
    explicit GCodeInterpreter(QObject *parent = nullptr);
    ~GCodeInterpreter();

    // All methods now return Result<T>
    Result<void> parseGCode(const QString &gcode);
    Result<void> loadGCode(const QString &filePath);
    Result<QVector<ToolpathPoint>> getToolpath() const;
    Result<QVector<ToolpathPoint>> simulateExecution(double timeStep) const;
    Result<ToolpathPoint> processLine(const QString &line);
    Result<QMap<int, QString>> getAnnotatedGCode() const;

    // State queries remain simple
    bool isParsingInProgress() const;
    void cancelParsing();

signals:
    // Enhanced signals with structured error information
    void parsingStarted();
    void parsingProgress(int lineNumber, int totalLines);
    void parsingCompleted();
    void parsingCanceled();
    void parsingError(const Vizion3D::Utils::Error& error);  // Structured error

    // Remove old string-based error signals completely
};
```

### 2. SimulationEngine Interface

```cpp
// include/engine/interfaces/simulation_engine_interface.h
class ISimulationEngine : public QObject {
    Q_OBJECT

public:
    virtual ~ISimulationEngine() = default;

    // Core operations with Result<T> returns
    virtual Result<void> parseGCode(const QString& gcode) = 0;
    virtual Result<QVector<ToolpathPoint>> getToolpath() const = 0;
    virtual Result<void> startSimulation(double timeStep = 0.1) = 0;
    virtual Result<void> loadMachine(const QString& filePath) = 0;
    virtual Result<void> simulateMaterialRemoval(const QString& gcode, double timeStep = 0.1) = 0;
    virtual Result<void> processNextLine() = 0;

    // State queries and simple operations
    virtual void pauseSimulation() = 0;
    virtual void resumeSimulation() = 0;
    virtual void stopSimulation() = 0;
    virtual void stepSimulation() = 0;
    virtual void setSimulationSpeed(double speed) = 0;

    virtual bool isSimulationRunning() const = 0;
    virtual bool isSimulationPaused() const = 0;
    virtual double getSimulationSpeed() const = 0;
    virtual double getWorkpieceVolume() const = 0;
    virtual QStringList checkCollisions() = 0;
    virtual ToolpathPoint getCurrentPosition() const = 0;

signals:
    // State change signals
    void simulationStarted();
    void simulationPaused();
    void simulationResumed();
    void simulationStopped();
    void simulationCompleted();
    void simulationProgress(double progress);
    void simulationStep(const ToolpathPoint& position);

    // Structured error signals
    void simulationError(const Vizion3D::Utils::Error& error);
    void parsingStarted();
    void parsingProgress(int lineNumber, int totalLines);
    void parsingCompleted();
    void parsingError(const Vizion3D::Utils::Error& error);
};
```

### 3. Renderer Interface Transformations

```cpp
// include/ui/renderer.h
class Renderer : protected QOpenGLFunctions_4_1_Core {
public:
    Renderer();
    virtual ~Renderer();

    // All initialization and setup methods return Result<void>
    virtual Result<void> initialize() = 0;
    virtual Result<void> render(const QMatrix4x4& viewMatrix, const QMatrix4x4& projectionMatrix) = 0;

protected:
    // Helper methods with Result<T> returns
    Result<void> createAndBindVAO(QOpenGLVertexArrayObject& vao);
    Result<std::unique_ptr<QOpenGLShaderProgram>> createShaderProgram(
        const QString& vertexShaderSource, const QString& fragmentShaderSource);
    Result<QString> loadShaderSource(const QString& filePath);

    QOpenGLVertexArrayObject m_vao;
};

// include/ui/grid_renderer.h
class GridRenderer : public Renderer {
public:
    GridRenderer();
    ~GridRenderer() override;

    Result<void> initialize() override;
    Result<void> render(const QMatrix4x4& viewMatrix, const QMatrix4x4& projectionMatrix) override;

    // Configuration methods
    void setSize(float size);
    void setStep(float step);
    void setColors(const QVector3D& gridColor, const QVector3D& xAxisColor,
                   const QVector3D& yAxisColor, const QVector3D& zAxisColor);

private:
    Result<void> createShaderProgram();
    Result<void> setupGridBuffers();
    Result<void> setupMainAxesBuffers();
    Result<void> setupToolpathAreaBuffers();
    Result<void> generateGridVertices();
    Result<void> generateMainAxesVertices();
    Result<void> generateToolpathAreaVertices();

    // ... member variables
};

// Similar transformations for AxesRenderer, ToolpathRenderer, PositionRenderer
```

### 4. File I/O and Validation Methods

```cpp
// File operations throughout the codebase
Result<QString> loadFileContent(const QString& filePath);
Result<void> saveFileContent(const QString& filePath, const QString& content);
Result<QByteArray> loadBinaryFile(const QString& filePath);
Result<void> saveBinaryFile(const QString& filePath, const QByteArray& data);

// Validation methods
Result<void> validateGCodeSyntax(const QString& line);
Result<void> validateCoordinates(double x, double y, double z);
Result<void> validateFeedRate(double feedRate);
Result<void> validateToolNumber(int toolNumber);

// OpenGL validation
Result<void> checkOpenGLError(const QString& operation);
Result<void> validateShaderCompilation(QOpenGLShader* shader);
Result<void> validateProgramLinking(QOpenGLShaderProgram* program);
```

## Implementation Strategy

### Phase 1: Core Foundation (Week 1) ✅ COMPLETED
**Deliverables:**
- ✅ Complete `include/utils/result.h` implementation
- ✅ Helper functions in `src/utils/result.cpp`
- ✅ Comprehensive unit tests for Result<T> template
- ✅ Update CMakeLists.txt and build system

**Key Tasks:**
1. ✅ Implement Result<T> template with all specializations
2. ✅ Create ErrorCode enum with comprehensive categories
3. ✅ Implement error-to-string conversion functions
4. ✅ Create automatic logging integration
5. ✅ Write extensive unit tests covering all Result<T> operations

**Validation Results:**
- ✅ All 24 unit tests pass
- ✅ No breaking changes to existing logging system
- ✅ Result<T> template works correctly for both value and void types
- ✅ Automatic error logging integrates properly with existing log sinks
- ✅ Documentation updated to reflect enhanced logging capabilities

### Phase 2: Engine Core Transformation (Week 2)
**Deliverables:**
- Completely transformed GCodeInterpreter class
- Updated SimulationEngine implementation
- New ISimulationEngine interface
- Updated Qt signals throughout engine

**Key Tasks:**
1. Replace all `bool` returns with `Result<void>` in GCodeInterpreter
2. Replace all `nullptr` returns with `Result<T>` in GCodeInterpreter
3. Transform SimulationEngine error handling completely
4. Update all Qt signals to use structured Error objects
5. Remove all try/catch blocks in favor of Result<T> propagation

### Phase 3: Renderer System Transformation (Week 3)
**Deliverables:**
- Transformed base Renderer class
- Updated all renderer implementations (Grid, Axes, Toolpath, Position)
- Enhanced OpenGL error handling
- Updated SimulationView integration

**Key Tasks:**
1. Transform all renderer initialization methods to return Result<void>
2. Update shader compilation and linking to use Result<T>
3. Replace OpenGL error checking with Result<T> pattern
4. Update VAO and buffer creation methods
5. Transform SimulationView to handle Result<T> from renderers

### Phase 4: UI Integration (Week 4)
**Deliverables:**
- Updated MainWindow error handling
- Enhanced error display in UI
- Updated status reporting
- Comprehensive error dialogs

**Key Tasks:**
1. Update MainWindow to handle structured Error objects from signals
2. Create enhanced error display dialogs showing error codes and context
3. Update status bar to show detailed error information
4. Implement error reporting and logging UI features

### Phase 5: Testing and Validation (Week 5)
**Deliverables:**
- Comprehensive test suite for all error paths
- Integration tests for error propagation
- Performance validation
- Documentation updates

**Key Tasks:**
1. Create unit tests for every converted method
2. Test error propagation from engine to UI
3. Validate performance impact of Result<T> pattern
4. Update all documentation with new error handling patterns
5. Create developer guide for Result<T> usage

## Breaking Changes and Migration

### Removed Patterns
1. **Boolean return types** - All replaced with `Result<void>`
2. **Nullptr returns** - All replaced with `Result<T>`
3. **Exception throwing** - All replaced with `Result<T>::error()`
4. **String-based error signals** - All replaced with structured Error signals
5. **Manual error logging** - All automated through Result<T> creation

### New Patterns
1. **Consistent Result<T> returns** for all operations that can fail
2. **Structured Error objects** with codes, categories, and context
3. **Automatic error logging** when errors are created
4. **Enhanced Qt signals** carrying structured error information
5. **Centralized error categorization** and severity mapping

### API Changes
```cpp
// OLD API (completely removed)
bool parseGCode(const QString& gcode);
ToolpathPoint* processLine(const QString& line);
QOpenGLShaderProgram* createShaderProgram(const QString& vs, const QString& fs);

// NEW API (direct replacement)
Result<void> parseGCode(const QString& gcode);
Result<ToolpathPoint> processLine(const QString& line);
Result<std::unique_ptr<QOpenGLShaderProgram>> createShaderProgram(const QString& vs, const QString& fs);
```

## Integration with Current Logging System

### Seamless Integration
The Result<T> pattern integrates perfectly with the existing logging system:

1. **No changes to VLOG_* macros** - They continue to work exactly as before
2. **Automatic error logging** - Errors are logged when Result<T>::error() is called
3. **Enhanced log messages** - Include error codes and structured context
4. **Category preservation** - Uses existing logging categories (Engine, GCode, OpenGL, etc.)
5. **Level mapping** - Error codes automatically map to appropriate log levels

### Enhanced Logging Features
```cpp
// Automatic logging with error codes
auto result = parseGCode(gcode);
if (result.isError()) {
    // Error was already logged automatically with:
    // [GCodeParsingError] Failed to parse G-code: Invalid syntax at line 42

    // Additional context can be added
    VLOG_ERROR("UI", QString("User action failed: %1").arg(result.error().message));
}

// Error codes enable better log filtering and analysis
LogConfig::instance().setCategoryLogLevel("GCode", LogLevel::Warning);  // Only show warnings and above for G-code
```

## Testing Strategy

### Comprehensive Error Path Testing
```cpp
// Example test structure
class GCodeInterpreterTest : public ::testing::Test {
protected:
    void SetUp() override {
        m_interpreter = std::make_unique<GCodeInterpreter>();
    }

    std::unique_ptr<GCodeInterpreter> m_interpreter;
};

TEST_F(GCodeInterpreterTest, ParseValidGCode_ReturnsSuccess) {
    QString validGCode = "G01 X10 Y20 Z5 F100";
    auto result = m_interpreter->parseGCode(validGCode);

    EXPECT_TRUE(result.isSuccess());
    EXPECT_FALSE(result.isError());
}

TEST_F(GCodeInterpreterTest, ParseInvalidGCode_ReturnsError) {
    QString invalidGCode = "INVALID_COMMAND";
    auto result = m_interpreter->parseGCode(invalidGCode);

    EXPECT_TRUE(result.isError());
    EXPECT_EQ(result.error().code, ErrorCode::GCodeSyntaxError);
    EXPECT_EQ(result.error().category, "GCode");
    EXPECT_FALSE(result.error().message.isEmpty());
}

TEST_F(GCodeInterpreterTest, ProcessLineAfterError_PropagatesError) {
    // First cause an error
    m_interpreter->parseGCode("INVALID");

    // Then try to process a line
    auto result = m_interpreter->processLine("G01 X10");

    EXPECT_TRUE(result.isError());
    EXPECT_EQ(result.error().code, ErrorCode::SimulationInvalidState);
}
```

### Integration Testing
```cpp
class ErrorPropagationTest : public ::testing::Test {
public:
    void testErrorPropagationFromEngineToUI() {
        // Test that errors from engine reach UI through signals
        QSignalSpy errorSpy(m_engine.get(), &ISimulationEngine::simulationError);

        auto result = m_engine->parseGCode("INVALID_GCODE");
        EXPECT_TRUE(result.isError());

        // Verify signal was emitted with structured error
        EXPECT_EQ(errorSpy.count(), 1);
        auto errorArg = errorSpy.at(0).at(0).value<Vizion3D::Utils::Error>();
        EXPECT_EQ(errorArg.code, ErrorCode::GCodeSyntaxError);
    }
};
```

## Success Metrics

1. **100% error path coverage** - Every method that can fail returns Result<T>
2. **Zero inconsistent error handling** - Single pattern throughout codebase
3. **Automatic error logging** - No manual error logging required
4. **Enhanced debugging** - Structured error information with codes and context
5. **No performance regression** - Result<T> overhead < 5% in critical paths
6. **Complete Qt integration** - All signals carry structured error information
7. **Comprehensive testing** - 100% test coverage for error scenarios

## Implementation Files Required

### New Files to Create
1. `include/utils/result.h` - Core Result<T> template and Error struct
2. `src/utils/result.cpp` - Helper functions and error code mappings
3. `tests/utils/test_result.cpp` - Comprehensive Result<T> tests
4. `tests/engine/test_error_handling.cpp` - Engine error handling tests
5. `tests/ui/test_renderer_errors.cpp` - Renderer error handling tests

### Files to Modify
1. All header files in `include/engine/` - Update method signatures
2. All source files in `src/engine/` - Replace error handling implementation
3. All header files in `include/ui/` - Update renderer interfaces
4. All source files in `src/ui/` - Replace OpenGL error handling
5. `CMakeLists.txt` - Add new source files and dependencies

### Documentation Updates
1. Update all API documentation with Result<T> patterns
2. Create migration guide for developers
3. Update troubleshooting guides with error codes
4. Create best practices guide for Result<T> usage

This direct implementation creates a robust, production-ready error handling system that eliminates all inconsistencies while maintaining full integration with the existing logging infrastructure and Qt architecture.