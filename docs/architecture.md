# Qt Migration Architecture

## Overview

This document outlines the architecture of the Qt migration for the Vizion3D CNC simulation application. The architecture follows a hierarchical ownership model with a direct UI → Engine relationship, using interfaces for abstraction.

## Architecture Principles

1. **Hierarchical Ownership Model**
   - MainWindow is the sole owner of the SimulationEngine
   - UI components observe the engine through signal/slot connections
   - All engine control operations are handled exclusively by MainWindow

2. **Direct UI → Engine Relationship**
   - ISimulationEngine serves as the abstraction boundary
   - No intermediate mediator layers
   - Clean separation through interfaces

3. **Interface-Based Design**
   - Interfaces define contracts between components
   - Concrete implementations can be swapped without affecting clients
   - Facilitates testing and future extensibility

## Component Responsibilities

### MainWindow
- Application-level UI coordination
- G-code editing and management
- Simulation control flow (start, pause, stop, step)
- User feedback (status messages, errors, dialogs)
- Settings management
- **Owns and controls the SimulationEngine**

### SimulationView
- 3D visualization of toolpath, workpiece, and machine
- Camera control (rotation, panning, zooming)
- Visual feedback of current tool position
- OpenGL (version 4.1) rendering
- **Observes the SimulationEngine without controlling it**

### ISimulationEngine
- Defines the contract for simulation functionality
- Provides methods for controlling simulation
- Emits signals for simulation events
- Serves as the abstraction boundary

### SimulationEngine
- Implements the ISimulationEngine interface
- Adapts the original C++ engine to use Qt's paradigms
- Manages the simulation state
- Processes G-code and generates toolpaths
- Controls the simulation execution

### EngineFactory
- Creates instances of the SimulationEngine
- Provides a factory pattern for dependency injection
- Facilitates testing with mock implementations

## Communication Flow

1. **UI → Engine Communication**
   - MainWindow calls methods on ISimulationEngine
   - All control operations go through MainWindow
   - SimulationView only observes the engine through signals

2. **Engine → UI Communication**
   - Engine emits signals for events (start, pause, step, etc.)
   - UI components connect to these signals
   - No direct callbacks from engine to UI

## Implementation Details

### Signal/Slot Connections

```cpp
// In MainWindow::initializeSimulationEngine()
connect(m_simulationEngine, &ISimulationEngine::simulationStarted, 
        this, &MainWindow::onSimulationStarted);
// ... other connections

// In SimulationView::connectToEngine()
m_connections << connect(engine, &ISimulationEngine::simulationStep,
                        this, &SimulationView::onSimulationStep);
// ... other connections
```

### Hierarchical Ownership

```cpp
// MainWindow owns the engine
m_simulationEngine = m_engineFactory->createSimulationEngine(this);

// SimulationView only connects to the engine
m_simulationView->connectToEngine(m_simulationEngine);
```

### Interface-Based Design

```cpp
// Interface definition
class ISimulationEngine : public QObject {
    Q_OBJECT
public:
    virtual bool initialize() = 0;
    virtual bool parseGCode(const QString& gcode) = 0;
    // ... other methods
signals:
    void simulationStarted();
    // ... other signals
};

// Concrete implementation
class SimulationEngine : public ISimulationEngine {
    // Implementation details
};
```

## Benefits of This Architecture

1. **Clear Ownership and Responsibility**
   - Each component has a well-defined role
   - MainWindow is the clear owner of the engine
   - SimulationView focuses solely on visualization

2. **Reduced Coupling**
   - Components are loosely coupled through interfaces and signals
   - SimulationView doesn't depend on the concrete engine implementation
   - Changes to one component don't affect others

3. **Testability**
   - Components can be tested in isolation
   - Mock implementations can be used for testing
   - Signal/slot connections can be verified

4. **Extensibility**
   - New UI components can be added without modifying existing ones
   - Engine implementation can be changed without affecting UI
   - New features can be added through interface extensions

5. **Maintainability**
   - Clean separation of concerns
   - Clear communication paths
   - Reduced complexity

## Architectural Decisions

### Why Direct UI → Engine Relationship?

We chose a direct UI → Engine relationship over a mediator pattern because:

1. It provides the right level of abstraction for our needs
2. It leverages Qt's natural signal/slot mechanism
3. It's simpler to understand and maintain
4. It avoids unnecessary indirection

### Why Hierarchical Ownership?

We implemented a hierarchical ownership model where:

1. MainWindow owns and controls the engine
2. SimulationView only observes the engine

This approach:
1. Clarifies responsibility boundaries
2. Reduces coupling between components
3. Simplifies the mental model of the application
4. Follows Qt's parent-child ownership model

## Future Considerations

As the application grows, we may need to revisit some architectural decisions:

1. **Mediator Pattern**: If UI coordination becomes complex, a mediator might be beneficial
2. **Multiple Engines**: If we need to support multiple simulation engines, a factory registry might be needed
3. **Complex State Management**: If simulation state becomes complex, a dedicated state manager might be appropriate

However, the current architecture provides a solid foundation that can evolve as needed.
