# Vizion3D - Advanced CNC Simulation Engine

[![Build Status](https://github.com/yourusername/Vizion3D/workflows/CI/badge.svg)](https://github.com/yourusername/Vizion3D/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Qt Version](https://img.shields.io/badge/Qt-6.2+-green.svg)](https://www.qt.io/)
[![OpenGL](https://img.shields.io/badge/OpenGL-4.1_Core-blue.svg)](https://www.opengl.org/)

Vizion3D is a high-performance, real-time CNC simulation application built with modern C++ and Qt6. It provides comprehensive G-code interpretation, 3D toolpath visualization, and accurate CNC machining simulation with smooth tool movement animation.

## 🚀 Key Features

### ✅ Currently Implemented
- **Real-time G-code Processing**: Line-by-line interpretation with live simulation
- **3D Toolpath Visualization**: Modern OpenGL 4.1 Core Profile rendering with shader-based graphics
- **Smooth Tool Animation**: Interpolated tool movement between points for realistic CNC behavior
- **Comprehensive G-code Support**: G0/G1 moves, G2/G3 arcs, absolute/relative positioning (G90/G91)
- **Advanced UI Components**: Project tree, G-code editor with syntax highlighting, debug console
- **Simulation Controls**: Start, pause, stop, step-through, and variable speed control
- **Professional Error Handling**: Result<T> pattern with automatic logging integration
- **Comprehensive Testing**: Qt Test framework with unit and integration tests
- **Code Quality Assurance**: Clang-tidy integration, pre-commit hooks, CI/CD pipeline

### 🔄 In Development
- **Material Removal Simulation**: 3D stock material cutting visualization
- **Multi-axis Support**: 4th and 5th axis machining simulation
- **Advanced Tooling**: Tool library and automatic tool change simulation
- **Machine Kinematics**: Realistic machine movement constraints

## 🏗️ Architecture

Vizion3D implements a clean two-layer architecture with Qt6 UI components communicating with a high-performance C++ simulation engine through signals/slots. The design emphasizes hierarchical ownership, interface-based abstraction, and thread-safe operations.

For detailed architectural information, component responsibilities, and communication patterns, see [docs/architecture.md](docs/architecture.md).

## 🛠️ Technology Stack

- **Qt 6.2+** - Cross-platform GUI framework with OpenGL integration
- **OpenGL 4.1 Core Profile** - Modern shader-based 3D rendering
- **C++17** - Modern C++ with RAII and smart pointers
- **CMake 3.16+** - Cross-platform build system
- **OpenCASCADE 7.8.0** - Advanced 3D geometry operations (optional)

For complete technology requirements and platform-specific setup instructions, see [docs/setup.md](docs/setup.md).

## 📋 Prerequisites

- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 20.04+)
- **Graphics**: OpenGL 4.1 compatible GPU
- **Development Tools**: Qt 6.2+, C++17 compiler, CMake 3.16+

For complete system requirements, dependency installation, and platform-specific setup instructions, see [docs/setup.md](docs/setup.md).

## ⚡ Quick Start

```bash
# Clone and build
git clone https://github.com/yourusername/Vizion3D.git
cd Vizion3D
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --parallel

# Run the application
./bin/Vizion3D  # Linux/macOS
.\bin\Release\Vizion3D.exe  # Windows
```

For detailed build instructions, troubleshooting, and development setup, see [docs/setup.md](docs/setup.md).

## 🎯 Usage

### Loading and Simulating G-code

1. **Launch Vizion3D** and you'll see the main interface with simulation view and controls
2. **Load G-code** using File → Open or paste directly into the G-code Editor tab
3. **Start Simulation** using the play button or Ctrl+R
4. **Control Playback** with pause, stop, step, and speed controls
5. **View 3D Toolpath** in real-time as the simulation progresses

### Sample G-code Files

Vizion3D includes sample files in the `samples/` directory:
- `circle.nc` - Simple circular toolpath
- `square.nc` - Basic square cutting pattern~
- `circle_arc.nc` - Arc interpolation demonstration

### Supported G-code Commands

| Command | Description | Status |
|---------|-------------|--------|
| `G0` | Rapid positioning | ✅ Implemented |
| `G1` | Linear interpolation | ✅ Implemented |
| `G2/G3` | Circular interpolation | ✅ Implemented |
| `G90` | Absolute positioning | ✅ Implemented |
| `G91` | Relative positioning | ✅ Implemented |
| `F` | Feed rate | ✅ Implemented |
| `M30` | Program end | ✅ Implemented |

## 🧪 Development Setup

### Getting Started as a Contributor

1. **Fork and clone** the repository
2. **Install development tools** (pre-commit, clang-tidy)
3. **Set up pre-commit hooks** for code quality
4. **Build with debug configuration** for development
5. **Run tests** to verify setup

### Code Quality Standards

Vizion3D maintains high code quality through modern C++17 practices, Result<T> error handling, VLOG_* logging, comprehensive testing, and automated static analysis.

For detailed development environment setup, coding standards, and contribution guidelines, see:
- [docs/setup.md](docs/setup.md) - Development environment setup
- [docs/development/](docs/development/) - Development guides and workflows
- [docs/project_structure.md](docs/project_structure.md) - Project organization and standards

## 🧪 Testing

Vizion3D includes comprehensive testing using Qt Test framework with coverage for G-code interpretation, error handling, simulation engine, OpenGL rendering, and UI components.

```bash
# Run all tests
cd build && ctest --output-on-failure

# Run specific test suites
./bin/gcode_interpreter_test
./bin/result_test
```

For detailed testing information, writing tests, and test coverage, see the test documentation in [tests/](tests/).

## 🔍 Code Quality & CI/CD

Vizion3D employs automated quality assurance including pre-commit hooks, GitHub Actions CI with multi-platform build verification, static analysis with clang-tidy, and comprehensive test coverage.

```bash
# Run quality checks locally
pre-commit run --all-files
./scripts/pre-commit/clang-tidy-precommit-hook.sh
```

For complete information on automated quality systems, CI/CD pipeline, and local development workflow, see [docs/development/automated_code_quality.md](docs/development/automated_code_quality.md).

## 📚 Documentation

Comprehensive documentation is available in the `docs/` directory:

| Category | Key Documents |
|----------|---------------|
| **Getting Started** | [Setup Guide](docs/setup.md) |
| **Architecture** | [System Design](docs/architecture.md), [Project Structure](docs/project_structure.md) |
| **Features** | [Logging System](docs/features/logging_system.md), [Error Handling](docs/implementation/direct_error_handling_implementation.md) |
| **Development** | [Code Quality](docs/development/automated_code_quality.md), [Git Workflow](docs/development/git_precommit_workflow.md) |

## 🤝 Contributing

We welcome contributions! Please:

1. **Fork** the repository and create a feature branch
2. **Follow** our development setup and coding standards
3. **Write tests** for new functionality
4. **Ensure** all quality checks pass
5. **Submit** a pull request with clear description

For detailed contribution guidelines, coding standards, and development workflow, see [docs/development/](docs/development/).

## 🐛 Troubleshooting

### Quick Fixes

- **Qt not found**: `cmake .. -DCMAKE_PREFIX_PATH=/path/to/qt6`
- **OpenGL issues**: Ensure OpenGL 4.1 support and updated drivers
- **Build errors**: Check CMake output for missing dependencies

For comprehensive troubleshooting, platform-specific issues, and detailed solutions, see [docs/setup.md](docs/setup.md#troubleshooting).

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Qt Framework Team** - For the excellent cross-platform framework
- **OpenCASCADE Community** - For advanced 3D geometry capabilities
- **Khronos Group** - For OpenGL specifications and documentation
- **Contributors** - All developers who have contributed to this project

---

**Vizion3D** - Bringing precision CNC simulation to modern development practices.
