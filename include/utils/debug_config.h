#ifndef VIZION3D_DEBUG_CONFIG_H
#define VIZION3D_DEBUG_CONFIG_H

/**
 * @file debug_config.h
 * @brief Compile-time configuration for debugging and logging
 *
 * This file contains compile-time configuration options for debugging and logging.
 * It is included by all source files that need to use debugging features.
 */

// Define build modes
#if (!defined(NDEBUG) || defined(VIZION3D_FORCE_DEBUG)) && !defined(VIZION3D_DEBUG_MODE)
    /**
     * @brief Debug build mode
     *
     * This is defined when building in debug mode or when VIZION3D_FORCE_DEBUG is defined,
     * but only if it hasn't already been defined by CMake.
     */
    #define VIZION3D_DEBUG_MODE
#endif

// Logging configuration
#if defined(VIZION3D_DEBUG_MODE) && !defined(VIZION3D_DISABLE_LOGGING)
    /**
     * @brief Enable detailed logging
     *
     * This is defined when building in debug mode and logging is not explicitly disabled.
     */
    #define VIZION3D_ENABLE_DETAILED_LOGGING
#endif

// Performance profiling
#if defined(VIZION3D_DEBUG_MODE) && !defined(VIZION3D_DISABLE_PROFILING)
    /**
     * @brief Enable performance profiling
     *
     * This is defined when building in debug mode and profiling is not explicitly disabled.
     */
    #define VIZION3D_ENABLE_PROFILING
#endif

// Assertion configuration
#if defined(VIZION3D_DEBUG_MODE) && !defined(VIZION3D_DISABLE_ASSERTIONS)
    /**
     * @brief Enable assertions
     *
     * This is defined when building in debug mode and assertions are not explicitly disabled.
     */
    #define VIZION3D_ENABLE_ASSERTIONS
#endif

// Module-specific debug flags
#if defined(VIZION3D_DEBUG_MODE)
    /**
     * @brief Enable OpenGL debugging
     *
     * This is defined when building in debug mode.
     */
    #define VIZION3D_DEBUG_OPENGL

    /**
     * @brief Enable G-code interpreter debugging
     *
     * This is defined when building in debug mode.
     */
    #define VIZION3D_DEBUG_GCODE

    /**
     * @brief Enable simulation engine debugging
     *
     * This is defined when building in debug mode.
     */
    #define VIZION3D_DEBUG_SIMULATION

    /**
     * @brief Enable UI debugging
     *
     * This is defined when building in debug mode.
     */
    #define VIZION3D_DEBUG_UI
#endif

// Convenience macros for conditional compilation
#ifdef VIZION3D_DEBUG_MODE
    /**
     * @brief Executes code only in debug mode
     *
     * This macro executes the given code only when building in debug mode.
     */
    #define VIZION3D_DEBUG_ONLY(x) x
#else
    #define VIZION3D_DEBUG_ONLY(x)
#endif

#ifdef VIZION3D_ENABLE_ASSERTIONS
    #include <cassert>
    /**
     * @brief Assertion macro
     *
     * This macro asserts that the given condition is true.
     * It is only active when assertions are enabled.
     */
    #define VIZION3D_ASSERT(condition) assert(condition)

    /**
     * @brief Assertion macro with message
     *
     * This macro asserts that the given condition is true and outputs a message if it fails.
     * It is only active when assertions are enabled.
     */
    #define VIZION3D_ASSERT_MSG(condition, message) assert((condition) && (message))
#else
    #define VIZION3D_ASSERT(condition)
    #define VIZION3D_ASSERT_MSG(condition, message)
#endif

#ifdef VIZION3D_ENABLE_PROFILING
    #include <QElapsedTimer>
    #include <QString>
    #include "utils/logger.h"

    /**
     * @brief Profiling macro
     *
     * This macro measures the execution time of a block of code and logs it.
     * It is only active when profiling is enabled.
     */
    #define VIZION3D_PROFILE_BLOCK(name) \
        QElapsedTimer __profiler_timer; \
        __profiler_timer.start(); \
        auto __profiler_guard = qScopeGuard([&](){ \
            qint64 __elapsed = __profiler_timer.elapsed(); \
            Vizion3D::Utils::Logger::instance().log( \
                Vizion3D::Utils::LogLevel::Debug, \
                "Profiling", \
                QString("Block '%1' took %2 ms").arg(name).arg(__elapsed), \
                __FILE__, __LINE__, __FUNCTION__ \
            ); \
        });

    /**
     * @brief Profiling macro for functions
     *
     * This macro measures the execution time of a function and logs it.
     * It is only active when profiling is enabled.
     */
    #define VIZION3D_PROFILE_FUNCTION() \
        VIZION3D_PROFILE_BLOCK(__FUNCTION__)
#else
    #define VIZION3D_PROFILE_BLOCK(name)
    #define VIZION3D_PROFILE_FUNCTION()
#endif

#endif // VIZION3D_DEBUG_CONFIG_H
