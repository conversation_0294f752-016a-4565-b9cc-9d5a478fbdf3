#ifndef VIZION3D_LOG_CONFIG_H
#define VIZION3D_LOG_CONFIG_H

#include "utils/logger.h"
#include <QObject>
#include <QSettings>
#include <QMap>
#include <QString>

namespace Vizion3D {
namespace Utils {

/**
 * @brief The LogConfig class manages logging configuration
 *
 * This class is the single source of truth for all logging configuration,
 * including log levels for different categories and sink configurations.
 * It provides methods for saving and loading configuration from a file,
 * and applies configuration changes to the Logger instance.
 */
class LogConfig : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Gets the singleton instance of the LogConfig
     * @return The LogConfig instance
     */
    static LogConfig& instance();

    /**
     * @brief Initializes the logging configuration
     * @param appName The name of the application
     * @param configFilePath The path to the configuration file (optional)
     */
    void initialize(const QString& appName, const QString& configFilePath = QString());

    /**
     * @brief Sets the global log level
     * @param level The log level to set
     */
    void setGlobalLogLevel(LogLevel level);

    /**
     * @brief Gets the global log level
     * @return The global log level
     */
    LogLevel globalLogLevel() const;

    /**
     * @brief Sets the log level for a specific category
     * @param category The category
     * @param level The log level to set
     */
    void setCategoryLogLevel(const QString& category, LogLevel level);

    /**
     * @brief Gets the log level for a specific category
     * @param category The category
     * @return The log level for the category, or the global log level if the category is not found
     */
    LogLevel categoryLogLevel(const QString& category) const;

    /**
     * @brief Gets all category log levels
     * @return A map of category names to log levels
     */
    QMap<QString, LogLevel> categoryLogLevels() const;

    /**
     * @brief Enables or disables logging to a file
     * @param enabled Whether to enable file logging
     * @param filePath The path to the log file (optional)
     * @param maxSizeBytes The maximum size of the log file in bytes before rotation (optional)
     * @param maxBackupCount The maximum number of backup files to keep (optional)
     */
    void setFileLoggingEnabled(bool enabled,
                              const QString& filePath = QString(),
                              qint64 maxSizeBytes = 10 * 1024 * 1024,
                              int maxBackupCount = 5);

    /**
     * @brief Checks if file logging is enabled
     * @return True if file logging is enabled, false otherwise
     */
    bool isFileLoggingEnabled() const;

    /**
     * @brief Gets the path to the log file
     * @return The path to the log file
     */
    QString logFilePath() const;

    /**
     * @brief Gets the maximum size of the log file in bytes before rotation
     * @return The maximum size of the log file in bytes
     */
    qint64 maxLogFileSizeBytes() const;

    /**
     * @brief Gets the maximum number of backup files to keep
     * @return The maximum number of backup files
     */
    int maxLogBackupCount() const;

    /**
     * @brief Enables or disables logging to the console
     * @param enabled Whether to enable console logging
     */
    void setConsoleLoggingEnabled(bool enabled);

    /**
     * @brief Checks if console logging is enabled
     * @return True if console logging is enabled, false otherwise
     */
    bool isConsoleLoggingEnabled() const;

    /**
     * @brief Loads configuration from a file
     * @param filePath The path to the configuration file
     */
    void loadConfiguration(const QString& filePath);

    /**
     * @brief Saves configuration to a file
     * @param filePath The path to the configuration file
     */
    void saveConfiguration(const QString& filePath);

    /**
     * @brief Gets the default configuration file path
     * @return The default configuration file path
     */
    QString defaultConfigFilePath() const;

    /**
     * @brief Applies the current configuration to the logger
     *
     * This method updates the Logger instance with the current configuration settings.
     * It is called automatically when configuration changes are made through this class.
     */
    void applyConfiguration();

    // Prevent copying
    LogConfig(const LogConfig&) = delete;
    LogConfig& operator=(const LogConfig&) = delete;

    // Virtual destructor for proper cleanup
    ~LogConfig() override;

signals:
    /**
     * @brief Signal emitted when the configuration changes
     */
    void configurationChanged();

private:
    LogConfig();

    QString m_appName;
    QString m_configFilePath;
    LogLevel m_globalLogLevel;
    QMap<QString, LogLevel> m_categoryLogLevels;
    bool m_fileLoggingEnabled;
    QString m_logFilePath;
    qint64 m_maxLogFileSizeBytes;
    int m_maxLogBackupCount;
    bool m_consoleLoggingEnabled;
};

} // namespace Utils
} // namespace Vizion3D

#endif // VIZION3D_LOG_CONFIG_H
