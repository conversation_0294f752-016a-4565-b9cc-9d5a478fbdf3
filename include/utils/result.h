#ifndef VIZION3D_RESULT_H
#define VIZION3D_RESULT_H

#include "utils/logger.h"
#include <QString>
#include <QDebug>
#include <variant>
#include <optional>
#include <memory>
#include <type_traits>

namespace Vizion3D::Utils {

/**
 * @brief Enumeration of error codes categorized by functional area
 *
 * Error codes are organized in ranges to allow for easy categorization:
 * - 0: Success
 * - 1000-1999: G-code Processing Errors
 * - 2000-2999: Simulation Engine Errors
 * - 3000-3999: OpenGL Rendering Errors
 * - 4000-4999: File I/O Errors
 * - 5000-5999: Memory and Resource Errors
 * - 6000-6999: Threading Errors
 * - 7000-7999: Validation Errors
 * - 8000-8999: System Errors
 */
enum class ErrorCode {
    // Success
    Success = 0,

    // G-code Processing Errors (1000-1999)
    GCodeParsingError = 1000,
    GCodeSyntaxError = 1001,
    GCodeFileNotFound = 1002,
    GCodeInvalidCommand = 1003,
    GCodeInvalidCoordinate = 1004,
    GCodeUnsupportedFeature = 1005,
    GCodeInvalidFeedRate = 1006,
    GCodeInvalidToolNumber = 1007,
    GCodeInvalidSpindleSpeed = 1008,
    GCodeMissingParameter = 1009,
    GCodeProcessingError = 1010,
    EndOfProgram = 1011,

    // Simulation Engine Errors (2000-2999)
    SimulationNotInitialized = 2000,
    SimulationAlreadyRunning = 2001,
    SimulationInvalidState = 2002,
    SimulationNoGCode = 2003,
    SimulationThreadError = 2004,
    SimulationStepError = 2005,
    SimulationTimeoutError = 2006,
    SimulationCancelled = 2007,
    SimulationStartFailed = 2008,
    NotInitialized = 2009,
    InitializationFailed = 2010,

    // OpenGL Rendering Errors (3000-3999)
    OpenGLContextError = 3000,
    OpenGLShaderCompileError = 3001,
    OpenGLShaderLinkError = 3002,
    OpenGLBufferError = 3003,
    OpenGLVAOError = 3004,
    OpenGLTextureError = 3005,
    OpenGLFramebufferError = 3006,
    OpenGLUniformError = 3007,

    // File I/O Errors (4000-4999)
    FileNotFound = 4000,
    FilePermissionDenied = 4001,
    FileCorrupted = 4002,
    FileWriteError = 4003,
    FileReadError = 4004,
    FileFormatError = 4005,
    DirectoryNotFound = 4006,
    DirectoryCreationFailed = 4007,

    // Memory and Resource Errors (5000-5999)
    OutOfMemory = 5000,
    ResourceAllocationFailed = 5001,
    ResourceNotAvailable = 5002,
    ResourceLimitExceeded = 5003,
    ResourceLeakDetected = 5004,

    // Threading Errors (6000-6999)
    ThreadCreationFailed = 6000,
    ThreadSynchronizationError = 6001,
    ThreadTimeoutError = 6002,
    ThreadDeadlockDetected = 6003,
    ThreadPoolExhausted = 6004,

    // Validation Errors (7000-7999)
    InvalidArgument = 7000,
    InvalidState = 7001,
    InvalidConfiguration = 7002,
    InvalidRange = 7003,
    InvalidFormat = 7004,
    InvalidOperation = 7005,

    // System Errors (8000-8999)
    InternalError = 8000,
    NotImplemented = 8001,
    OperationCancelled = 8002,
    OperationTimeout = 8003,
    NetworkError = 8004,
    DatabaseError = 8005
};

/**
 * @brief Structured error information with automatic logging integration
 *
 * The Error struct encapsulates all information about an error including
 * the error code, message, category, and source location. It provides
 * automatic logging integration with the existing logging system.
 */
struct Error {
    ErrorCode code;
    QString message;
    QString category;
    QString file;
    int line;
    QString function;
    QString context;  // Additional context information

    /**
     * @brief Constructs an Error with automatic category assignment
     * @param code The error code
     * @param message The error message
     * @param category The error category (auto-assigned if empty)
     * @param context Additional context information
     * @param file Source file where error occurred
     * @param line Line number where error occurred
     * @param function Function where error occurred
     */
    Error(ErrorCode code, const QString& message, const QString& category = QString(),
          const QString& context = QString(),
          const char* file = __FILE__, int line = __LINE__, const char* function = nullptr);

    /**
     * @brief Logs the error using the existing logging system
     * @param level The log level to use (auto-determined if not specified)
     * @return Reference to this Error for method chaining
     */
    Error& log(LogLevel level = LogLevel::Error);
};

// Forward declarations of helper functions
QString errorCodeToString(ErrorCode code);
LogLevel getDefaultLogLevel(ErrorCode code);
QString getCategoryForErrorCode(ErrorCode code);

/**
 * @brief Result template for operations that can succeed with a value or fail with an error
 * @tparam T The type of the success value
 */
template<typename T>
class Result {
private:
    std::variant<T, Error> m_data;

public:
    // Success constructors
    static Result success(T value) { return Result(std::move(value)); }

    // Error constructors with automatic logging
    static Result error(Error error) {
        error.log();  // Automatic logging when error is created
        return Result(std::move(error));
    }

    static Result error(ErrorCode code, const QString& message,
                       const QString& context = QString()) {
        Error err(code, message, QString(), context);
        return error(std::move(err));
    }

    // Query methods
    bool isSuccess() const { return std::holds_alternative<T>(m_data); }
    bool isError() const { return !isSuccess(); }

    // Value access
    const T& value() const {
        if (isError()) {
            const auto& err = std::get<Error>(m_data);
            #ifdef QT_TESTLIB_LIB
                // In test mode, just throw without logging
                throw std::runtime_error("Attempted to access value of failed Result");
            #else
                VLOG_FATAL(err.category, QString("Attempted to access value of failed Result: %1").arg(err.message));
                throw std::runtime_error("Attempted to access value of failed Result");
            #endif
        }
        return std::get<T>(m_data);
    }

    T& value() {
        if (isError()) {
            const auto& err = std::get<Error>(m_data);
            #ifdef QT_TESTLIB_LIB
                // In test mode, just throw without logging
                throw std::runtime_error("Attempted to access value of failed Result");
            #else
                VLOG_FATAL(err.category, QString("Attempted to access value of failed Result: %1").arg(err.message));
                throw std::runtime_error("Attempted to access value of failed Result");
            #endif
        }
        return std::get<T>(m_data);
    }

    // Error access
    const Error& error() const {
        if (isSuccess()) {
            throw std::runtime_error("Attempted to access error of successful Result");
        }
        return std::get<Error>(m_data);
    }

    // Convenience methods
    T valueOr(T defaultValue) const {
        return isSuccess() ? value() : defaultValue;
    }

    template<typename F>
    auto map(F&& func) -> Result<decltype(func(value()))> {
        if (isError()) {
            return Result<decltype(func(value()))>::error(error());
        }
        return Result<decltype(func(value()))>::success(func(value()));
    }

    template<typename F>
    Result<T> mapError(F&& func) {
        if (isError()) {
            return Result<T>::error(func(error()));
        }
        return *this;
    }

private:
    explicit Result(T value) : m_data(std::move(value)) {}
    explicit Result(Error error) : m_data(std::move(error)) {}
};

/**
 * @brief Specialization of Result for void operations
 */
template<>
class Result<void> {
private:
    std::optional<Error> m_error;

public:
    static Result success() { return Result(); }

    static Result error(Error error) {
        error.log();  // Automatic logging when error is created
        return Result(std::move(error));
    }

    static Result error(ErrorCode code, const QString& message,
                       const QString& context = QString()) {
        Error err(code, message, QString(), context);
        return error(std::move(err));
    }

    bool isSuccess() const { return !m_error.has_value(); }
    bool isError() const { return m_error.has_value(); }

    const Error& error() const {
        if (!m_error) {
            throw std::runtime_error("Attempted to access error of successful Result");
        }
        return *m_error;
    }

    template<typename F>
    Result<void> mapError(F&& func) {
        if (isError()) {
            return Result<void>::error(func(error()));
        }
        return *this;
    }

private:
    Result() = default;
    explicit Result(Error error) : m_error(std::move(error)) {}
};

} // namespace Vizion3D::Utils

// Convenience macros for creating errors with automatic location capture
#define VIZION3D_ERROR(code, message) \
    Vizion3D::Utils::Result<void>::error(code, message)

#define VIZION3D_ERROR_WITH_CONTEXT(code, message, context) \
    Vizion3D::Utils::Result<void>::error(code, message, context)

#endif // VIZION3D_RESULT_H
