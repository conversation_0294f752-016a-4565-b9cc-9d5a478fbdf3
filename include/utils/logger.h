#ifndef VIZION3D_LOGGER_H
#define VIZION3D_LOGGER_H

#include <QString>
#include <QFile>
#include <QTextStream>
#include <QDateTime>
#include <QMutex>
#include <QDebug>
#include <QSettings>
#include <QDir>
#include <QThread>
#include <QCoreApplication>
#include <QStandardPaths>
#include <functional>
#include <memory>
#include <unordered_map>

namespace Vizion3D {
namespace Utils {

// Forward declaration to break circular dependency
class LogConfig;

/**
 * @brief Enumeration of log severity levels
 */
enum class LogLevel {
    Trace,   ///< Detailed tracing information
    Debug,   ///< Debugging information
    Info,    ///< Informational messages
    Warning, ///< Warning messages
    Error,   ///< Error messages
    Fatal    ///< Critical errors that cause the application to terminate
};

/**
 * @brief Converts a LogLevel to a string representation
 * @param level The log level to convert
 * @return String representation of the log level
 */
QString logLevelToString(LogLevel level);

/**
 * @brief Converts a string to a LogLevel
 * @param levelStr The string to convert
 * @return The corresponding LogLevel, or LogLevel::Info if the string is invalid
 */
LogLevel stringToLogLevel(const QString& levelStr);

/**
 * @brief Interface for log sinks
 *
 * A log sink is a destination for log messages, such as a file, console, or UI widget.
 */
class ILogSink {
public:
    virtual ~ILogSink() = default;

    /**
     * @brief Writes a log message to the sink
     * @param timestamp The timestamp of the log message
     * @param level The severity level of the log message
     * @param category The category of the log message
     * @param message The log message
     * @param file The source file where the log message was generated
     * @param line The line number where the log message was generated
     * @param function The function where the log message was generated
     * @param threadId The ID of the thread that generated the log message
     */
    virtual void write(const QDateTime& timestamp,
                      LogLevel level,
                      const QString& category,
                      const QString& message,
                      const QString& file,
                      int line,
                      const QString& function,
                      Qt::HANDLE threadId) = 0;

    /**
     * @brief Flushes any buffered log messages
     */
    virtual void flush() = 0;
};

/**
 * @brief Console log sink that outputs to stdout/stderr
 */
class ConsoleLogSink : public ILogSink {
public:
    void write(const QDateTime& timestamp,
              LogLevel level,
              const QString& category,
              const QString& message,
              const QString& file,
              int line,
              const QString& function,
              Qt::HANDLE threadId) override;

    void flush() override;
};

/**
 * @brief File log sink that outputs to a file
 */
class FileLogSink : public ILogSink {
public:
    /**
     * @brief Constructs a FileLogSink
     * @param filePath The path to the log file
     * @param maxSizeBytes The maximum size of the log file in bytes before rotation
     * @param maxBackupCount The maximum number of backup files to keep
     */
    FileLogSink(const QString& filePath, qint64 maxSizeBytes = 10 * 1024 * 1024, int maxBackupCount = 5);
    ~FileLogSink() override;

    void write(const QDateTime& timestamp,
              LogLevel level,
              const QString& category,
              const QString& message,
              const QString& file,
              int line,
              const QString& function,
              Qt::HANDLE threadId) override;

    void flush() override;

    // Accessor methods for Logger class
    QString filePath() const { return m_filePath; }
    qint64 maxSizeBytes() const { return m_maxSizeBytes; }
    int maxBackupCount() const { return m_maxBackupCount; }

private:
    QString m_filePath;
    qint64 m_maxSizeBytes;
    int m_maxBackupCount;
    QFile m_file;
    QTextStream m_stream;
    QMutex m_mutex;

    bool openFile();
    void rotateLogFiles();
};

/**
 * @brief Custom Qt message handler that redirects Qt's logging to our system
 * @param type The type of message
 * @param context The context of the message
 * @param msg The message
 */
void qtMessageHandler(QtMsgType type, const QMessageLogContext& context, const QString& msg);

/**
 * @brief The Logger class provides a centralized logging system
 *
 * This class manages log sinks and provides methods for logging messages at different severity levels.
 * All configuration is delegated to the LogConfig class, which serves as the single source of truth
 * for logging configuration.
 */
class Logger {
public:
    /**
     * @brief Gets the singleton instance of the Logger
     * @return The Logger instance
     */
    static Logger& instance();

    /**
     * @brief Initializes the logging system
     * @param appName The name of the application
     * @param configFilePath The path to the configuration file (optional)
     */
    void initialize(const QString& appName, const QString& configFilePath = QString());

    /**
     * @brief Initializes the logging system without installing the Qt message handler
     *
     * This method is useful to avoid potential deadlocks during initialization.
     * After calling this method, you should manually install the Qt message handler
     * using qInstallMessageHandler(qtMessageHandler) when it's safe to do so.
     *
     * @param appName The name of the application
     * @param configFilePath The path to the configuration file (optional)
     */
    void initializeWithoutMessageHandler(const QString& appName, const QString& configFilePath = QString());

    /**
     * @brief Shuts down the logging system
     */
    void shutdown();

    /**
     * @brief Adds a log sink
     * @param sink The log sink to add
     */
    void addSink(const std::shared_ptr<ILogSink>& sink);

    /**
     * @brief Removes a log sink
     * @param sink The log sink to remove
     */
    void removeSink(const std::shared_ptr<ILogSink>& sink);

    /**
     * @brief Sets the global log level
     * @param level The log level to set
     * @note This method is provided for backward compatibility. New code should use LogConfig.
     */
    void setGlobalLogLevel(LogLevel level);

    /**
     * @brief Gets the global log level
     * @return The global log level
     * @note This method delegates to LogConfig for the single source of truth.
     */
    LogLevel globalLogLevel() const;

    /**
     * @brief Sets the log level for a specific category
     * @param category The category
     * @param level The log level to set
     * @note This method is provided for backward compatibility. New code should use LogConfig.
     */
    void setCategoryLogLevel(const QString& category, LogLevel level);

    /**
     * @brief Gets the log level for a specific category
     * @param category The category
     * @return The log level for the category, or the global log level if the category is not found
     * @note This method delegates to LogConfig for the single source of truth.
     */
    LogLevel categoryLogLevel(const QString& category) const;

    /**
     * @brief Logs a message
     * @param level The severity level of the message
     * @param category The category of the message
     * @param message The message to log
     * @param file The source file where the message was generated
     * @param line The line number where the message was generated
     * @param function The function where the message was generated
     */
    void log(LogLevel level,
            const QString& category,
            const QString& message,
            const QString& file = QString(),
            int line = 0,
            const QString& function = QString());

    /**
     * @brief Flushes all log sinks
     */
    void flush();

    /**
     * @brief Loads configuration from a file
     * @param filePath The path to the configuration file
     * @note This method is provided for backward compatibility. New code should use LogConfig.
     */
    void loadConfiguration(const QString& filePath);

    /**
     * @brief Saves configuration to a file
     * @param filePath The path to the configuration file
     * @note This method is provided for backward compatibility. New code should use LogConfig.
     */
    void saveConfiguration(const QString& filePath);

    /**
     * @brief Enables or disables logging to a file
     * @param enabled Whether to enable file logging
     * @param filePath The path to the log file (optional)
     * @param maxSizeBytes The maximum size of the log file in bytes before rotation (optional)
     * @param maxBackupCount The maximum number of backup files to keep (optional)
     * @note This method is provided for backward compatibility. New code should use LogConfig.
     */
    void setFileLoggingEnabled(bool enabled,
                              const QString& filePath = QString(),
                              qint64 maxSizeBytes = 10 * 1024 * 1024,
                              int maxBackupCount = 5);

    /**
     * @brief Enables or disables logging to the console
     * @param enabled Whether to enable console logging
     * @note This method is provided for backward compatibility. New code should use LogConfig.
     */
    void setConsoleLoggingEnabled(bool enabled);

    /**
     * @brief Checks if the logger is initialized
     * @return True if the logger is initialized, false otherwise
     */
    bool isInitialized() const;

    /**
     * @brief Updates the logger's sinks based on the current configuration
     * @note This method is called by LogConfig when configuration changes
     */
    void updateSinks();

public:
    // Prevent copying
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;

private:
    Logger();
    ~Logger();

    std::vector<std::shared_ptr<ILogSink>> m_sinks;
    QMutex m_mutex;
    QString m_appName;
    std::shared_ptr<FileLogSink> m_fileSink;
    std::shared_ptr<ConsoleLogSink> m_consoleSink;
    bool m_initialized;
};

} // namespace Utils
} // namespace Vizion3D

// Convenience macros for logging
#ifdef VIZION3D_DISABLE_LOGGING
    #define VLOG_TRACE(category, message) do {} while(0)
    #define VLOG_DEBUG(category, message) do {} while(0)
    #define VLOG_INFO(category, message) do {} while(0)
    #define VLOG_WARNING(category, message) do {} while(0)
    #define VLOG_ERROR(category, message) do {} while(0)
    #define VLOG_FATAL(category, message) do {} while(0)
#else
    #define VLOG_TRACE(category, message) \
        Vizion3D::Utils::Logger::instance().log(Vizion3D::Utils::LogLevel::Trace, category, message, __FILE__, __LINE__, __FUNCTION__)
    #define VLOG_DEBUG(category, message) \
        Vizion3D::Utils::Logger::instance().log(Vizion3D::Utils::LogLevel::Debug, category, message, __FILE__, __LINE__, __FUNCTION__)
    #define VLOG_INFO(category, message) \
        Vizion3D::Utils::Logger::instance().log(Vizion3D::Utils::LogLevel::Info, category, message, __FILE__, __LINE__, __FUNCTION__)
    #define VLOG_WARNING(category, message) \
        Vizion3D::Utils::Logger::instance().log(Vizion3D::Utils::LogLevel::Warning, category, message, __FILE__, __LINE__, __FUNCTION__)
    #define VLOG_ERROR(category, message) \
        Vizion3D::Utils::Logger::instance().log(Vizion3D::Utils::LogLevel::Error, category, message, __FILE__, __LINE__, __FUNCTION__)
    #define VLOG_FATAL(category, message) \
        Vizion3D::Utils::Logger::instance().log(Vizion3D::Utils::LogLevel::Fatal, category, message, __FILE__, __LINE__, __FUNCTION__)
#endif

#endif // VIZION3D_LOGGER_H
