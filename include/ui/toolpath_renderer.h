#ifndef TOOLPATH_RENDERER_H
#define TOOLPATH_RENDERER_H

#include "ui/renderer.h"
#include <QVector3D>
#include <QVector>
#include <QOpenGLShaderProgram>
#include <QOpenGLVertexArrayObject>
#include <QOpenGLBuffer>

namespace Vizion3D {
namespace UI {

/**
 * @brief Renderer for the toolpath
 *
 * This class renders the toolpath as a line strip with color coding using modern OpenGL.
 */
class ToolpathRenderer : public Renderer {
public:
    /**
     * @brief Constructor
     */
    ToolpathRenderer();

    /**
     * @brief Destructor
     */
    ~ToolpathRenderer() override;

    /**
     * @brief Initialize the renderer
     */
    void initialize() override;

    /**
     * @brief Render the toolpath
     * @param viewMatrix The view matrix
     * @param projectionMatrix The projection matrix
     */
    void render(const QMatrix4x4& viewMatrix, const QMatrix4x4& projectionMatrix) override;

    /**
     * @brief Set the toolpath points
     * @param points The toolpath points
     */
    void setPoints(const QVector<QVector3D>& points);

    /**
     * @brief Set the toolpath colors
     * @param colors The toolpath colors
     */
    void setColors(const QVector<QVector3D>& colors);

    /**
     * @brief Set the line width
     * @param width The line width
     */
    void setLineWidth(float width);

    /**
     * @brief Set the point size
     * @param size The point size
     */
    void setPointSize(float size);

    /**
     * @brief Set whether to show points
     * @param show Whether to show points
     */
    void setShowPoints(bool show);

    /**
     * @brief Get the clamped line width for OpenGL state management
     * @return The clamped line width value
     */
    float getClampedLineWidth() const;

    /**
     * @brief Get the clamped point size for OpenGL state management
     * @return The clamped point size value
     */
    float getClampedPointSize() const;

    /**
     * @brief Clear the toolpath
     */
    void clear();

private:
    /**
     * @brief Create and compile shader program
     */
    void createShaderProgram();

    /**
     * @brief Setup VAO and VBO for toolpath
     */
    void setupToolpathBuffers();

    /**
     * @brief Cache OpenGL capability ranges to avoid repeated queries
     */
    void cacheOpenGLRanges();

    /**
     * @brief Check for OpenGL errors and log them with context
     * @param operation Description of the operation being performed
     */
    void checkOpenGLError(const QString& operation);

    QVector<QVector3D> m_points; ///< Toolpath points
    QVector<QVector3D> m_colors; ///< Toolpath colors
    float m_lineWidth;           ///< Line width
    float m_pointSize;           ///< Point size
    bool m_showPoints;           ///< Whether to show points

    QOpenGLShaderProgram* m_shaderProgram;  ///< Shader program

    // Toolpath data
    QOpenGLVertexArrayObject m_lineVAO;
    QOpenGLBuffer m_lineVertexVBO;
    QOpenGLBuffer m_lineColorVBO;

    // Points data
    QOpenGLVertexArrayObject m_pointVAO;
    QOpenGLBuffer m_pointVertexVBO;
    QOpenGLBuffer m_pointColorVBO;

    bool m_initialized;  ///< Whether the renderer has been initialized
    bool m_needsUpdate;  ///< Whether the buffers need to be updated

    // Cached OpenGL capability ranges to avoid repeated queries
    float m_minLineWidth;   ///< Minimum supported line width
    float m_maxLineWidth;   ///< Maximum supported line width
    float m_minPointSize;   ///< Minimum supported point size
    float m_maxPointSize;   ///< Maximum supported point size
    bool m_rangesCached;    ///< Whether the ranges have been cached
};

} // namespace UI
} // namespace Vizion3D

#endif // TOOLPATH_RENDERER_H
