#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QMessageBox>
#include <QFileDialog>
#include <QSettings>
#include <QScopedPointer>
#include <QTabWidget>
#include "utils/result.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

namespace Vizion3D {
namespace Engine {
namespace Interfaces {
class ISimulationEngine;
class IEngineFactory;
}
}
namespace UI {
class SimulationView;
class ProjectTreeWidget;
class GCodeEditor;
class DebugConsoleWidget;
class LogSettingsDialog;
}
}

/**
 * @brief The MainWindow class represents the main application window.
 *
 * This class provides the main user interface for the Vizion3D CNC simulation application.
 * It includes the menu bar, toolbar, status bar, and central widget with the G-code editor
 * and 3D viewport.
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief Constructs a MainWindow object.
     * @param parent The parent widget.
     */
    MainWindow(QWidget *parent = nullptr);

    /**
     * @brief Destroys the MainWindow object.
     */
    ~MainWindow();

private slots:
    /**
     * @brief Creates a new G-code file.
     */
    void on_actionNew_triggered();

    /**
     * @brief Opens a G-code file.
     */
    void on_actionOpen_triggered();

    /**
     * @brief Saves the current G-code file.
     */
    void on_actionSave_triggered();

    /**
     * @brief Saves the current G-code file with a new name.
     */
    void on_actionSaveAs_triggered();

    /**
     * @brief Exits the application.
     */
    void on_actionExit_triggered();

    /**
     * @brief Undoes the last action in the G-code editor.
     */
    void on_actionUndo_triggered();

    /**
     * @brief Redoes the last undone action in the G-code editor.
     */
    void on_actionRedo_triggered();

    /**
     * @brief Cuts the selected text in the G-code editor.
     */
    void on_actionCut_triggered();

    /**
     * @brief Copies the selected text in the G-code editor.
     */
    void on_actionCopy_triggered();

    /**
     * @brief Pastes text from the clipboard into the G-code editor.
     */
    void on_actionPaste_triggered();

    /**
     * @brief Starts the simulation.
     */
    void on_actionStart_triggered();

    /**
     * @brief Pauses the simulation.
     */
    void on_actionPause_triggered();

    /**
     * @brief Stops the simulation.
     */
    void on_actionStop_triggered();

    /**
     * @brief Steps the simulation.
     */
    void on_actionStep_triggered();

    /**
     * @brief Shows the about dialog.
     */
    void on_actionAbout_triggered();

    /**
     * @brief Shows the documentation.
     */
    void on_actionDocumentation_triggered();

    /**
     * @brief Sets the top view.
     */
    void on_actionTopView_triggered();

    /**
     * @brief Sets the front view.
     */
    void on_actionFrontView_triggered();

    /**
     * @brief Sets the side view.
     */
    void on_actionSideView_triggered();

    /**
     * @brief Sets the isometric view.
     */
    void on_actionIsometricView_triggered();

    /**
     * @brief Zooms in.
     */
    void on_actionZoomIn_triggered();

    /**
     * @brief Zooms out.
     */
    void on_actionZoomOut_triggered();

    /**
     * @brief Fits the view to the content.
     */
    void on_actionZoomFit_triggered();

    /**
     * @brief Shows the settings dialog.
     */
    void on_actionSettings_triggered();

    /**
     * @brief Shows the logging settings dialog.
     */
    void on_actionLoggingSettings_triggered();

    /**
     * @brief Shows the debug console.
     */
    void on_actionDebugConsole_triggered();

    /**
     * @brief Handles the start button click.
     */
    void on_startButton_clicked();

    /**
     * @brief Handles the pause button click.
     */
    void on_pauseButton_clicked();

    /**
     * @brief Handles the stop button click.
     */
    void on_stopButton_clicked();

    /**
     * @brief Handles the step button click.
     */
    void on_stepButton_clicked();

    /**
     * @brief Handles the speed slider value change.
     * @param value The new speed value.
     */
    void on_speedSlider_valueChanged(int value);

private:
    Ui::MainWindow *ui;
    QString currentFilePath;

    QScopedPointer<Vizion3D::Engine::Interfaces::IEngineFactory> m_engineFactory;
    Vizion3D::Engine::Interfaces::ISimulationEngine* m_simulationEngine;
    Vizion3D::UI::SimulationView* m_simulationView;
    Vizion3D::UI::ProjectTreeWidget* m_projectTreeWidget;
    Vizion3D::UI::GCodeEditor* m_gCodeEditor;
    Vizion3D::UI::DebugConsoleWidget* m_debugConsoleWidget;
    Vizion3D::UI::LogSettingsDialog* m_logSettingsDialog;

    /**
     * @brief Initializes the UI.
     */
    void initializeUi();

    /**
     * @brief Connects signals and slots.
     */
    void connectSignalsAndSlots();

    /**
     * @brief Updates the UI based on the simulation state.
     */
    void updateUi();

    /**
     * @brief Loads settings from the settings file.
     */
    void loadSettings();

    /**
     * @brief Saves settings to the settings file.
     */
    void saveSettings();

    /**
     * @brief Initializes the simulation engine.
     */
    void initializeSimulationEngine();

    /**
     * @brief Handles the close event.
     * @param event The close event.
     *
     * This method is called when the user attempts to close the application.
     * It checks for unsaved changes and prompts the user to save them.
     */
    void closeEvent(QCloseEvent* event) override;

private slots:
    /**
     * @brief Handles simulation started signal.
     */
    void onSimulationStarted();

    /**
     * @brief Handles simulation paused signal.
     */
    void onSimulationPaused();

    /**
     * @brief Handles simulation resumed signal.
     */
    void onSimulationResumed();

    /**
     * @brief Handles simulation stopped signal.
     */
    void onSimulationStopped();

    /**
     * @brief Handles simulation completed signal.
     */
    void onSimulationCompleted();

    /**
     * @brief Handles simulation error signal.
     * @param error The structured error information.
     */
    void onSimulationError(const Vizion3D::Utils::Error& error);

    /**
     * @brief Handles parsing completed signal.
     */
    void onParsingCompleted();

    /**
     * @brief Handles parsing error signal.
     * @param error The structured error information.
     */
    void onParsingError(const Vizion3D::Utils::Error& error);
};

#endif // MAINWINDOW_H
