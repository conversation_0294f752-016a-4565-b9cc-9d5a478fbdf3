#ifndef SIMULATION_VIEW_H
#define SIMULATION_VIEW_H

#include <QWidget>
#include <QVector3D>
#include <QOpenGLWidget>
#include <QOpenGLFunctions_4_1_Core>
#include <QMatrix4x4>

#include "ui/camera.h"
#include "ui/grid_renderer.h"
#include "ui/axes_renderer.h"
#include "ui/toolpath_renderer.h"
#include "ui/position_renderer.h"

namespace Vizion3D {
namespace Engine {
namespace Interfaces {
class ISimulationEngine;
class ToolpathPoint;
}
}

namespace UI {

/**
 * @brief Widget for displaying the simulation
 *
 * This widget provides a 3D view of the simulation, including the toolpath,
 * workpiece, and machine. It observes the simulation engine through signal/slot
 * connections without storing a direct reference.
 */
class SimulationView : public QOpenGLWidget, protected QOpenGLFunctions_4_1_Core {
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent The parent widget
     */
    explicit SimulationView(QWidget* parent = nullptr);

    /**
     * @brief Destructor
     */
    ~SimulationView() override;

    /**
     * @brief Connect to a simulation engine
     * @param engine The simulation engine to connect to
     *
     * This method connects the view to the engine's signals without
     * storing a direct reference to the engine.
     */
    void connectToEngine(Engine::Interfaces::ISimulationEngine* engine);

    /**
     * @brief Set the camera view
     * @param view The view type (top, front, side, isometric)
     */
    void setView(const QString& view);

    /**
     * @brief Fit the view to the content
     */
    void fitView();

protected:
    /**
     * @brief Initialize OpenGL
     *
     * This method is called once before the first call to paintGL() or resizeGL().
     */
    void initializeGL() override;

    /**
     * @brief Paint the scene
     *
     * This method is called whenever the widget needs to be painted.
     */
    void paintGL() override;

    /**
     * @brief Handle resize events
     * @param width The new width of the widget
     * @param height The new height of the widget
     *
     * This method is called whenever the widget has been resized.
     */
    void resizeGL(int width, int height) override;

    /**
     * @brief Handle mouse press events
     * @param event The mouse event
     */
    void mousePressEvent(QMouseEvent* event) override;

    /**
     * @brief Handle mouse move events
     * @param event The mouse event
     */
    void mouseMoveEvent(QMouseEvent* event) override;

    /**
     * @brief Handle mouse release events
     * @param event The mouse event
     */
    void mouseReleaseEvent(QMouseEvent* event) override;

    /**
     * @brief Handle wheel events
     * @param event The wheel event
     */
    void wheelEvent(QWheelEvent* event) override;

private slots:
    /**
     * @brief Handle simulation step
     * @param position The current position
     */
    void onSimulationStep(const Engine::Interfaces::ToolpathPoint& position);

    /**
     * @brief Handle simulation started
     */
    void onSimulationStarted();

    /**
     * @brief Handle simulation stopped
     */
    void onSimulationStopped();

    /**
     * @brief Handle simulation completed
     */
    void onSimulationCompleted();

private:
    /**
     * @brief Reset the camera
     */
    void resetCamera();

    // Signal connections
    QList<QMetaObject::Connection> m_connections;

    // Camera
    Camera m_camera;

    // Renderers
    GridRenderer m_gridRenderer;
    AxesRenderer m_axesRenderer;
    ToolpathRenderer m_toolpathRenderer;
    PositionRenderer m_positionRenderer;

    // Mouse interaction
    QPoint m_lastMousePos;
    bool m_mousePressed;

    // Rendering state
    bool m_showGrid;
    bool m_showAxes;
    bool m_showToolpath;

    // Simulation state
    bool m_simulationActive;

    // Current position
    QVector3D m_currentPosition;

    // Toolpath data
    QVector<QVector3D> m_toolpathPoints;
    QVector<QVector3D> m_toolpathColors;
};

} // namespace UI
} // namespace Vizion3D

#endif // SIMULATION_VIEW_H
