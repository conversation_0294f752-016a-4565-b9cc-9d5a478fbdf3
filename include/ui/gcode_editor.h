#ifndef GCODE_EDITOR_H
#define GCODE_EDITOR_H

#include <QPlainTextEdit>
#include <QObject>
#include <QSyntaxHighlighter>
#include <QTextCharFormat>
#include <QRegularExpression>
#include <QPainter>
#include <QTextBlock>
#include <QScrollBar>

namespace Vizion3D {
namespace UI {

/**
 * @brief Syntax highlighter for G-code
 */
class GCodeSyntaxHighlighter : public QSyntaxHighlighter {
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent The parent document
     */
    explicit GCodeSyntaxHighlighter(QTextDocument* parent = nullptr);

protected:
    /**
     * @brief Highlight a block of text
     * @param text The text to highlight
     */
    void highlightBlock(const QString& text) override;

private:
    struct HighlightingRule {
        QRegularExpression pattern;
        QTextCharFormat format;
    };

    QVector<HighlightingRule> m_highlightingRules;

    QTextCharFormat m_gCodeFormat;
    QTextCharFormat m_mCodeFormat;
    QTextCharFormat m_coordinateFormat;
    QTextCharFormat m_commentFormat;
    QTextCharFormat m_numberFormat;
};

/**
 * @brief Line number area for the G-code editor
 */
class LineNumberArea : public QWidget {
public:
    /**
     * @brief Constructor
     * @param editor The parent editor
     */
    LineNumberArea(QPlainTextEdit* editor);

    /**
     * @brief Get the size hint for the line number area
     * @return The size hint
     */
    QSize sizeHint() const override;

protected:
    /**
     * @brief Paint event handler
     * @param event The paint event
     */
    void paintEvent(QPaintEvent* event) override;

private:
    QPlainTextEdit* m_codeEditor;
};

/**
 * @brief G-code editor widget with syntax highlighting and line numbers
 */
class GCodeEditor : public QPlainTextEdit {
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent The parent widget
     */
    explicit GCodeEditor(QWidget* parent = nullptr);

    /**
     * @brief Set the current line to highlight
     * @param lineNumber The line number to highlight (1-based)
     */
    void setCurrentLine(int lineNumber);

    /**
     * @brief Get the current highlighted line
     * @return The current highlighted line (1-based)
     */
    int currentLine() const;

    /**
     * @brief Line number area width
     * @return The width of the line number area
     */
    int lineNumberAreaWidth() const;

    /**
     * @brief Get the bounding geometry of a text block
     * @param block The text block
     * @return The bounding geometry of the block
     */
    QRectF blockBoundingGeometryPublic(const QTextBlock& block) const;

    /**
     * @brief Get the bounding rectangle of a text block
     * @param block The text block
     * @return The bounding rectangle of the block
     */
    QRectF blockBoundingRectPublic(const QTextBlock& block) const;

    /**
     * @brief Get the content offset
     * @return The content offset
     */
    QPointF contentOffsetPublic() const;

protected:
    /**
     * @brief Resize event handler
     * @param event The resize event
     */
    void resizeEvent(QResizeEvent* event) override;

private slots:
    /**
     * @brief Update the line number area width
     * @param newBlockCount The new block count
     */
    void updateLineNumberAreaWidth(int newBlockCount);

    /**
     * @brief Update the line number area
     * @param rect The rectangle to update
     * @param dy The vertical scroll amount
     */
    void updateLineNumberArea(const QRect& rect, int dy);

    /**
     * @brief Highlight the current line
     */
    void highlightCurrentLine();

private:
    LineNumberArea* m_lineNumberArea;
    GCodeSyntaxHighlighter* m_highlighter;
    int m_currentLineNumber;
};

} // namespace UI
} // namespace Vizion3D

#endif // GCODE_EDITOR_H
