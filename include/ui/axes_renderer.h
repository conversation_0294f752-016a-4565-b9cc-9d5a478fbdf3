#ifndef AXES_RENDERER_H
#define AXES_RENDERER_H

#include "ui/renderer.h"
#include <QVector3D>
#include <QOpenGLShaderProgram>
#include <QOpenGLVertexArrayObject>
#include <QOpenGLBuffer>
#include <QVector>

namespace Vizion3D {
namespace UI {

/**
 * @brief Renderer for the coordinate axes
 *
 * This class renders the X, Y, and Z coordinate axes using modern OpenGL.
 */
class AxesRenderer : public Renderer {
public:
    /**
     * @brief Constructor
     */
    AxesRenderer();

    /**
     * @brief Destructor
     */
    ~AxesRenderer() override;

    /**
     * @brief Initialize the renderer
     */
    void initialize() override;

    /**
     * @brief Render the axes
     * @param viewMatrix The view matrix
     * @param projectionMatrix The projection matrix
     */
    void render(const QMatrix4x4& viewMatrix, const QMatrix4x4& projectionMatrix) override;

    /**
     * @brief Set the axis length
     * @param length The axis length
     */
    void setAxisLength(float length);

    /**
     * @brief Set the arrow size
     * @param size The arrow size
     */
    void setArrowSize(float size);

    /**
     * @brief Set the axis colors
     * @param xColor The X axis color
     * @param yColor The Y axis color
     * @param zColor The Z axis color
     */
    void setAxisColors(const QVector3D& xColor, const QVector3D& yColor, const QVector3D& zColor);

    /**
     * @brief Set whether to show labels
     * @param show Whether to show labels
     */
    void setShowLabels(bool show);

private:
    /**
     * @brief Generate axes vertices
     */
    void generateAxesVertices();

    /**
     * @brief Create and compile shader program
     */
    void createShaderProgram();

    /**
     * @brief Setup VAO and VBO for axes
     */
    void setupAxesBuffers();

    float m_axisLength;     ///< Axis length
    float m_arrowSize;      ///< Arrow size
    QVector3D m_xAxisColor; ///< X axis color
    QVector3D m_yAxisColor; ///< Y axis color
    QVector3D m_zAxisColor; ///< Z axis color
    bool m_showLabels;      ///< Whether to show labels

    QOpenGLShaderProgram* m_shaderProgram;  ///< Shader program

    // Axes data
    QVector<QVector3D> m_axesVertices;
    QVector<QVector3D> m_axesColors;
    QOpenGLVertexArrayObject m_axesVAO;
    QOpenGLBuffer m_axesVertexVBO;
    QOpenGLBuffer m_axesColorVBO;

    bool m_initialized;  ///< Whether the renderer has been initialized
    bool m_needsUpdate;  ///< Whether the buffers need to be updated
};

} // namespace UI
} // namespace Vizion3D

#endif // AXES_RENDERER_H
