#ifndef RENDERER_H
#define RENDERER_H

#include <QOpenGLFunctions_4_1_Core>
#include <QMatrix4x4>
#include <QOpenGLShaderProgram>
#include <QOpenGLVertexArrayObject>
#include <QOpenGLBuffer>
#include <QString>
#include <QDebug>

namespace Vizion3D {
namespace UI {

/**
 * @brief Base class for all renderers
 *
 * This class defines the interface for all renderers using modern OpenGL.
 */
class Renderer : protected QOpenGLFunctions_4_1_Core {
public:
    /**
     * @brief Constructor
     */
    Renderer();

    /**
     * @brief Virtual destructor
     */
    virtual ~Renderer();

    /**
     * @brief Initialize the renderer
     *
     * This method is called once before the first render call.
     */
    virtual void initialize() = 0;

    /**
     * @brief Render the element
     * @param viewMatrix The view matrix
     * @param projectionMatrix The projection matrix
     *
     * This method renders the element using the provided matrices.
     */
    virtual void render(const QMatrix4x4& viewMatrix, const QMatrix4x4& projectionMatrix) = 0;

    /**
     * @brief Create and bind a Vertex Array Object (VAO)
     *
     * This method creates and binds a VAO, which is required in OpenGL Core Profile
     * before setting up vertex attributes.
     *
     * @param vao The VAO to create and bind
     * @return True if the VAO was created and bound successfully, false otherwise
     */
    bool createAndBindVAO(QOpenGLVertexArrayObject& vao);

protected:
    QOpenGLVertexArrayObject m_vao; ///< Vertex Array Object

protected:
    /**
     * @brief Create a shader program
     * @param vertexShaderSource The vertex shader source code
     * @param fragmentShaderSource The fragment shader source code
     * @return The shader program, or nullptr if creation failed
     */
    QOpenGLShaderProgram* createShaderProgram(const QString& vertexShaderSource, const QString& fragmentShaderSource);

    /**
     * @brief Load shader source from file
     * @param filePath The path to the shader file
     * @return The shader source code
     */
    QString loadShaderSource(const QString& filePath);

    /**
     * @brief Delete a shader program
     * @param program The shader program to delete
     */
    void deleteShaderProgram(QOpenGLShaderProgram* program);
};

} // namespace UI
} // namespace Vizion3D

#endif // RENDERER_H
