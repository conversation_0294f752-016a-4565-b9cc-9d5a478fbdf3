#ifndef OPENGL_STATE_H
#define OPENGL_STATE_H

#include <QOpenGLFunctions_4_1_Core>
#include <QVector3D>
#include <QVector4D>
#include <QMatrix4x4>

namespace Vizion3D {
namespace UI {

/**
 * @brief Helper class for managing OpenGL state
 *
 * This class provides methods for managing OpenGL state changes,
 * reducing redundant state changes and centralizing state management.
 */
class OpenGLState : protected QOpenGLFunctions_4_1_Core {
public:
    /**
     * @brief Constructor
     */
    OpenGLState();

    /**
     * @brief Get the singleton instance
     * @return The singleton instance
     */
    static OpenGLState& instance();

    /**
     * @brief Initialize OpenGL state
     *
     * This method should be called once at the beginning of rendering.
     */
    void initialize();

    /**
     * @brief Enable depth testing
     * @param depthFunc The depth function to use (default: GL_LESS)
     */
    void enableDepthTest(GLenum depthFunc = GL_LESS);

    /**
     * @brief Disable depth testing
     */
    void disableDepthTest();

    /**
     * @brief Enable backface culling
     * @param mode The culling mode (default: GL_BACK)
     */
    void enableCullFace(GLenum mode = GL_BACK);

    /**
     * @brief Disable backface culling
     */
    void disableCullFace();

    /**
     * @brief Enable blending
     * @param sfactor The source factor (default: GL_SRC_ALPHA)
     * @param dfactor The destination factor (default: GL_ONE_MINUS_SRC_ALPHA)
     */
    void enableBlending(GLenum sfactor = GL_SRC_ALPHA, GLenum dfactor = GL_ONE_MINUS_SRC_ALPHA);

    /**
     * @brief Disable blending
     */
    void disableBlending();

    /**
     * @brief Set line width
     * @param width The line width
     */
    void setLineWidth(float width);

    /**
     * @brief Set point size
     * @param size The point size
     */
    void setPointSize(float size);

    /**
     * @brief Set color
     * @param color The color as RGB
     */
    void setColor(const QVector3D& color);

    /**
     * @brief Set color
     * @param color The color as RGBA
     */
    void setColor(const QVector4D& color);

    /**
     * @brief Set color
     * @param r Red component
     * @param g Green component
     * @param b Blue component
     * @param a Alpha component (default: 1.0f)
     */
    void setColor(float r, float g, float b, float a = 1.0f);

    /**
     * @brief Set the projection matrix
     * @param matrix The projection matrix
     */
    void setProjectionMatrix(const QMatrix4x4& matrix);

    /**
     * @brief Set the model-view matrix
     * @param matrix The model-view matrix
     */
    void setModelViewMatrix(const QMatrix4x4& matrix);

    /**
     * @brief Clear the color and depth buffers
     * @param color The clear color (default: dark gray)
     */
    void clear(const QVector4D& color = QVector4D(0.15f, 0.15f, 0.15f, 1.0f));

private:
    // Current state tracking
    bool m_depthTestEnabled;
    bool m_cullFaceEnabled;
    bool m_blendingEnabled;
    float m_lineWidth;
    float m_pointSize;
    QVector4D m_color;
    QMatrix4x4 m_projectionMatrix;
    QMatrix4x4 m_modelViewMatrix;

    // Singleton instance
    static OpenGLState* s_instance;
};

// Convenience functions for the singleton instance
namespace OpenGLStateHelper {
    inline void initialize() { OpenGLState::instance().initialize(); }
    inline void enableDepthTest(GLenum depthFunc = GL_LESS) { OpenGLState::instance().enableDepthTest(depthFunc); }
    inline void disableDepthTest() { OpenGLState::instance().disableDepthTest(); }
    inline void enableCullFace(GLenum mode = GL_BACK) { OpenGLState::instance().enableCullFace(mode); }
    inline void disableCullFace() { OpenGLState::instance().disableCullFace(); }
    inline void enableBlending(GLenum sfactor = GL_SRC_ALPHA, GLenum dfactor = GL_ONE_MINUS_SRC_ALPHA) { OpenGLState::instance().enableBlending(sfactor, dfactor); }
    inline void disableBlending() { OpenGLState::instance().disableBlending(); }
    inline void setLineWidth(float width) { OpenGLState::instance().setLineWidth(width); }
    inline void setPointSize(float size) { OpenGLState::instance().setPointSize(size); }
    inline void setColor(const QVector3D& color) { OpenGLState::instance().setColor(color); }
    inline void setColor(const QVector4D& color) { OpenGLState::instance().setColor(color); }
    inline void setColor(float r, float g, float b, float a = 1.0f) { OpenGLState::instance().setColor(r, g, b, a); }
    inline void setProjectionMatrix(const QMatrix4x4& matrix) { OpenGLState::instance().setProjectionMatrix(matrix); }
    inline void setModelViewMatrix(const QMatrix4x4& matrix) { OpenGLState::instance().setModelViewMatrix(matrix); }
    inline void clear(const QVector4D& color = QVector4D(0.15f, 0.15f, 0.15f, 1.0f)) { OpenGLState::instance().clear(color); }
}

} // namespace UI
} // namespace Vizion3D

#endif // OPENGL_STATE_H
