#ifndef GRID_RENDERER_H
#define GRID_RENDERER_H

#include "ui/renderer.h"
#include <QVector3D>
#include <QOpenGLShaderProgram>
#include <QOpenGLVertexArrayObject>
#include <QOpenGLBuffer>
#include <QVector>

namespace Vizion3D {
namespace UI {

/**
 * @brief Renderer for the grid
 *
 * This class renders a reference grid on the XZ plane using modern OpenGL.
 */
class GridRenderer : public Renderer {
public:
    /**
     * @brief Constructor
     */
    GridRenderer();

    /**
     * @brief Destructor
     */
    ~GridRenderer() override;

    /**
     * @brief Initialize the renderer
     */
    void initialize() override;

    /**
     * @brief Render the grid
     * @param viewMatrix The view matrix
     * @param projectionMatrix The projection matrix
     */
    void render(const QMatrix4x4& viewMatrix, const QMatrix4x4& projectionMatrix) override;

    /**
     * @brief Set the grid size
     * @param size The grid size
     */
    void setSize(float size);

    /**
     * @brief Set the grid step
     * @param step The grid step
     */
    void setStep(float step);

    /**
     * @brief Set the grid color
     * @param color The grid color
     */
    void setColor(const QVector3D& color);

    /**
     * @brief Set the main axes color
     * @param xColor The X axis color
     * @param yColor The Y axis color
     * @param zColor The Z axis color
     */
    void setMainAxesColor(const QVector3D& xColor, const QVector3D& yColor, const QVector3D& zColor);

    /**
     * @brief Set whether to highlight the toolpath area
     * @param highlight Whether to highlight the toolpath area
     */
    void setHighlightToolpathArea(bool highlight);

    /**
     * @brief Set the toolpath area
     * @param min The minimum corner of the toolpath area
     * @param max The maximum corner of the toolpath area
     */
    void setToolpathArea(const QVector3D& min, const QVector3D& max);

private:
    /**
     * @brief Generate grid vertices
     */
    void generateGridVertices();

    /**
     * @brief Generate main axes vertices
     */
    void generateMainAxesVertices();

    /**
     * @brief Generate toolpath area vertices
     */
    void generateToolpathAreaVertices();

    /**
     * @brief Create and compile shader program
     */
    void createShaderProgram();

    /**
     * @brief Setup VAO and VBO for grid
     */
    void setupGridBuffers();

    /**
     * @brief Setup VAO and VBO for main axes
     */
    void setupMainAxesBuffers();

    /**
     * @brief Setup VAO and VBO for toolpath area
     */
    void setupToolpathAreaBuffers();

    float m_size;                  ///< Grid size
    float m_step;                  ///< Grid step
    QVector3D m_color;             ///< Grid color
    QVector3D m_xAxisColor;        ///< X axis color
    QVector3D m_yAxisColor;        ///< Y axis color
    QVector3D m_zAxisColor;        ///< Z axis color
    bool m_highlightToolpathArea;  ///< Whether to highlight the toolpath area
    QVector3D m_toolpathAreaMin;   ///< Minimum corner of the toolpath area
    QVector3D m_toolpathAreaMax;   ///< Maximum corner of the toolpath area

    QOpenGLShaderProgram* m_shaderProgram;  ///< Shader program

    // Grid data
    QVector<QVector3D> m_gridVertices;
    QVector<QVector3D> m_gridColors;
    QOpenGLVertexArrayObject m_gridVAO;
    QOpenGLBuffer m_gridVertexVBO;
    QOpenGLBuffer m_gridColorVBO;

    // Main axes data
    QVector<QVector3D> m_axesVertices;
    QVector<QVector3D> m_axesColors;
    QOpenGLVertexArrayObject m_axesVAO;
    QOpenGLBuffer m_axesVertexVBO;
    QOpenGLBuffer m_axesColorVBO;

    // Toolpath area data
    QVector<QVector3D> m_toolpathAreaVertices;
    QVector<QVector3D> m_toolpathAreaColors;
    QOpenGLVertexArrayObject m_toolpathAreaVAO;
    QOpenGLBuffer m_toolpathAreaVertexVBO;
    QOpenGLBuffer m_toolpathAreaColorVBO;

    bool m_initialized;  ///< Whether the renderer has been initialized
    bool m_needsUpdate;  ///< Whether the buffers need to be updated
};

} // namespace UI
} // namespace Vizion3D

#endif // GRID_RENDERER_H
