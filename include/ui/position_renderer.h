#ifndef POSITION_RENDERER_H
#define POSITION_RENDERER_H

#include "ui/renderer.h"
#include <QVector3D>
#include <QVector4D>
#include <QOpenGLShaderProgram>
#include <QOpenGLVertexArrayObject>
#include <QOpenGLBuffer>
#include <QVector>

namespace Vizion3D {
namespace UI {

/**
 * @brief Renderer for the current position
 *
 * This class renders the current tool position as a sphere using modern OpenGL.
 */
class PositionRenderer : public Renderer {
public:
    /**
     * @brief Constructor
     */
    PositionRenderer();

    /**
     * @brief Destructor
     */
    ~PositionRenderer() override;

    /**
     * @brief Initialize the renderer
     */
    void initialize() override;

    /**
     * @brief Render the current position
     * @param viewMatrix The view matrix
     * @param projectionMatrix The projection matrix
     */
    void render(const QMatrix4x4& viewMatrix, const QMatrix4x4& projectionMatrix) override;

    /**
     * @brief Set the current position
     * @param position The current position
     */
    void setPosition(const QVector3D& position);

    /**
     * @brief Set the sphere radius
     * @param radius The sphere radius
     */
    void setRadius(float radius);

    /**
     * @brief Set the sphere color
     * @param color The sphere color
     */
    void setColor(const QVector4D& color);

    /**
     * @brief Set the sphere resolution
     * @param slices The number of slices
     * @param stacks The number of stacks
     */
    void setResolution(int slices, int stacks);

    /**
     * @brief Set whether to show the position cross
     * @param show Whether to show the position cross
     */
    void setShowCross(bool show);

    /**
     * @brief Set the cross size
     * @param size The cross size
     */
    void setCrossSize(float size);

private:
    /**
     * @brief Generate sphere vertices
     */
    void generateSphereVertices();

    /**
     * @brief Generate cross vertices
     */
    void generateCrossVertices();

    /**
     * @brief Create and compile shader program
     */
    void createShaderProgram();

    /**
     * @brief Setup VAO and VBO for sphere
     */
    void setupSphereBuffers();

    /**
     * @brief Setup VAO and VBO for cross
     */
    void setupCrossBuffers();

    QVector3D m_position;  ///< Current position
    float m_radius;        ///< Sphere radius
    QVector4D m_color;     ///< Sphere color
    int m_slices;          ///< Number of slices
    int m_stacks;          ///< Number of stacks
    bool m_showCross;      ///< Whether to show the position cross
    float m_crossSize;     ///< Cross size

    QOpenGLShaderProgram* m_shaderProgram;  ///< Shader program

    // Sphere data
    QVector<QVector3D> m_sphereVertices;
    QVector<QVector3D> m_sphereNormals;
    QVector<unsigned int> m_sphereIndices;
    QOpenGLVertexArrayObject m_sphereVAO;
    QOpenGLBuffer m_sphereVertexVBO;
    QOpenGLBuffer m_sphereNormalVBO;
    QOpenGLBuffer m_sphereIndexVBO;

    // Cross data
    QVector<QVector3D> m_crossVertices;
    QVector<QVector3D> m_crossColors;
    QOpenGLVertexArrayObject m_crossVAO;
    QOpenGLBuffer m_crossVertexVBO;
    QOpenGLBuffer m_crossColorVBO;

    bool m_initialized;  ///< Whether the renderer has been initialized
    bool m_needsUpdate;  ///< Whether the buffers need to be updated
};

} // namespace UI
} // namespace Vizion3D

#endif // POSITION_RENDERER_H
