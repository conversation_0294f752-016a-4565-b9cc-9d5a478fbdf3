#ifndef CAMERA_H
#define CAMERA_H

#include <QVector3D>
#include <QMatrix4x4>

namespace Vizion3D {
namespace UI {

/**
 * @brief Camera class for 3D visualization
 *
 * This class encapsulates camera functionality for 3D visualization,
 * including position, target, orientation, and view manipulation.
 */
class Camera {
public:
    /**
     * @brief Default constructor
     */
    Camera();

    /**
     * @brief Constructor with parameters
     * @param position The camera position
     * @param target The camera target (look-at point)
     * @param up The camera up vector
     */
    Camera(const QVector3D& position, const QVector3D& target, const QVector3D& up);

    /**
     * @brief Get the camera position
     * @return The camera position
     */
    QVector3D position() const;

    /**
     * @brief Set the camera position
     * @param position The new camera position
     */
    void setPosition(const QVector3D& position);

    /**
     * @brief Get the camera target
     * @return The camera target
     */
    QVector3D target() const;

    /**
     * @brief Set the camera target
     * @param target The new camera target
     */
    void setTarget(const QVector3D& target);

    /**
     * @brief Get the camera up vector
     * @return The camera up vector
     */
    QVector3D up() const;

    /**
     * @brief Set the camera up vector
     * @param up The new camera up vector
     */
    void setUp(const QVector3D& up);

    /**
     * @brief Get the camera field of view
     * @return The camera field of view in degrees
     */
    float fov() const;

    /**
     * @brief Set the camera field of view
     * @param fov The new camera field of view in degrees
     */
    void setFov(float fov);

    /**
     * @brief Get the camera near plane distance
     * @return The camera near plane distance
     */
    float nearPlane() const;

    /**
     * @brief Set the camera near plane distance
     * @param nearPlane The new camera near plane distance
     */
    void setNearPlane(float nearPlane);

    /**
     * @brief Get the camera far plane distance
     * @return The camera far plane distance
     */
    float farPlane() const;

    /**
     * @brief Set the camera far plane distance
     * @param farPlane The new camera far plane distance
     */
    void setFarPlane(float farPlane);

    /**
     * @brief Orbit the camera around the target
     * @param deltaX Horizontal orbit angle in degrees
     * @param deltaY Vertical orbit angle in degrees
     */
    void orbit(float deltaX, float deltaY);

    /**
     * @brief Pan the camera
     * @param deltaX Horizontal pan amount
     * @param deltaY Vertical pan amount
     */
    void pan(float deltaX, float deltaY);

    /**
     * @brief Zoom the camera
     * @param factor Zoom factor (< 1 zooms in, > 1 zooms out)
     */
    void zoom(float factor);

    /**
     * @brief Set the camera to top view
     * @param center The center point to look at
     */
    void setTopView(const QVector3D& center = QVector3D(12.5f, 0.0f, 12.5f));

    /**
     * @brief Set the camera to front view
     * @param center The center point to look at
     */
    void setFrontView(const QVector3D& center = QVector3D(12.5f, 0.0f, 12.5f));

    /**
     * @brief Set the camera to side view
     * @param center The center point to look at
     */
    void setSideView(const QVector3D& center = QVector3D(12.5f, 0.0f, 12.5f));

    /**
     * @brief Set the camera to isometric view
     * @param center The center point to look at
     */
    void setIsometricView(const QVector3D& center = QVector3D(12.5f, 0.0f, 12.5f));

    /**
     * @brief Reset the camera to default settings
     * @param center The center point to look at
     */
    void reset(const QVector3D& center = QVector3D(12.5f, 0.0f, 12.5f));

    /**
     * @brief Get the view matrix
     * @return The view matrix
     */
    QMatrix4x4 viewMatrix() const;

    /**
     * @brief Get the projection matrix
     * @param aspect The aspect ratio (width / height)
     * @return The projection matrix
     */
    QMatrix4x4 projectionMatrix(float aspect) const;

private:
    QVector3D m_position;  ///< Camera position
    QVector3D m_target;    ///< Camera target (look-at point)
    QVector3D m_up;        ///< Camera up vector
    float m_fov;           ///< Field of view in degrees
    float m_nearPlane;     ///< Near plane distance
    float m_farPlane;      ///< Far plane distance
};

} // namespace UI
} // namespace Vizion3D

#endif // CAMERA_H
