#ifndef VIZION3D_LOG_SETTINGS_DIALOG_H
#define VIZION3D_LOG_SETTINGS_DIALOG_H

#include "utils/logger.h"
#include "utils/log_config.h"
#include <QDialog>
#include <QMap>
#include <QString>
#include <QComboBox>
#include <QCheckBox>
#include <QLineEdit>
#include <QSpinBox>
#include <QPushButton>
#include <QTableWidget>
#include <QLabel>

namespace Vizion3D {
namespace UI {

/**
 * @brief The LogSettingsDialog class provides a dialog for configuring logging settings
 */
class LogSettingsDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief Constructs a LogSettingsDialog
     * @param parent The parent widget
     */
    explicit LogSettingsDialog(QWidget* parent = nullptr);

    /**
     * @brief Destroys the LogSettingsDialog
     */
    ~LogSettingsDialog();

private slots:
    /**
     * @brief Handles the OK button click
     */
    void onOkClicked();

    /**
     * @brief Handles the Cancel button click
     */
    void onCancelClicked();

    /**
     * @brief Handles the Apply button click
     */
    void onApplyClicked();

    /**
     * @brief Handles the Reset button click
     */
    void onResetClicked();

    /**
     * @brief Handles the Add Category button click
     */
    void onAddCategoryClicked();

    /**
     * @brief Handles the Remove Category button click
     */
    void onRemoveCategoryClicked();

    /**
     * @brief Handles the Browse button click for the log file path
     */
    void onBrowseClicked();

    /**
     * @brief Handles changes to the file logging enabled checkbox
     * @param checked Whether the checkbox is checked
     */
    void onFileLoggingEnabledChanged(bool checked);

private:
    /**
     * @brief Initializes the UI
     */
    void initializeUi();

    /**
     * @brief Loads the current configuration into the UI
     */
    void loadConfiguration();

    /**
     * @brief Applies the UI settings to the configuration
     */
    void applyConfiguration();

    /**
     * @brief Converts a LogLevel to a combo box index
     * @param level The log level
     * @return The combo box index
     */
    int logLevelToIndex(Utils::LogLevel level) const;

    /**
     * @brief Converts a combo box index to a LogLevel
     * @param index The combo box index
     * @return The log level
     */
    Utils::LogLevel indexToLogLevel(int index) const;

    QComboBox* m_globalLevelCombo;
    QTableWidget* m_categoryTable;
    QCheckBox* m_fileLoggingEnabledCheck;
    QLineEdit* m_logFilePathEdit;
    QPushButton* m_browseButton;
    QSpinBox* m_maxFileSizeSpin;
    QSpinBox* m_maxBackupCountSpin;
    QCheckBox* m_consoleLoggingEnabledCheck;
    QPushButton* m_addCategoryButton;
    QPushButton* m_removeCategoryButton;
    QPushButton* m_okButton;
    QPushButton* m_cancelButton;
    QPushButton* m_applyButton;
    QPushButton* m_resetButton;

    QMap<QString, Utils::LogLevel> m_categoryLevels;
};

} // namespace UI
} // namespace Vizion3D

#endif // VIZION3D_LOG_SETTINGS_DIALOG_H
