#ifndef VIZION3D_DEBUG_CONSOLE_WIDGET_H
#define VIZION3D_DEBUG_CONSOLE_WIDGET_H

#include "utils/logger.h"
#include <QWidget>
#include <QPlainTextEdit>
#include <QComboBox>
#include <QPushButton>
#include <QCheckBox>
#include <QLineEdit>
#include <QMutex>
#include <QTimer>
#include <QQueue>
#include <QDateTime>

namespace Vizion3D {
namespace UI {

/**
 * @brief A log sink that outputs to a QPlainTextEdit
 */
class DebugConsoleLogSink : public QObject, public Utils::ILogSink {
    Q_OBJECT
public:
    /**
     * @brief Constructs a DebugConsoleLogSink
     * @param textEdit The QPlainTextEdit to output to
     */
    explicit DebugConsoleLogSink(QPlainTextEdit* textEdit);

    void write(const QDateTime& timestamp,
              Utils::LogLevel level,
              const QString& category,
              const QString& message,
              const QString& file,
              int line,
              const QString& function,
              Qt::HANDLE threadId) override;

    void flush() override;

    /**
     * @brief Sets the maximum number of lines to keep in the console
     * @param maxLines The maximum number of lines
     */
    void setMaxLines(int maxLines);

    /**
     * @brief Gets the maximum number of lines to keep in the console
     * @return The maximum number of lines
     */
    int maxLines() const;

    /**
     * @brief Sets whether to show timestamps in the console
     * @param show Whether to show timestamps
     */
    void setShowTimestamps(bool show);

    /**
     * @brief Gets whether timestamps are shown in the console
     * @return Whether timestamps are shown
     */
    bool showTimestamps() const;

    /**
     * @brief Sets whether to show categories in the console
     * @param show Whether to show categories
     */
    void setShowCategories(bool show);

    /**
     * @brief Gets whether categories are shown in the console
     * @return Whether categories are shown
     */
    bool showCategories() const;

    /**
     * @brief Sets whether to show file and line information in the console
     * @param show Whether to show file and line information
     */
    void setShowFileInfo(bool show);

    /**
     * @brief Gets whether file and line information is shown in the console
     * @return Whether file and line information is shown
     */
    bool showFileInfo() const;

    /**
     * @brief Sets whether to show function names in the console
     * @param show Whether to show function names
     */
    void setShowFunctions(bool show);

    /**
     * @brief Gets whether function names are shown in the console
     * @return Whether function names are shown
     */
    bool showFunctions() const;

    /**
     * @brief Sets whether to show thread IDs in the console
     * @param show Whether to show thread IDs
     */
    void setShowThreadIds(bool show);

    /**
     * @brief Gets whether thread IDs are shown in the console
     * @return Whether thread IDs are shown
     */
    bool showThreadIds() const;

    /**
     * @brief Sets the minimum log level to display
     * @param level The minimum log level
     */
    void setMinLogLevel(Utils::LogLevel level);

    /**
     * @brief Gets the minimum log level to display
     * @return The minimum log level
     */
    Utils::LogLevel minLogLevel() const;

    /**
     * @brief Sets the category filter
     * @param filter The category filter (empty string for no filter)
     */
    void setCategoryFilter(const QString& filter);

    /**
     * @brief Gets the category filter
     * @return The category filter
     */
    QString categoryFilter() const;

    /**
     * @brief Clears the console
     */
    void clear();

private slots:
    /**
     * @brief Process pending log entries
     */
    void processPendingEntries();

private:
    QPlainTextEdit* m_textEdit;
    int m_maxLines;
    bool m_showTimestamps;
    bool m_showCategories;
    bool m_showFileInfo;
    bool m_showFunctions;
    bool m_showThreadIds;
    Utils::LogLevel m_minLogLevel;
    QString m_categoryFilter;
    QMutex m_mutex;

    struct LogEntry {
        QDateTime timestamp;
        Utils::LogLevel level;
        QString category;
        QString message;
        QString file;
        int line;
        QString function;
        Qt::HANDLE threadId;
    };

    QQueue<LogEntry> m_pendingEntries;
    QTimer m_updateTimer;

    void processLogEntry(const LogEntry& entry);
    QString formatLogEntry(const LogEntry& entry) const;
    QColor levelToColor(Utils::LogLevel level) const;
};

/**
 * @brief The DebugConsoleWidget class provides a widget for displaying and filtering log messages
 */
class DebugConsoleWidget : public QWidget {
    Q_OBJECT

public:
    /**
     * @brief Constructs a DebugConsoleWidget
     * @param parent The parent widget
     */
    explicit DebugConsoleWidget(QWidget* parent = nullptr);

    /**
     * @brief Destroys the DebugConsoleWidget
     */
    ~DebugConsoleWidget();

private slots:
    /**
     * @brief Handles the clear button click
     */
    void onClearClicked();

    /**
     * @brief Handles the save button click
     */
    void onSaveClicked();

    /**
     * @brief Handles the log level combo box change
     * @param index The new index
     */
    void onLogLevelChanged(int index);

    /**
     * @brief Handles the category filter change
     */
    void onCategoryFilterChanged();

    /**
     * @brief Handles the show timestamps checkbox change
     * @param checked Whether the checkbox is checked
     */
    void onShowTimestampsChanged(bool checked);

    /**
     * @brief Handles the show categories checkbox change
     * @param checked Whether the checkbox is checked
     */
    void onShowCategoriesChanged(bool checked);

    /**
     * @brief Handles the show file info checkbox change
     * @param checked Whether the checkbox is checked
     */
    void onShowFileInfoChanged(bool checked);

    /**
     * @brief Handles the show functions checkbox change
     * @param checked Whether the checkbox is checked
     */
    void onShowFunctionsChanged(bool checked);

    /**
     * @brief Handles the show thread IDs checkbox change
     * @param checked Whether the checkbox is checked
     */
    void onShowThreadIdsChanged(bool checked);

private:
    /**
     * @brief Initializes the UI
     */
    void initializeUi();

    QPlainTextEdit* m_textEdit;
    QComboBox* m_logLevelCombo;
    QLineEdit* m_categoryFilterEdit;
    QCheckBox* m_showTimestampsCheck;
    QCheckBox* m_showCategoriesCheck;
    QCheckBox* m_showFileInfoCheck;
    QCheckBox* m_showFunctionsCheck;
    QCheckBox* m_showThreadIdsCheck;
    QPushButton* m_clearButton;
    QPushButton* m_saveButton;

    std::shared_ptr<DebugConsoleLogSink> m_logSink;
};

} // namespace UI
} // namespace Vizion3D

#endif // VIZION3D_DEBUG_CONSOLE_WIDGET_H
