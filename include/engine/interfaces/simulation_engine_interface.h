#ifndef ISIMULATION_ENGINE_H
#define ISIMULATION_ENGINE_H

#include <QObject>
#include <QString>
#include <QVector>
#include <QVector3D>
#include "engine/interfaces/toolpath_point.h"
#include "utils/result.h"

namespace Vizion3D {
namespace Engine {
namespace Interfaces {

/**
 * @brief Interface for the simulation engine
 *
 * This interface defines the contract between the UI and the simulation engine.
 * It provides methods for controlling the simulation, loading models, and
 * querying the simulation state.
 */
class ISimulationEngine : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent The parent QObject
     */
    explicit ISimulationEngine(QObject* parent = nullptr) : QObject(parent) {}

    /**
     * @brief Virtual destructor
     */
    ~ISimulationEngine() override = default;

    /**
     * @brief Initialize the simulation engine
     * @param configPath Optional path to a configuration file
     * @return Result indicating success or failure with error details
     */
    virtual Vizion3D::Utils::Result<void> initialize(const QString& configPath = QString()) = 0;

    /**
     * @brief Parse G-code and generate toolpath
     * @param gcode The G-code to parse
     * @return Result indicating success or failure with error details
     */
    virtual Vizion3D::Utils::Result<void> parseGCode(const QString& gcode) = 0;

    /**
     * @brief Get the generated toolpath
     * @return Result containing vector of toolpath points or error details
     */
    virtual Vizion3D::Utils::Result<QVector<ToolpathPoint>> getToolpath() const = 0;

    /**
     * @brief Start the simulation
     * @param timeStep The time step for simulation in seconds
     * @return Result indicating success or failure with error details
     */
    virtual Vizion3D::Utils::Result<void> startSimulation(double timeStep = 0.1) = 0;

    /**
     * @brief Pause the simulation
     */
    virtual void pauseSimulation() = 0;

    /**
     * @brief Resume the simulation
     */
    virtual void resumeSimulation() = 0;

    /**
     * @brief Stop the simulation
     */
    virtual void stopSimulation() = 0;

    /**
     * @brief Step the simulation
     */
    virtual void stepSimulation() = 0;

    /**
     * @brief Reset the simulation back to the beginning
     */
    virtual void resetSimulation() = 0;

    /**
     * @brief Load a machine definition
     * @param filePath Path to the machine definition file
     * @return Result indicating success or failure with error details
     */
    virtual Vizion3D::Utils::Result<void> loadMachine(const QString& filePath) = 0;

    /**
     * @brief Load a 3D model
     * @param filePath Path to the model file
     * @param name Unique name for the model
     * @return Result indicating success or failure with error details
     */
    virtual Vizion3D::Utils::Result<void> loadModel(const QString& filePath, const QString& name) = 0;

    /**
     * @brief Export the workpiece to a file
     * @param filePath Path to the output file
     * @param fileFormat Format of the output file (STL, STEP, etc.)
     * @return Result indicating success or failure with error details
     */
    virtual Vizion3D::Utils::Result<void> exportWorkpiece(const QString& filePath, const QString& fileFormat) = 0;

    /**
     * @brief Check if the simulation is running
     * @return True if the simulation is running, false otherwise
     */
    virtual bool isSimulationRunning() const = 0;

    /**
     * @brief Check if the simulation is paused
     * @return True if the simulation is paused, false otherwise
     */
    virtual bool isSimulationPaused() const = 0;

    /**
     * @brief Check if the simulation is in stepping mode
     * @return True if the simulation is in stepping mode, false otherwise
     */
    virtual bool isSteppingMode() const = 0;

    /**
     * @brief Get the current volume of the workpiece
     * @return Volume in cubic units
     */
    virtual double getWorkpieceVolume() const = 0;

    /**
     * @brief Check for collisions in the current scene
     * @return Vector of collision descriptions
     */
    virtual QStringList checkCollisions() = 0;

    /**
     * @brief Simulate material removal
     * @param gcode G-code string to simulate
     * @param timeStep Time step for simulation in seconds
     * @return Result indicating success or failure with error details
     */
    virtual Vizion3D::Utils::Result<void> simulateMaterialRemoval(const QString& gcode, double timeStep = 0.1) = 0;

    /**
     * @brief Set the simulation speed
     * @param speed The simulation speed factor (1.0 = normal speed)
     */
    virtual void setSimulationSpeed(double speed) = 0;

    /**
     * @brief Get the simulation speed
     * @return The simulation speed factor
     */
    virtual double getSimulationSpeed() const = 0;

    /**
     * @brief Process the next line of G-code
     * @return Result indicating success or failure with error details
     *
     * This method processes the next line of G-code and updates the simulation state.
     * It returns success if a line was processed, or error if processing failed.
     */
    virtual Vizion3D::Utils::Result<void> processNextLine() = 0;

signals:
    /**
     * @brief Signal emitted when the simulation starts
     */
    void simulationStarted();

    /**
     * @brief Signal emitted when the simulation is paused
     */
    void simulationPaused();

    /**
     * @brief Signal emitted when the simulation is resumed
     */
    void simulationResumed();

    /**
     * @brief Signal emitted when the simulation is stopped
     */
    void simulationStopped();

    /**
     * @brief Signal emitted when the simulation is completed
     */
    void simulationCompleted();

    /**
     * @brief Signal emitted to report simulation progress
     * @param progress The progress as a value between 0.0 and 1.0
     */
    void simulationProgress(double progress);

    /**
     * @brief Signal emitted for each simulation step
     * @param position The current position
     */
    void simulationStep(const ToolpathPoint& position);

    /**
     * @brief Signal emitted when a simulation error occurs
     * @param error The structured error information
     */
    void simulationError(const Vizion3D::Utils::Error& error);

    /**
     * @brief Signal emitted when parsing starts
     */
    void parsingStarted();

    /**
     * @brief Signal emitted to report parsing progress
     * @param lineNumber The current line number being parsed
     * @param totalLines The total number of lines to parse
     */
    void parsingProgress(int lineNumber, int totalLines);

    /**
     * @brief Signal emitted when parsing is completed
     */
    void parsingCompleted();

    /**
     * @brief Signal emitted when a parsing error occurs
     * @param error The structured error information
     */
    void parsingError(const Vizion3D::Utils::Error& error);

    /**
     * @brief Signal emitted when a collision is detected
     * @param description Description of the collision
     */
    void collisionDetected(const QString& description);

    /**
     * @brief Signal emitted when material removal is completed
     */
    void materialRemovalCompleted();
};

} // namespace Interfaces
} // namespace Engine
} // namespace Vizion3D

#endif // ISIMULATION_ENGINE_H
