#ifndef IENGINE_FACTORY_H
#define IENGINE_FACTORY_H

#include <QObject>

namespace Vizion3D {
namespace Engine {
namespace Interfaces {

// Forward declarations
class ISimulationEngine;

/**
 * @brief Interface for the engine factory
 *
 * This interface defines the contract for creating engine instances.
 * It allows for dependency injection and easy mocking for tests.
 */
class IEngineFactory {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~IEngineFactory() = default;

    /**
     * @brief Create a simulation engine instance
     * @param parent The parent QObject for memory management
     * @return A new simulation engine instance
     */
    virtual ISimulationEngine* createSimulationEngine(QObject* parent = nullptr) = 0;
};

} // namespace Interfaces
} // namespace Engine
} // namespace Vizion3D

#endif // IENGINE_FACTORY_H
