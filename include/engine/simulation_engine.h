#ifndef SIMULATION_ENGINE_H
#define SIMULATION_ENGINE_H

#include "engine/interfaces/simulation_engine_interface.h"
#include "engine/interfaces/toolpath_point.h"
#include "utils/result.h"

#include <QObject>
#include <QVector>
#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QScopedPointer>

namespace Vizion3D {
namespace Engine {

/**
 * @brief Forward declaration of the SimulationWorker class
 */
class SimulationWorker;

/**
 * @brief Concrete implementation of the simulation engine interface
 *
 * This class provides a Qt-friendly implementation of the simulation engine
 * that adapts the original C++ engine to use Qt's paradigms (signals/slots,
 * Qt data types).
 */
class SimulationEngine : public Interfaces::ISimulationEngine {
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent The parent QObject
     */
    explicit SimulationEngine(QObject *parent = nullptr);

    /**
     * @brief Destructor
     */
    ~SimulationEngine() override;

    // Prevent copying
    SimulationEngine(const SimulationEngine &) = delete;
    SimulationEngine &operator=(const SimulationEngine &) = delete;

    /**
     * @brief Initialize the simulation engine
     * @param configPath Optional path to a configuration file
     * @return Result indicating success or failure with error details
     */
    Vizion3D::Utils::Result<void> initialize(const QString& configPath = QString()) override;

    /**
     * @brief Parse G-code and generate toolpath
     * @param gcode The G-code to parse
     * @return Result indicating success or failure with error details
     */
    Vizion3D::Utils::Result<void> parseGCode(const QString& gcode) override;

    /**
     * @brief Get the generated toolpath
     * @return Result containing vector of toolpath points or error details
     */
    Vizion3D::Utils::Result<QVector<Interfaces::ToolpathPoint>> getToolpath() const override;

    /**
     * @brief Start the simulation
     * @param timeStep The time step for simulation in seconds
     * @return Result indicating success or failure with error details
     */
    Vizion3D::Utils::Result<void> startSimulation(double timeStep = 0.1) override;

    /**
     * @brief Pause the simulation
     */
    void pauseSimulation() override;

    /**
     * @brief Resume the simulation
     */
    void resumeSimulation() override;

    /**
     * @brief Stop the simulation
     */
    void stopSimulation() override;

    /**
     * @brief Step the simulation
     */
    void stepSimulation() override;

    /**
     * @brief Reset the simulation back to the beginning
     */
    void resetSimulation() override;

    /**
     * @brief Load a machine definition
     * @param filePath Path to the machine definition file
     * @return Result indicating success or failure with error details
     */
    Vizion3D::Utils::Result<void> loadMachine(const QString& filePath) override;

    /**
     * @brief Load a 3D model
     * @param filePath Path to the model file
     * @param name Unique name for the model
     * @return Result indicating success or failure with error details
     */
    Vizion3D::Utils::Result<void> loadModel(const QString& filePath, const QString& name) override;

    /**
     * @brief Export the workpiece to a file
     * @param filePath Path to the output file
     * @param fileFormat Format of the output file (STL, STEP, etc.)
     * @return Result indicating success or failure with error details
     */
    Vizion3D::Utils::Result<void> exportWorkpiece(const QString& filePath, const QString& fileFormat) override;

    /**
     * @brief Check if the simulation is running
     * @return True if the simulation is running, false otherwise
     */
    bool isSimulationRunning() const override;

    /**
     * @brief Check if the simulation is paused
     * @return True if the simulation is paused, false otherwise
     */
    bool isSimulationPaused() const override;

    /**
     * @brief Check if the simulation is in stepping mode
     * @return True if the simulation is in stepping mode, false otherwise
     */
    bool isSteppingMode() const override;

    /**
     * @brief Get the current volume of the workpiece
     * @return Volume in cubic units
     */
    double getWorkpieceVolume() const override;

    /**
     * @brief Check for collisions in the current scene
     * @return Vector of collision descriptions
     */
    QStringList checkCollisions() override;

    /**
     * @brief Simulate material removal
     * @param gcode G-code string to simulate
     * @param timeStep Time step for simulation in seconds
     * @return Result indicating success or failure with error details
     */
    Vizion3D::Utils::Result<void> simulateMaterialRemoval(const QString& gcode, double timeStep = 0.1) override;

    /**
     * @brief Set the simulation speed
     * @param speed The simulation speed factor (1.0 = normal speed)
     */
    void setSimulationSpeed(double speed) override;

    /**
     * @brief Get the simulation speed
     * @return The simulation speed factor
     */
    double getSimulationSpeed() const override;

    /**
     * @brief Get the current simulation position
     * @return The current position as a ToolpathPoint
     */
    Interfaces::ToolpathPoint getCurrentPosition() const;

    /**
     * @brief Process the next line of G-code
     * @return Result indicating success or failure with error details
     *
     * This method processes the next line of G-code and updates the simulation state.
     * It returns success if a line was processed, or error if processing failed.
     */
    Vizion3D::Utils::Result<void> processNextLine() override;

private:
    /**
     * @brief Private implementation class
     *
     * This class contains the actual implementation of the simulation engine,
     * adapting the original C++ engine to use Qt's paradigms.
     */
    class Private;

    /**
     * @brief Pointer to the private implementation
     */
    QScopedPointer<Private> d;
};

/**
 * @brief Worker class for running the simulation in a separate thread
 */
class SimulationWorker : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param toolpath The toolpath to simulate
     * @param timeStep The time step for simulation
     * @param parent The parent QObject
     */
    SimulationWorker(const QVector<Interfaces::ToolpathPoint> &toolpath, double timeStep, QObject *parent = nullptr);

public slots:
    /**
     * @brief Process the simulation
     *
     * This method runs the simulation loop in a separate thread.
     */
    void process();

    /**
     * @brief Pause the simulation
     */
    void pause();

    /**
     * @brief Resume the simulation
     */
    void resume();

    /**
     * @brief Stop the simulation
     */
    void stop();

signals:
    /**
     * @brief Signal emitted for each simulation step
     * @param position The current position
     */
    void step(const Interfaces::ToolpathPoint &position);

    /**
     * @brief Signal emitted to report simulation progress
     * @param progress The progress as a value between 0.0 and 1.0
     */
    void progress(double progress);

    /**
     * @brief Signal emitted when the simulation is completed
     */
    void finished();

    /**
     * @brief Signal emitted when a simulation error occurs
     * @param error The structured error information
     */
    void error(const Vizion3D::Utils::Error &error);

private:
    QVector<Interfaces::ToolpathPoint> m_toolpath;
    double m_timeStep;
    bool m_shouldStop;
    bool m_isPaused;
    QMutex m_mutex;
    QWaitCondition m_pauseCondition;
};

} // namespace Engine
} // namespace Vizion3D

#endif // SIMULATION_ENGINE_H
