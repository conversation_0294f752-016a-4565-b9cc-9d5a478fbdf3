#ifndef ENGINE_FACTORY_H
#define ENGINE_FACTORY_H

#include "engine/interfaces/engine_factory_interface.h"

namespace Vizion3D {
namespace Engine {

/**
 * @brief Factory class for creating engine instances
 *
 * This class provides methods for creating instances of the simulation engine
 * and other engine components.
 */
class EngineFactory : public Interfaces::IEngineFactory {
public:
    /**
     * @brief Constructor
     */
    EngineFactory();

    /**
     * @brief Destructor
     */
    ~EngineFactory() override;

    /**
     * @brief Create a simulation engine instance
     * @param parent The parent QObject for memory management
     * @return A new simulation engine instance
     */
    Interfaces::ISimulationEngine* createSimulationEngine(QObject* parent = nullptr) override;
};

} // namespace Engine
} // namespace Vizion3D

#endif // ENGINE_FACTORY_H
