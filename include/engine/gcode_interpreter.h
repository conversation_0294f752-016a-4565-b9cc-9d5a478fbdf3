#ifndef GCODE_INTERPRETER_H
#define GCODE_INTERPRETER_H

#include <QObject>
#include <QString>
#include <QVector>
#include <QMap>
#include <QMutex>

#include "engine/toolpath_point.h"
#include "utils/result.h"

namespace Vizion3D {
namespace Engine {

/**
 * @brief Class for interpreting G-code and generating toolpaths
 *
 * This class provides functionality for parsing G-code and generating
 * toolpaths that can be used for simulation and visualization.
 */
class GCodeInterpreter : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent The parent QObject
     */
    explicit GCodeInterpreter(QObject *parent = nullptr);

    /**
     * @brief Destructor
     */
    ~GCodeInterpreter() override;

    /**
     * @brief Parse G-code and generate toolpath
     * @param gcode The G-code to parse
     * @return Result indicating success or failure with error details
     *
     * This method parses the provided G-code and generates a toolpath.
     * The parsing is done asynchronously in a separate thread, and progress
     * is reported through signals.
     */
    Vizion3D::Utils::Result<void> parseGCode(const QString &gcode);

    /**
     * @brief Load G-code from a file and generate toolpath
     * @param filePath Path to the G-code file
     * @return Result indicating success or failure with error details
     *
     * This method loads G-code from a file and parses it to generate a toolpath.
     * The parsing is done asynchronously in a separate thread, and progress
     * is reported through signals.
     */
    Vizion3D::Utils::Result<void> loadGCode(const QString &filePath);

    /**
     * @brief Get the generated toolpath
     * @return Result containing vector of toolpath points or error details
     */
    Vizion3D::Utils::Result<QVector<ToolpathPoint>> getToolpath() const;

    /**
     * @brief Simulate execution of the toolpath with interpolation
     * @param timeStep Time step for simulation in seconds
     * @return Result containing vector of interpolated toolpath points or error details
     *
     * This method simulates the execution of the toolpath with the specified
     * time step, generating intermediate points for smooth animation.
     */
    Vizion3D::Utils::Result<QVector<ToolpathPoint>> simulateExecution(double timeStep) const;

    /**
     * @brief Process a single line of G-code
     * @param line The G-code line to process
     * @return Result containing the toolpath point generated from this line or error details
     *
     * This method processes a single line of G-code and updates the internal state.
     * It returns the new toolpath point if the line resulted in a movement, or error
     * if the line could not be processed (e.g., syntax errors, invalid commands).
     */
    Vizion3D::Utils::Result<ToolpathPoint> processLine(const QString &line);

    /**
     * @brief Get the parsed G-code with annotations
     * @return Result containing map of line numbers to annotated G-code lines or error details
     */
    Vizion3D::Utils::Result<QMap<int, QString>> getAnnotatedGCode() const;

    /**
     * @brief Check if parsing is in progress
     * @return True if parsing is in progress, false otherwise
     */
    bool isParsingInProgress() const;

    /**
     * @brief Cancel the current parsing operation
     */
    void cancelParsing();

signals:
    /**
     * @brief Signal emitted when parsing starts
     */
    void parsingStarted();

    /**
     * @brief Signal emitted to report parsing progress
     * @param lineNumber The current line number being parsed
     * @param totalLines The total number of lines to parse
     */
    void parsingProgress(int lineNumber, int totalLines);

    /**
     * @brief Signal emitted when parsing is completed successfully
     */
    void parsingCompleted();

    /**
     * @brief Signal emitted when a parsing error occurs
     * @param error The structured error information
     */
    void parsingError(const Vizion3D::Utils::Error &error);

    /**
     * @brief Signal emitted when parsing is canceled
     */
    void parsingCanceled();

public:
    // Prevent copying
    GCodeInterpreter(const GCodeInterpreter &) = delete;
    GCodeInterpreter &operator=(const GCodeInterpreter &) = delete;

private:
    class Private;
    Private *d;
};

} // namespace Engine
} // namespace Vizion3D

#endif // GCODE_INTERPRETER_H
