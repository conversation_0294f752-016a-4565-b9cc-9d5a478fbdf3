#ifndef TOOLPATH_POINT_H
#define TOOLPATH_POINT_H

#include <QVector3D>
#include <QString>

namespace Vizion3D {
namespace Engine {

/**
 * @brief Enum representing the type of toolpath move
 */
enum class MoveType {
    Rapid,      ///< Rapid positioning (G0)
    Linear,     ///< Linear interpolation (G1)
    ArcCW,      ///< Circular interpolation clockwise (G2)
    ArcCCW,     ///< Circular interpolation counter-clockwise (G3)
    Dwell,      ///< Dwell (G4)
    Unknown     ///< Unknown move type
};

/**
 * @brief Class representing a point in the toolpath
 *
 * This class represents a single point in the toolpath, including position,
 * move type, feed rate, and line number in the G-code file.
 */
class ToolpathPoint {
public:
    /**
     * @brief Default constructor
     */
    ToolpathPoint();

    /**
     * @brief Constructor with position
     * @param position The position of the point
     */
    explicit ToolpathPoint(const QVector3D &position);

    /**
     * @brief Constructor with position and move type
     * @param position The position of the point
     * @param moveType The type of move
     */
    ToolpathPoint(const QVector3D &position, MoveType moveType);

    /**
     * @brief Constructor with all parameters
     * @param position The position of the point
     * @param moveType The type of move
     * @param feedRate The feed rate for the move
     * @param lineNumber The line number in the G-code file
     */
    ToolpathPoint(const QVector3D &position, MoveType moveType, double feedRate, int lineNumber);

    /**
     * @brief Get the position of the point
     * @return The position as a QVector3D
     */
    QVector3D position() const;

    /**
     * @brief Set the position of the point
     * @param position The new position
     */
    void setPosition(const QVector3D &position);

    /**
     * @brief Get the move type
     * @return The move type
     */
    MoveType moveType() const;

    /**
     * @brief Set the move type
     * @param moveType The new move type
     */
    void setMoveType(MoveType moveType);

    /**
     * @brief Get the feed rate
     * @return The feed rate in units per minute
     */
    double feedRate() const;

    /**
     * @brief Set the feed rate
     * @param feedRate The new feed rate in units per minute
     */
    void setFeedRate(double feedRate);

    /**
     * @brief Get the line number in the G-code file
     * @return The line number
     */
    int lineNumber() const;

    /**
     * @brief Set the line number in the G-code file
     * @param lineNumber The new line number
     */
    void setLineNumber(int lineNumber);

    /**
     * @brief Convert move type to string
     * @param moveType The move type to convert
     * @return String representation of the move type
     */
    static QString moveTypeToString(MoveType moveType);

private:
    QVector3D m_position;  ///< Position of the point
    MoveType m_moveType;   ///< Type of move
    double m_feedRate;     ///< Feed rate in units per minute
    int m_lineNumber;      ///< Line number in the G-code file
};

} // namespace Engine
} // namespace Vizion3D

#endif // TOOLPATH_POINT_H
