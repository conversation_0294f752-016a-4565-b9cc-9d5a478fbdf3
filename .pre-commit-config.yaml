# .pre-commit-config.yaml
# Pre-commit configuration for Vizion3D
#
# This configuration implements the graduated quality check approach:
# - Fast checks run on every commit
# - Medium checks can be enabled for stricter enforcement
# - Full checks run in CI/CD pipeline

repos:
  # Vizion3D Custom Quality Checks (Optimized)
  - repo: local
    hooks:
      # Fast parallel pre-commit analysis (replaces individual hooks)
      - id: vizion3d-fast-analysis
        name: Vizion3D Fast Parallel Analysis
        entry: scripts/pre-commit/fast-precommit-dispatcher.sh
        language: script
        pass_filenames: false
        stages: [pre-commit]
        always_run: true

      # Legacy hooks (disabled - use fast-analysis instead)
      # - id: vizion3d-logging-compliance
      #   name: Vizion3D Logging Compliance with Auto-fix
      #   entry: scripts/pre-commit/logging-precommit-hook.sh
      #   language: script
      #   pass_filenames: false
      #   stages: [pre-commit]
      #   always_run: true

      # - id: vizion3d-clang-tidy
      #   name: Vizion3D Clang-Tidy Analysis
      #   entry: scripts/pre-commit/clang-tidy-precommit-hook.sh
      #   language: script
      #   pass_filenames: false
      #   stages: [pre-commit]
      #   always_run: true

      # Medium pre-push checks (optional, can be enabled)
      - id: vizion3d-medium-check
        name: Vizion3D Medium Quality Check
        entry: scripts/pre-commit/medium-check.sh
        language: script
        pass_filenames: false
        stages: [pre-push]
        always_run: true

  # Standard formatting and basic checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        exclude: \.(md|rst)$
      - id: end-of-file-fixer
        exclude: \.(md|rst)$
      - id: check-yaml
        args: ['--unsafe']  # Allow custom YAML tags
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-merge-conflict
      - id: check-case-conflict

  # C++ formatting (optional - can be enabled if desired)
  # - repo: https://github.com/pre-commit/mirrors-clang-format
  #   rev: v16.0.6
  #   hooks:
  #     - id: clang-format
  #       files: \.(cpp|cxx|cc|h|hpp)$
  #       args: ['-i']  # In-place formatting

# Configuration for different enforcement levels
default_stages: [pre-commit]

# Exclude patterns (adjust as needed)
exclude: |
  (?x)^(
    build/.*|
    \.git/.*|
    test_opengl.*|
    .*\.autogen/.*
  )$
