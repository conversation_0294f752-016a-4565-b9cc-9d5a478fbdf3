---
Checks: >
  -*,
  readability-*,
  performance-*,
  bugprone-*,
  clang-analyzer-*,
  modernize-*,
  cppcoreguidelines-*,
  -readability-magic-numbers,
  -readability-function-cognitive-complexity,
  -readability-identifier-length,
  -readability-implicit-bool-conversion,
  -readability-static-accessed-through-instance,
  -readability-redundant-access-specifiers,
  -readability-uppercase-literal-suffix,
  -readability-duplicate-include,
  -readability-convert-member-functions-to-static,
  -readability-non-const-parameter,
  -readability-braces-around-statements,
  -readability-make-member-function-const,
  -readability-else-after-return,
  -bugprone-narrowing-conversions,
  -bugprone-implicit-widening-of-multiplication-result,
  -bugprone-easily-swappable-parameters,
  -clang-analyzer-deadcode.DeadStores,
  -clang-analyzer-core.NullDereference,
  -clang-analyzer-cplusplus.NewDeleteLeaks,
  -readability-inconsistent-declaration-parameter-name,
  -performance-enum-size,
  # Modernize checks to exclude (too aggressive or Qt-incompatible)
  -modernize-pass-by-value,
  -modernize-use-trailing-return-type,
  -modernize-use-nodiscard,
  -modernize-replace-auto-ptr,
  -modernize-concat-nested-namespaces,
  # Core Guidelines checks to exclude (Qt-incompatible or too strict)
  -cppcoreguidelines-avoid-non-const-global-variables,
  -cppcoreguidelines-pro-bounds-array-to-pointer-decay,
  -cppcoreguidelines-pro-bounds-constant-array-index,
  -cppcoreguidelines-pro-bounds-pointer-arithmetic,
  -cppcoreguidelines-macro-usage,
  -cppcoreguidelines-non-private-member-variables-in-classes,
  -cppcoreguidelines-avoid-magic-numbers,
  -cppcoreguidelines-pro-type-member-init

CheckOptions:
  # Existing naming conventions
  - key: readability-identifier-naming.ClassCase
    value: CamelCase
  - key: readability-identifier-naming.MethodCase
    value: camelBack
  - key: readability-identifier-naming.VariableCase
    value: camelBack
  - key: readability-identifier-naming.NamespaceCase
    value: CamelCase
  - key: readability-identifier-naming.FunctionCase
    value: camelBack
  # Ignore Qt's auto-connect slot naming convention
  - key: readability-identifier-naming.MethodIgnoredRegexp
    value: '^on_.*_(clicked|triggered|valueChanged|textChanged|currentIndexChanged)$'
  # Ignore Qt's role constants naming convention
  - key: readability-identifier-naming.VariableIgnoredRegexp
    value: '.*Role$|^s_.*'

  # Modernize check configurations
  - key: modernize-use-auto.MinTypeNameLength
    value: '5'
  - key: modernize-use-auto.RemoveStars
    value: 'false'
  - key: modernize-loop-convert.MaxCopySize
    value: '16'
  - key: modernize-loop-convert.MinConfidence
    value: 'safe'
  - key: modernize-make-unique.MakeSmartPtrFunction
    value: 'std::make_unique'
  - key: modernize-make-shared.MakeSmartPtrFunction
    value: 'std::make_shared'

  # Core Guidelines configurations
  - key: cppcoreguidelines-special-member-functions.AllowSoleDefaultDtor
    value: 'true'
  - key: cppcoreguidelines-special-member-functions.AllowMissingMoveFunctions
    value: 'true'
  - key: cppcoreguidelines-narrowing-conversions.WarnOnFloatingPointNarrowingConversion
    value: 'false'
  - key: cppcoreguidelines-narrowing-conversions.WarnOnIntegerNarrowingConversion
    value: 'true'
  - key: cppcoreguidelines-owning-memory.LegacyResourceProducers
    value: '::malloc;::aligned_alloc;::realloc;::calloc;::fopen;::freopen;::tmpfile'
  - key: cppcoreguidelines-owning-memory.LegacyResourceConsumers
    value: '::free;::realloc;::freopen;::fclose'

WarningsAsErrors: ''
# Only analyze our source code, exclude Qt auto-generated files
HeaderFilterRegex: '^(?!.*/(build|.*_autogen)/).*/(include|src)/.*\.(h|hpp)$'
FormatStyle: file

# Qt Ownership Model Exception
# ---------------------------
# In Qt UI code, parent-child ownership is the standard for widgets and QTreeWidgetItem.
# clang-tidy's cppcoreguidelines-owning-memory check may produce false positives for these cases.
# It is acceptable and recommended to use:
#   // NOLINT(cppcoreguidelines-owning-memory) Qt parent-child ownership
# on lines where Qt parent pointers are used, and never use raw delete on parented Qt objects.
#
# This policy is project-wide and reviewers should accept NOLINT with this justification in Qt UI code.
