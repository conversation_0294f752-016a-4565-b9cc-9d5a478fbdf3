{"name": "Require Tests for C++ Logic", "scope": "include, src", "description": "All new C++ modules or functions must include appropriate tests.", "rule": "Whenever adding logic in C++, include unit tests in a corresponding test file. Test both success and error paths for Result<T> functions. Ensure tests pass locally before committing.", "rationale": "Maintains code correctness, prevents regressions, and ensures proper error handling with Result<T> pattern.", "examples": {"good": ["// Test Result<T> success case\nTEST_CASE(\"parseGCode success\") { auto result = interpreter.parseGCode(validGCode); REQUIRE(result.isSuccess()); }", "// Test Result<T> error case\nTEST_CASE(\"parseGCode error\") { auto result = interpreter.parseGCode(invalidGCode); REQUIRE(result.isError()); REQUIRE(result.error().code == ErrorCode::GCodeSyntaxError); }", "// Test error propagation\nTEST_CASE(\"error propagation\") { auto result = engine.startSimulation(); REQUIRE(result.isError()); REQUIRE(result.error().category == \"Engine\"); }"], "bad": ["// Complex parsing function with no tests\nResult<void> parseGCode(const QString& code) { ... }", "// Testing only success path\nTEST_CASE(\"parseGCode\") { auto result = interpreter.parseGCode(validGCode); REQUIRE(result.isSuccess()); } // Missing error case tests"]}}