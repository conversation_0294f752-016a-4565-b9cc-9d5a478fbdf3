{"name": "Ensure Cross-Platform Compatibility", "scope": "*", "description": "Validate that any OS interaction works on macOS, Windows, and Linux.", "rule": "When adding file paths or OS-specific logic, use Qt's cross-platform APIs (QDir, QFile, etc.). Test or mock behavior on the three main OSes before merging.", "rationale": "Vizion3D is distributed to all major desktop platforms.", "examples": {"good": ["QString homePath = QDir::homePath();", "QFile file(filePath);"], "bad": ["std::string homePath = \"/home/<USER>\"; // linux-only", "FILE* file = fopen(\"/tmp/file.txt\", \"r\"); // platform-specific path"]}}