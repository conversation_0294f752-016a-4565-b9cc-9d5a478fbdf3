{"name": "Avoid Hard-Coded Paths & Temp Dirs", "scope": "*", "description": "Use Qt's platform-agnostic helpers, never hard-code absolute paths.", "rule": "Utilize Qt's QDir, QFile, QStandardPaths, and QTemporaryFile for file and directory handling. Do not embed user-specific or OS-specific paths in code.", "rationale": "Ensures cross-platform compatibility and sandbox safety.", "examples": {"good": ["QString tempDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation);", "QTemporaryFile tempFile;"], "bad": ["QString tempDir = \"/tmp/vizion3d\";", "std::string userHome = \"/Users/<USER>\";"]}}