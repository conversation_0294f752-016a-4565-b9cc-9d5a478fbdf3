{"name": "Profiling Best Practices", "scope": "include, src", "description": "Use the Vizion3D profiling macros for performance-critical code.", "rule": "Add VIZION3D_PROFILE_FUNCTION() to performance-critical functions and VIZION3D_PROFILE_BLOCK(\"Name\") for specific code blocks that need profiling. Always profile OpenGL rendering and G-code processing operations.", "rationale": "Helps identify performance bottlenecks and optimize critical code paths.", "examples": {"good": ["void renderScene() { VIZION3D_PROFILE_FUNCTION(); /* ... */ }", "void processGCode() { VIZION3D_PROFILE_FUNCTION(); /* ... */ }"], "bad": ["void renderScene() { /* No profiling for performance-critical function */ }", "// Missing VIZION3D_PROFILE_BLOCK for expensive loop"]}}