{"name": "Document Public APIs", "scope": "frontend/src, src-tauri/src, engine", "description": "Require doc comments for public modules, functions, and components.", "rule": "For TypeScript, use JSDoc/TSDoc. For Rust, use triple-slash `///`. For C++, use Doxygen-style comments. Docs should describe parameters, return types, and examples where helpful.", "rationale": "Helps maintainability and enables auto-generated documentation.", "examples": {"good": ["/// Loads a project from disk\n///\n/// path - absolute path to .viz3d file\nfn load_project(path: &str) -> Result<Project> { ... }"], "bad": ["fn load_project(path: &str) -> Result<Project> { ... } // undocumented"]}}