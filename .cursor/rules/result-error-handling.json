{"name": "Result<T> E<PERSON><PERSON>", "scope": "include, src", "description": "Use Result<T> pattern for consistent error handling with automatic logging.", "rule": "All functions that can fail must return Result<T>. Use Result<T>::error() with appropriate ErrorCode for automatic logging and categorization. Handle Result<T> in calling code with .isSuccess()/.error() pattern. Propagate errors up the stack efficiently.", "rationale": "Provides structured error handling with automatic logging, categorization, and consistent error propagation throughout the application.", "examples": {"good": ["// Engine function returning Result<T>", "Vizion3D::Utils::Result<QVector<ToolpathPoint>> getToolpath() const { if (!initialized) return Result<QVector<ToolpathPoint>>::error(ErrorCode::NotInitialized, \"Engine not initialized\"); return Result<QVector<ToolpathPoint>>::success(toolpath); }", "// UI handling Result<T> with proper error checking", "auto result = engine->getToolpath(); if (!result.isSuccess()) { const auto& error = result.error(); VLOG_ERROR(\"UI\", \"Failed to get toolpath: \" + error.message); QMessageBox::critical(this, \"Error\", error.message); return; }", "// Error propagation in engine layer", "auto parseResult = interpreter->parseGCode(gcode); if (!parseResult.isSuccess()) { return parseResult; } // Propagate error up", "// Include proper headers", "#include \"utils/result.h\" // For Result<T> and ErrorCode"], "bad": ["bool getToolpath(QVector<ToolpathPoint>& out) { /* ... */ } // Use Result<T> instead", "QVector<ToolpathPoint>* getToolpath() { return nullptr; } // Use Result<T> instead", "void parseGCode() { throw std::runtime_error(\"error\"); } // Use Result<T>::error() instead", "auto result = engine->getToolpath(); toolpath = result.value(); // Missing error checking"]}}