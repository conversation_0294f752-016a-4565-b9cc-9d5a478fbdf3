{"name": "Logging Best Practices", "scope": "include, src", "description": "Use the Vizion3D logging system with Result<T> error handling and VLOG_* macros consistently.", "rule": "Use Result<T>::error() for functions that can fail (automatic logging). Use VLOG_* macros for informational logging and adding context around errors. Include 'utils/result.h' for Result<T> or 'utils/logger.h' for direct VLOG_* usage.", "rationale": "Ensures consistent error handling and logging across the codebase with automatic categorization and appropriate log levels.", "examples": {"good": ["// Engine functions that can fail - use Result<T> with automatic logging", "Vizion3D::Utils::Result<void> parseGCode(const QString& gcode) { if (error) return Result<void>::error(ErrorCode::GCodeSyntaxError, \"Invalid syntax\"); }", "// UI context and informational logging - use VLOG_*", "VLOG_INFO(\"UI\", \"Loading project from: \" + filePath);", "// UI error handling - handle Result<T> and add context", "auto result = engine->parseGCode(gcode); if (!result.isSuccess()) { VLOG_ERROR(\"UI\", \"User action failed: \" + result.error().message); }"], "bad": ["qDebug() << \"Loading project\"; // Use VLOG_* instead", "bool parseGCode() { /* error handling */ return false; } // Use Result<T> instead", "throw std::runtime_error(\"Parse error\"); // Use Result<T>::error() instead"]}}