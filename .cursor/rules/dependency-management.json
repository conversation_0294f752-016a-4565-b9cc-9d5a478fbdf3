{"name": "Propose New Dependencies Sparingly", "scope": "frontend/package.json, src-tauri/Cargo.toml", "description": "Require justification for adding external libraries.", "rule": "When suggesting or adding a new npm or Cargo dependency, first verify that built-in APIs or existing packages cannot cover the need. Provide rationale and maintenance info (stars, last commit) in the PR.", "rationale": "Controls bundle size, audit surface, and update workload.", "examples": {"good": ["Proposal: add 'zod' for schema validation because we currently hand-roll validation in 3 places and it's error-prone. zod has 10k stars and is actively maintained."], "bad": ["Added lodash for \"convenience\" without explanation"]}}