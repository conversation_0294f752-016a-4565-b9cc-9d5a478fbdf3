{"name": "Manage Large Binary Assets with Git-LFS", "scope": "*", "description": "Prevent large binaries from bloating the repository.", "rule": "Add STL or other large assets via Git-LFS and update .gitattributes. Do not commit large binaries directly.", "rationale": "Keeps repository size small and clone times fast.", "examples": {"good": ["git lfs track '*.stl'"], "bad": ["git add sample_model.stl (100 MB)"]}}