{"name": "Qt Best Practices", "scope": "include, src", "description": "Follow Qt best practices for signals, slots, and memory management.", "rule": "Use new-style signal/slot syntax, prefer Qt's parent-child ownership model, and follow Qt's memory management patterns. Use Qt's container classes when appropriate.", "rationale": "Ensures code follows Qt idioms and prevents memory leaks and crashes.", "examples": {"good": ["connect(sender, &Sender::signal, receiver, &Receiver::slot);", "QLabel* label = new QLabel(this); // parent-child ownership"], "bad": ["connect(sender, SIGNAL(signal()), receiver, SLOT(slot())); // old-style", "QLabel* label = new QLabel(); // no parent, potential memory leak"]}}