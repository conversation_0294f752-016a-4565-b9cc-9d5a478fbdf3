{"name": "Maintain Clean Component Boundaries", "scope": "include, src", "description": "Maintain strict component boundaries with clear interfaces.", "rule": "C++ code should be organized by component with clear interfaces. UI code stays in `ui/` directories, engine code in `engine/`, and utility code in `utils/`. Components should communicate through well-defined interfaces.", "rationale": "Prevents accidental complexity and maintains a clean architecture that's easy to test and maintain.", "examples": {"good": ["UI component using engine interface to start simulation", "Engine component exposing a clean interface for UI interaction"], "bad": ["UI code directly manipulating engine internals", "Mixing UI and engine logic in the same class"]}}