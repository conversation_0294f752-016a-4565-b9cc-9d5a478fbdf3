{"name": "Robust Error Handling with <PERSON><PERSON>t<T>", "scope": "include, src", "description": "Use Result<T> pattern for structured error handling with automatic logging.", "rule": "Use Result<T>::error() for functions that can fail (engine operations, file I/O, initialization). Handle Result<T> in UI code and add context with VLOG_* macros. Use try/catch only for external library exceptions, then convert to Result<T>. Never swallow errors silently.", "rationale": "Provides consistent, structured error handling with automatic logging and categorization, improving debuggability and user experience.", "examples": {"good": ["// Engine function with Result<T> error handling", "Result<void> loadFile(const QString& path) { try { file.open(path); return Result<void>::success(); } catch (const std::exception& e) { return Result<void>::error(ErrorCode::FileReadError, e.what()); } }", "// UI handling Result<T> with context", "auto result = engine->loadFile(path); if (!result.isSuccess()) { VLOG_ERROR(\"UI\", \"User file load failed: \" + result.error().message); showErrorDialog(\"Failed to load file\"); }", "// Convert external exceptions to Result<T>", "try { externalLibraryCall(); } catch (const std::exception& e) { return Result<void>::error(ErrorCode::InternalError, e.what()); }"], "bad": ["file.open(path); // no error handling", "bool loadFile() { try { file.open(); } catch (...) { return false; } } // Use Result<T> instead", "void loadFile() { try { file.open(); } catch (...) { /* silent failure */ } } // Never swallow errors"]}}