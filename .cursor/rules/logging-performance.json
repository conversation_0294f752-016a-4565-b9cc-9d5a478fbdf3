{"name": "Logging Performance Best Practices", "scope": "include, src", "description": "Optimize logging performance while maintaining debugging capabilities.", "rule": "Use Result<T> for error paths (infrequent, automatic logging acceptable). Use VLOG_TRACE/DEBUG sparingly in hot paths. Configure category log levels appropriately for production vs development. Use conditional logging for expensive debug information generation.", "rationale": "Ensures logging provides value without impacting application performance, especially in performance-critical rendering and simulation code.", "examples": {"good": ["// Result<T> for error paths (infrequent)", "Result<void> parseGCode() { if (error) return Result<void>::error(ErrorCode::GCodeSyntaxError, \"Invalid syntax\"); }", "// Conditional expensive logging", "if (LogConfig::instance().categoryLogLevel(\"Engine\") <= LogLevel::Debug) { QString expensiveDebugInfo = generateDetailedState(); VLOG_DEBUG(\"Engine\", expensiveDebugInfo); }", "// Appropriate log levels for hot paths", "VLOG_TRACE(\"OpenGL\", \"Rendering frame\"); // Can be disabled in production", "// Production configuration", "LogConfig::instance().setCategoryLogLevel(\"GCode\", LogLevel::Warning); // Reduce verbose logging"], "bad": ["// Expensive logging in hot paths", "for (auto& point : toolpath) { VLOG_DEBUG(\"Engine\", QString(\"Processing point %1,%2,%3\").arg(point.x).arg(point.y).arg(point.z)); }", "// Always generating expensive debug info", "QString debugInfo = generateExpensiveDebugInfo(); VLOG_DEBUG(\"Engine\", debugInfo); // Generate even if not logged", "// Using Result<T> for frequent success cases without need", "Result<int> getToolpathSize() { return Result<int>::success(toolpath.size()); } // Simple getter doesn't need Result<T>"]}}