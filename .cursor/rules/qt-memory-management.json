{"name": "Qt Memory Management Best Practices", "scope": "include, src, tests", "description": "Enforce Qt parent-child ownership, require NOLINT for cppcoreguidelines-owning-memory, and ban raw delete on parented Qt objects.", "rule": "Always pass the parent pointer when creating Qt widgets or QTreeWidgetItem. Use NOLINT(cppcoreguidelines-owning-memory) with justification for Qt parent-child ownership. Never use raw delete on Qt objects with a parent; let Qt manage deletion. Commit hooks and code review must reject code that violates these patterns.", "rationale": "Qt's parent-child ownership model is the standard for memory management. clang-tidy may produce false positives, so NOLINT with justification is required. Manual deletion of parented Qt objects can cause crashes and leaks.", "examples": {"good": ["auto* widget = new QWidget(parent); // NOLINT(cppcoreguidelines-owning-memory) Qt parent-child ownership", "auto* item = new QTreeWidgetItem(parentItem); // NOLINT(cppcoreguidelines-owning-memory) Qt parent-child ownership"], "bad": ["auto* widget = new QWidget(); // No parent, memory leak risk", "delete widget; // Only if no parent, otherwise let Qt manage", "delete item; // Never manually delete a QTreeWidgetItem with a parent"]}}