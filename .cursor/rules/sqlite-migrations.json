{"name": "Require Deterministic SQLite Migrations", "scope": "src-tauri/src, db/migrations", "description": "Ensure schema changes are reproducible across environments.", "rule": "Add an idempotent SQL migration file for every schema change. Do not execute inline SQL for schema alteration in Rust handlers.", "rationale": "Prevents drift between local, CI, and production databases.", "examples": {"good": ["-- 2024_05_15_add_projects_table.sql\nCREATE TABLE IF NOT EXISTS projects (...)"], "bad": ["connection.execute(\"ALTER TABLE projects ...\") inside Rust command"]}}