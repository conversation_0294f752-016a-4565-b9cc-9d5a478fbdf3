{"name": "Maintain Strict Content Security Policy", "scope": "src-tauri/tauri.conf.json, frontend/src", "description": "Prevent accidental relaxation of CSP rules in production builds.", "rule": "Do not weaken the CSP in tauri.conf.json without explicit reviewer approval. Any proposed changes must document the security implications.", "rationale": "Protects the desktop app from XSS and injection attacks.", "examples": {"good": ["{ \"csp\": \"default-src 'self';\" }"], "bad": ["{ \"csp\": \"default-src * 'unsafe-inline' 'unsafe-eval';\" }"]}}