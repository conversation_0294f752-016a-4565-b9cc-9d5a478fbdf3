{"name": "Use Conventional Commits for semantic-release", "scope": "git, pull_requests", "description": "Standardize commit and PR titles for automated versioning.", "rule": "Prefix commits and PR titles with semantic keywords (`feat:`, `fix:`, `chore:`, `docs:`, `refactor:` etc.), keep subject ≤ 72 characters, wrap body at 100 chars.", "rationale": "semantic-release parses commit history to bump versions and generate changelogs.", "examples": {"good": ["feat: add STL slicing preview to viewer"], "bad": ["Added new feature"]}}