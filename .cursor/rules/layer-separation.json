{"name": "Enforce Layer Separation", "scope": "*", "description": "Ensure each component communicates only through its intended interfaces and never bypasses established boundaries.", "rule": "UI components must communicate with the simulation engine exclusively via the defined interfaces using Result<T> pattern. Engine layer returns Result<T> for operations that can fail. UI layer handles Result<T> and adds user-friendly context. Direct access to implementation details across component boundaries is prohibited.", "rationale": "Maintains a clean, testable, and secure architecture with consistent error handling patterns and prevents tight coupling across components.", "examples": {"good": ["MainWindow using ISimulationEngine interface with Result<T> handling: auto result = engine->startSimulation(); if (!result.isSuccess()) { showError(result.error().message); }", "SimulationView observing engine state changes through signals/slots with structured Error objects", "Engine returning Result<T>: Result<void> parseGCode(const QString& gcode) override;"], "bad": ["Directly accessing internal engine data structures from UI components", "Bypassing interfaces to call implementation details", "UI calling engine methods that return bool or throw exceptions instead of Result<T>"]}}