{"name": "Preserve Component-Based Folder Structure", "scope": "include, src", "description": "Place files according to component type and responsibility.", "rule": "Put UI-related code in `ui/` directories, engine code in `engine/`, and utility code in `utils/`. Headers go in `include/` with matching structure, implementations in `src/`. Keep interfaces in `interfaces/` subdirectories.", "rationale": "Keeps the codebase modular, navigable, and aligned with docs.", "examples": {"good": ["include/ui/project_tree_widget.h and src/ui/project_tree_widget.cpp", "include/engine/interfaces/simulation_engine_interface.h"], "bad": ["src/project_tree_widget.h (header not in include/)", "include/ui/engine_implementation.h (engine code in ui/)"]}}