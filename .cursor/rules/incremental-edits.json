{"name": "Prefer Incremental Scoped Edits", "scope": "assistant_suggestions", "description": "Encourage the AI to propose small, targeted changes.", "rule": "Suggest minimal diffs focusing on the immediate task unless a broader refactor is explicitly requested by the user.", "rationale": "Reduces merge conflicts and cognitive load during reviews.", "examples": {"good": ["Propose adding a single function to utils.ts instead of rewriting the entire file"], "bad": ["Refactor the whole project tree to hooks without prompting"]}}