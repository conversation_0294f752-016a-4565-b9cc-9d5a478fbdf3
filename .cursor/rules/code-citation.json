{"name": "Use Precise Code Citations", "scope": "assistant_responses", "description": "Format code excerpts so users can jump directly in Cursor.", "rule": "Always cite code with `startLine:endLine:filepath` formatting. Do not inline multi-line code without citation headers.", "rationale": "Enables quick navigation and maintains context for AI explanations.", "examples": {"good": ["```12:18:src/App.tsx\n// ... code ...\n```"], "bad": ["```\nfunction foo() { ... }\n```"]}}