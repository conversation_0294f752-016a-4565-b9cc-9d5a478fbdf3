<<<<<<< Updated upstream
=======
<<<<<<< Updated upstream
# Ignore .DS_Store files
=======
>>>>>>> Stashed changes
# C++ objects and libs
*.slo
*.lo
*.o
*.a
*.la
*.lai
*.so
*.so.*
*.dll
*.dylib

# Qt-es
object_script.*.Release
object_script.*.Debug
*_plugin_import.cpp
/.qmake.cache
/.qmake.stash
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.qmlc
*.jsc
Makefile*
*build-*
*.qm
*.prl

# Qt unit tests
target_wrapper.*

# QtCreator
*.autosave

# QtCreator Qml
*.qmlproject.user
*.qmlproject.user.*

# QtCreator CMake
CMakeLists.txt.user*

# QtCreator local machine specific files for imported projects
*creator.user*

# Build directories
build/
!scripts/build-verification/
!scripts/pre-commit/
debug/
release/
bin/
lib/

# CMake
CMakeCache.txt
CMakeFiles
CMakeScripts
Testing
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps

# Visual Studio
.vs/
*.sln
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.vcproj
*.vcproj.*.user
*.suo
*.ncb
*.sdf
*.opensdf
*.VC.db
*.VC.VC.opendb
ipch/
x64/
x86/
Win32/
Debug/
Release/
Obj/

# Xcode
*.xcodeproj
*.xcworkspace
xcuserdata/
DerivedData/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
*.xccheckout
*.moved-aside
*.hmap
*.ipa
*.dSYM.zip
*.dSYM

# macOS
<<<<<<< Updated upstream
=======
>>>>>>> Stashed changes
>>>>>>> Stashed changes
.DS_Store
.AppleDouble
.LSOverride
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.directory
.Trash-*

# IDE-specific files
.idea/
.vscode/
*.swp
*.swo
*~
.project
.cproject
.settings/
.classpath
.factorypath
.buildpath
nbproject/
.nb-gradle/

# Compiled resources
*.rcc

# Generated files
GeneratedFiles/
*.moc
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.qm
*.prl

# Dependency directories
vendor/
node_modules/

# Log files
*.log

# Temporary files
*.tmp
*.temp
*.bak
*.swp
*.swo
*~

# User-specific files
*.user
*.user.*
*.autosave

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
