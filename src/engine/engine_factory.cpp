#include "engine/engine_factory.h"
#include "engine/simulation_engine.h"

namespace Vizion3D {
namespace Engine {

EngineFactory::EngineFactory() = default;

EngineFactory::~EngineFactory() = default;

Interfaces::ISimulationEngine* EngineFactory::createSimulationEngine(QObject* parent)
{
    // NOLINTNEXTLINE(cppcoreguidelines-owning-memory) - Factory method intentionally returns new object
    return new SimulationEngine(parent);
}

} // namespace Engine
} // namespace Vizion3D
