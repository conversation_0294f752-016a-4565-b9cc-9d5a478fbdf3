#include "engine/toolpath_point.h"

namespace Vizion3D {
namespace Engine {

ToolpathPoint::ToolpathPoint()
    : m_position(QVector3D(0, 0, 0))
    , m_moveType(MoveType::Unknown)
    , m_feedRate(0.0)
    , m_lineNumber(-1)
{
}

ToolpathPoint::ToolpathPoint(const QVector3D &position)
    : m_position(position)
    , m_moveType(MoveType::Unknown)
    , m_feedRate(0.0)
    , m_lineNumber(-1)
{
}

ToolpathPoint::ToolpathPoint(const QVector3D &position, MoveType moveType)
    : m_position(position)
    , m_moveType(moveType)
    , m_feedRate(0.0)
    , m_lineNumber(-1)
{
}

ToolpathPoint::ToolpathPoint(const QVector3D &position, MoveType moveType, double feedRate, int lineNumber)
    : m_position(position)
    , m_moveType(moveType)
    , m_feedRate(feedRate)
    , m_lineNumber(lineNumber)
{
}

QVector3D ToolpathPoint::position() const
{
    return m_position;
}

void ToolpathPoint::setPosition(const QVector3D &position)
{
    m_position = position;
}

MoveType ToolpathPoint::moveType() const
{
    return m_moveType;
}

void ToolpathPoint::setMoveType(MoveType moveType)
{
    m_moveType = moveType;
}

double ToolpathPoint::feedRate() const
{
    return m_feedRate;
}

void ToolpathPoint::setFeedRate(double feedRate)
{
    m_feedRate = feedRate;
}

int ToolpathPoint::lineNumber() const
{
    return m_lineNumber;
}

void ToolpathPoint::setLineNumber(int lineNumber)
{
    m_lineNumber = lineNumber;
}

QString ToolpathPoint::moveTypeToString(MoveType moveType)
{
    switch (moveType) {
    case MoveType::Rapid:
        return "Rapid";
    case MoveType::Linear:
        return "Linear";
    case MoveType::ArcCW:
        return "Arc CW";
    case MoveType::ArcCCW:
        return "Arc CCW";
    case MoveType::Dwell:
        return "Dwell";
    case MoveType::Unknown:
    default:
        return "Unknown";
    }
}

} // namespace Engine
} // namespace Vizion3D
