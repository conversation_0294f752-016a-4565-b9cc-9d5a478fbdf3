#include "engine/simulation_engine.h"
#include "engine/interfaces/toolpath_point.h"
#include "engine/gcode_interpreter.h"
#include "utils/result.h"

#include <QDebug>
#include <QElapsedTimer>
#include <QThread>
#include <QMutexLocker>
#include <QTimer>
#include <QRegularExpression>
#include <QTime>
#include <QCoreApplication>

namespace Vizion3D {
namespace Engine {

/**
 * @brief Private implementation of SimulationEngine
 */
class SimulationEngine::Private : public QObject {
    Q_OBJECT
public slots:
    void onTimerTimeout();
    void handleSimulationCompletion();
    void handleSimulationError(const QString& errorMessage);

public:
    Private(SimulationEngine *q) : QObject(q), q(q), m_gCodeInterpreter(new GCodeInterpreter(q)) {
        // Connect to G-code interpreter signals
        connect(m_gCodeInterpreter, &GCodeInterpreter::parsingStarted, q, &SimulationEngine::parsingStarted);
        connect(m_gCodeInterpreter, &GCodeInterpreter::parsingProgress, q, &SimulationEngine::parsingProgress);
        connect(m_gCodeInterpreter, &GCodeInterpreter::parsingCompleted, q, &SimulationEngine::parsingCompleted);
        connect(m_gCodeInterpreter, &GCodeInterpreter::parsingError,
                [this, q](const Vizion3D::Utils::Error& error) {
                    emit q->parsingError(error);
                });
    }

    // Prevent copying
    Private(const Private&) = delete;
    Private& operator=(const Private&) = delete;

    ~Private() override {
        // Clean up simulation timer
        if (m_simulationTimer) {
            m_simulationTimer->stop();
            m_simulationTimer->deleteLater();
            m_simulationTimer = nullptr;
        }

        // Clean up worker thread
        if (m_workerThread) {
            if (m_workerThread->isRunning()) {
                m_workerThread->quit();
                m_workerThread->wait();
            }
            delete m_workerThread;
            m_workerThread = nullptr;
        }

        // Clean up worker
        if (m_worker) {
            delete m_worker;
            m_worker = nullptr;
        }

        // Clean up G-code interpreter
        if (m_gCodeInterpreter) {
            delete m_gCodeInterpreter;
            m_gCodeInterpreter = nullptr;
        }
    }

    Vizion3D::Utils::Result<void> initialize(const QString& configPath)
    {
        QMutexLocker locker(&m_mutex);

        if (m_initialized) {
            VLOG_DEBUG("Engine", "SimulationEngine already initialized");
            return Vizion3D::Utils::Result<void>::success();
        }

        try {
            // TODO: Initialize the engine components

            m_initialized = true;
            return Vizion3D::Utils::Result<void>::success();
        } catch (const std::exception& e) {
            VLOG_ERROR("Engine", QString("Error initializing simulation engine: %1").arg(e.what()));
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::InitializationFailed,
                QString("Error initializing simulation engine: %1").arg(e.what())
            );
        }
    }

    Vizion3D::Utils::Result<void> parseGCode(const QString& gcode)
    {
        QMutexLocker locker(&m_mutex);

        if (!m_initialized) {
            VLOG_WARNING("Engine", "SimulationEngine not initialized");
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::NotInitialized,
                "SimulationEngine not initialized"
            );
        }

        if (gcode.isEmpty()) {
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::InvalidArgument,
                "G-code string is empty"
            );
        }

        try {
            // Store the G-code lines for line-by-line processing
            m_gCodeLines = gcode.split(QRegularExpression("\\r?\\n"));
            m_currentLineIndex = 0;

            // Reset the toolpath
            m_toolpath.clear();

            // Add initial position to toolpath
            m_toolpath.append(Interfaces::ToolpathPoint(
                QVector3D(0, 0, 0),
                Interfaces::MoveType::Rapid,
                0.0,
                0
            ));

            // Set current position to the origin
            m_currentPosition = m_toolpath.first();

            return Vizion3D::Utils::Result<void>::success();
        } catch (const std::exception& e) {
            VLOG_ERROR("Engine", QString("Error parsing G-code: %1").arg(e.what()));
            auto error = Vizion3D::Utils::Error(
                Vizion3D::Utils::ErrorCode::GCodeSyntaxError,
                QString("Error parsing G-code: %1").arg(e.what()),
                "SimulationEngine"
            );
            emit q->parsingError(error);
            return Vizion3D::Utils::Result<void>::error(error);
        }
    }

    Vizion3D::Utils::Result<QVector<Interfaces::ToolpathPoint>> getToolpath() const
    {
        QMutexLocker locker(&m_mutex);
        return Vizion3D::Utils::Result<QVector<Interfaces::ToolpathPoint>>::success(m_toolpath);
    }

    Vizion3D::Utils::Result<void> startSimulation(double timeStep)
    {
        QMutexLocker locker(&m_mutex);

        if (!m_initialized) {
            VLOG_WARNING("Engine", "SimulationEngine not initialized");
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::NotInitialized,
                "SimulationEngine not initialized"
            );
        }

        // Track if we're transitioning from stepping mode (before we potentially clear the flag)
        bool transitioningFromStepping = m_steppingMode;

        // If simulation is paused, resume it instead of restarting
        if (m_simulationRunning && m_simulationPaused) {
            VLOG_DEBUG("Engine", "Simulation is paused, resuming instead of restarting");
            locker.unlock(); // Release mutex before calling resumeSimulation
            resumeSimulation();
            return Vizion3D::Utils::Result<void>::success();
        }

        // If simulation is in stepping mode, transition to automatic mode from current position
        if (m_simulationRunning && m_steppingMode) {
            VLOG_DEBUG("Engine", "Transitioning from stepping mode to automatic simulation");
            // Continue from current position - don't reset anything
            // Just clear stepping mode and set up timer
            m_steppingMode = false;  // Clear stepping mode for automatic simulation

            // Continue with timer setup below...
        } else if (m_simulationRunning) {
            VLOG_WARNING("Engine", "Simulation already running");
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::SimulationAlreadyRunning,
                "Simulation already running"
            );
        }

        if (m_gCodeLines.isEmpty()) {
            VLOG_WARNING("Engine", "No G-code to simulate");
            auto error = Vizion3D::Utils::Error(
                Vizion3D::Utils::ErrorCode::InvalidState,
                "No G-code to simulate",
                "SimulationEngine"
            );
            emit q->simulationError(error);
            return Vizion3D::Utils::Result<void>::error(error);
        }

        if (timeStep <= 0) {
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::InvalidArgument,
                "Time step must be positive"
            );
        }

        VLOG_DEBUG("Engine", QString("Starting simulation with %1 lines of G-code").arg(m_gCodeLines.size()));

        try {
            // Only reset simulation state if starting fresh (not resuming and not transitioning from stepping)
            if (m_currentLineIndex == 0 && !transitioningFromStepping) {
                VLOG_DEBUG("Engine", "Starting fresh simulation from beginning");

                // Reset toolpath to just the initial point
                m_toolpath.clear();
                m_toolpath.append(Interfaces::ToolpathPoint(
                    QVector3D(0, 0, 0),
                    Interfaces::MoveType::Rapid,
                    0.0,
                    0
                ));
                m_currentPosition = m_toolpath.first();
            } else {
                VLOG_DEBUG("Engine", QString("Continuing simulation from line %1").arg(m_currentLineIndex));
            }

            m_simulationRunning = true;
            m_simulationPaused = false;
            m_steppingMode = false;  // Clear stepping mode for automatic simulation

            // Don't process the first line immediately - let the timer handle it
            VLOG_DEBUG("Engine", "Initial position set, waiting for timer to process first line");

            // Store the current timer if it exists
            if (m_simulationTimer) {
                VLOG_DEBUG("Engine", "Stopping existing simulation timer");
                m_simulationTimer->stop();
                disconnect(m_simulationTimer, &QTimer::timeout, this, &Private::onTimerTimeout);
                m_simulationTimer->deleteLater();
                m_simulationTimer = nullptr;
            }

            // Create a new timer to process lines at the specified rate
            // NOLINTNEXTLINE(cppcoreguidelines-owning-memory) - Qt parent-child ownership
            m_simulationTimer = new QTimer(q);
            m_simulationTimer->setObjectName("SimulationTimer");

            // Calculate the base time step in milliseconds
            int baseTimeStepMs = static_cast<int>(timeStep * 1000);
            VLOG_DEBUG("Engine", QString("Creating simulation timer with base time step: %1 ms").arg(baseTimeStepMs));

            // Store the base time step for use in the timer callback
            m_baseTimeStepMs = baseTimeStepMs;

            // Connect the timer to a named slot instead of a lambda
            // Use Qt::QueuedConnection to ensure the slot is called safely across threads
            bool connected = connect(m_simulationTimer, &QTimer::timeout, this, &Private::onTimerTimeout, Qt::QueuedConnection);
            VLOG_DEBUG("Engine", QString("Timer connection established: %1").arg(connected ? "success" : "FAILED"));

            VLOG_DEBUG("Engine", QString("Timer connected to onTimerTimeout slot with base time step: %1 ms").arg(m_baseTimeStepMs));

            // Start the timer with the initial interval
            int initialInterval = static_cast<int>(baseTimeStepMs / m_simulationSpeed);
            VLOG_DEBUG("Engine", QString("Starting timer with initial interval: %1 ms").arg(initialInterval));

            // Release the mutex before starting the timer to avoid potential deadlock
            locker.unlock();

            m_simulationTimer->start(initialInterval);

            // Only process the first line immediately if we're starting fresh, not transitioning from stepping
            if (!transitioningFromStepping) {
                // Process the first line immediately to kick-start the simulation
                QMetaObject::invokeMethod(this, "onTimerTimeout", Qt::QueuedConnection);
            }

            // Emit appropriate signal based on whether we're transitioning from stepping mode
            if (transitioningFromStepping) {
                VLOG_DEBUG("Engine", "Emitting simulationResumed signal for stepping mode transition");
                emit q->simulationResumed();
            } else {
                emit q->simulationStarted();
            }
            return Vizion3D::Utils::Result<void>::success();
        } catch (const std::exception& e) {
            VLOG_ERROR("Engine", QString("Error starting simulation: %1").arg(e.what()));

            // Clean up resources on error
            if (m_simulationTimer) {
                m_simulationTimer->stop();
                disconnect(m_simulationTimer, &QTimer::timeout, this, &Private::onTimerTimeout);
                m_simulationTimer->deleteLater();
                m_simulationTimer = nullptr;
            }

            m_simulationRunning = false;
            m_simulationPaused = false;

            auto error = Vizion3D::Utils::Error(
                Vizion3D::Utils::ErrorCode::SimulationStartFailed,
                QString("Error starting simulation: %1").arg(e.what()),
                "SimulationEngine"
            );
            emit q->simulationError(error);
            return Vizion3D::Utils::Result<void>::error(error);
        } catch (...) {
            VLOG_ERROR("Engine", "Unknown error starting simulation");

            // Clean up resources on error
            if (m_simulationTimer) {
                m_simulationTimer->stop();
                disconnect(m_simulationTimer, &QTimer::timeout, this, &Private::onTimerTimeout);
                m_simulationTimer->deleteLater();
                m_simulationTimer = nullptr;
            }

            m_simulationRunning = false;
            m_simulationPaused = false;

            auto error = Vizion3D::Utils::Error(
                Vizion3D::Utils::ErrorCode::InternalError,
                "Unknown error starting simulation",
                "SimulationEngine"
            );
            emit q->simulationError(error);
            return Vizion3D::Utils::Result<void>::error(error);
        }
    }

    void pauseSimulation()
    {
        VLOG_DEBUG("Engine", "pauseSimulation() called");

        // Use a separate flag to signal pause request without holding the main mutex
        bool shouldEmitSignal = false;
        QTimer* timerToStop = nullptr;

        {
            QMutexLocker locker(&m_mutex);

            if (!m_simulationRunning || m_simulationPaused) {
                VLOG_DEBUG("Engine", "Simulation not running or already paused, ignoring pause request");
                return;
            }

            m_simulationPaused = true;
            shouldEmitSignal = true;

            // Get reference to timer for stopping outside the mutex
            timerToStop = m_simulationTimer;
        }

        // Stop the simulation timer outside the mutex to avoid deadlock
        if (timerToStop) {
            VLOG_DEBUG("Engine", "Stopping simulation timer for pause");
            timerToStop->stop();
        }

        // If using worker thread, pause that too
        if (m_worker) {
            m_worker->pause();
        }

        // Emit signal outside mutex to prevent deadlock
        if (shouldEmitSignal) {
            emit q->simulationPaused();
        }
    }

    void resumeSimulation()
    {
        VLOG_DEBUG("Engine", "resumeSimulation() called");

        bool shouldEmitSignal = false;
        int actualTimeStepMs = 0;
        QTimer* timerToStart = nullptr;

        {
            QMutexLocker locker(&m_mutex);

            if (!m_simulationRunning || !m_simulationPaused) {
                VLOG_DEBUG("Engine", "Simulation not running or not paused, ignoring resume request");
                return;
            }

            m_simulationPaused = false;
            shouldEmitSignal = true;

            // Calculate timer interval
            actualTimeStepMs = static_cast<int>(m_baseTimeStepMs / m_simulationSpeed);
            timerToStart = m_simulationTimer;
        }

        // Resume the simulation timer outside the mutex
        if (timerToStart) {
            VLOG_DEBUG("Engine", QString("Resuming simulation timer with interval: %1ms").arg(actualTimeStepMs));
            timerToStart->start(actualTimeStepMs);
        }

        // If using worker thread, resume that too
        if (m_worker) {
            m_worker->resume();
        }

        // Emit signal outside mutex to prevent deadlock
        if (shouldEmitSignal) {
            emit q->simulationResumed();
        }
    }

    void stopSimulation()
    {
        VLOG_DEBUG("Engine", "stopSimulation() called");

        bool shouldEmitSignal = false;
        QTimer* timerToCleanup = nullptr;
        SimulationWorker* workerToStop = nullptr;
        QThread* threadToStop = nullptr;

        {
            QMutexLocker locker(&m_mutex);

            if (!m_simulationRunning) {
                VLOG_DEBUG("Engine", "Simulation not running, ignoring stop request");
                return;
            }

            // Update state first
            m_simulationRunning = false;
            m_simulationPaused = false;
            m_steppingMode = false;  // Clear stepping mode
            shouldEmitSignal = true;

            // Get references for cleanup outside the mutex
            timerToCleanup = m_simulationTimer;
            workerToStop = m_worker;
            threadToStop = m_workerThread;

            // Clear the pointers while still holding the mutex
            m_simulationTimer = nullptr;
            m_worker = nullptr;
            m_workerThread = nullptr;

            // Reset current position
            if (!m_toolpath.isEmpty()) {
                m_currentPosition = m_toolpath.first();
            }
        }

        // Clean up timer outside the mutex to avoid deadlock
        if (timerToCleanup) {
            VLOG_DEBUG("Engine", "Stopping and cleaning up simulation timer");
            timerToCleanup->stop();

            // Disconnect all signals to prevent any further callbacks
            disconnect(timerToCleanup, nullptr, nullptr, nullptr);

            // Schedule for deletion
            timerToCleanup->deleteLater();
        }

        // Clean up worker thread outside the mutex
        if (workerToStop && threadToStop) {
            VLOG_DEBUG("Engine", "Stopping worker thread");
            workerToStop->stop();

            // Wait for the worker to finish
            threadToStop->quit();
            threadToStop->wait(5000); // 5 second timeout

            // Clean up using Qt's object management
            workerToStop->deleteLater();
            threadToStop->deleteLater();
        }

        // Emit signal outside mutex to prevent deadlock
        if (shouldEmitSignal) {
            emit q->simulationStopped();
        }
    }

    void stepSimulation()
    {
        VLOG_DEBUG("Engine", "stepSimulation() called");

        // Use local variables to store state outside the mutex lock
        bool canStep = false;
        bool shouldEmitError = false;
        bool shouldEmitCompleted = false;
        bool wasRunning = false;
        bool needsInitialization = false;

        {
            QMutexLocker locker(&m_mutex);

            if (m_simulationRunning && !m_simulationPaused && !m_steppingMode) {
                VLOG_WARNING("Engine", "Cannot step while automatic simulation is running");
                return;
            }

            if (m_gCodeLines.isEmpty()) {
                VLOG_WARNING("Engine", "No G-code to simulate");
                shouldEmitError = true;
                // Don't return here, emit signal outside mutex
            } else if (m_currentLineIndex < m_gCodeLines.size()) {
                // We can step - prepare state
                canStep = true;
                wasRunning = m_simulationRunning;

                // Check if we need to initialize simulation (stepping from beginning)
                if (!m_simulationRunning && m_currentLineIndex == 0) {
                    VLOG_DEBUG("Engine", "Stepping from beginning - initializing stepping mode");
                    needsInitialization = true;

                    // Initialize toolpath with origin point (same as startSimulation)
                    m_toolpath.clear();
                    m_toolpath.append(Interfaces::ToolpathPoint(
                        QVector3D(0, 0, 0),
                        Interfaces::MoveType::Rapid,
                        0.0,
                        0
                    ));
                    m_currentPosition = m_toolpath.first();

                    // Set simulation state for stepping mode
                    m_simulationRunning = true;
                    m_simulationPaused = false;
                    m_steppingMode = true;  // Enable stepping mode
                } else {
                    // Continue in stepping mode or set stepping mode if not already set
                    if (!m_steppingMode) {
                        VLOG_DEBUG("Engine", "Switching to stepping mode");
                        m_steppingMode = true;
                    }

                    // Ensure simulation is in the right state for stepping
                    if (!m_simulationRunning) {
                        m_simulationRunning = true;
                    }
                    m_simulationPaused = false;
                }
            } else {
                // We've reached the end
                shouldEmitCompleted = true;
            }
        }

        // Handle error case outside mutex
        if (shouldEmitError) {
            auto errorToEmit = Vizion3D::Utils::Error(
                Vizion3D::Utils::ErrorCode::InvalidState,
                "No G-code to simulate",
                "SimulationEngine"
            );
            emit q->simulationError(errorToEmit);
            return;
        }

        // Handle completion case outside mutex
        if (shouldEmitCompleted) {
            emit q->simulationCompleted();
            return;
        }

        // Emit simulationStarted signal if this is the first step (initialization)
        if (needsInitialization) {
            VLOG_DEBUG("Engine", "Emitting simulationStarted signal for step initialization");
            emit q->simulationStarted();
        }

        // Process the step outside mutex
        if (canStep) {
            VLOG_DEBUG("Engine", QString("Stepping simulation at line %1").arg(m_currentLineIndex));

            // Process the next line outside the mutex lock
            auto result = processNextLine();
            if (!result.isSuccess()) {
                VLOG_DEBUG("Engine", QString("Step simulation failed: %1").arg(result.error().message));
            }

            // In stepping mode, we don't restore the previous state
            // Keep m_simulationRunning = true and m_steppingMode = true for future steps
        }
    }

    Vizion3D::Utils::Result<void> loadMachine(const QString& filePath)
    {
        QMutexLocker locker(&m_mutex);

        if (!m_initialized) {
            VLOG_WARNING("Engine", "SimulationEngine not initialized");
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::NotInitialized,
                "SimulationEngine not initialized"
            );
        }

        if (filePath.isEmpty()) {
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::InvalidArgument,
                "File path is empty"
            );
        }

        // TODO: Implement machine loading

        return Vizion3D::Utils::Result<void>::success();
    }

    Vizion3D::Utils::Result<void> loadModel(const QString& filePath, const QString& name)
    {
        QMutexLocker locker(&m_mutex);

        if (!m_initialized) {
            VLOG_WARNING("Engine", "SimulationEngine not initialized");
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::NotInitialized,
                "SimulationEngine not initialized"
            );
        }

        if (filePath.isEmpty()) {
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::InvalidArgument,
                "File path is empty"
            );
        }

        if (name.isEmpty()) {
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::InvalidArgument,
                "Model name is empty"
            );
        }

        // TODO: Implement model loading

        return Vizion3D::Utils::Result<void>::success();
    }

    Vizion3D::Utils::Result<void> exportWorkpiece(const QString& filePath, const QString& fileFormat)
    {
        QMutexLocker locker(&m_mutex);

        if (!m_initialized) {
            VLOG_WARNING("Engine", "SimulationEngine not initialized");
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::NotInitialized,
                "SimulationEngine not initialized"
            );
        }

        if (filePath.isEmpty()) {
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::InvalidArgument,
                "File path is empty"
            );
        }

        if (fileFormat.isEmpty()) {
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::InvalidArgument,
                "File format is empty"
            );
        }

        // TODO: Implement workpiece export

        return Vizion3D::Utils::Result<void>::success();
    }

    bool isSimulationRunning() const
    {
        QMutexLocker locker(&m_mutex);
        return m_simulationRunning;
    }

    bool isSimulationPaused() const
    {
        QMutexLocker locker(&m_mutex);
        return m_simulationPaused;
    }

    bool isSteppingMode() const
    {
        QMutexLocker locker(&m_mutex);
        return m_steppingMode;
    }

    double getWorkpieceVolume() const
    {
        QMutexLocker locker(&m_mutex);

        if (!m_initialized) {
            VLOG_WARNING("Engine", "SimulationEngine not initialized");
            return 0.0;
        }

        // TODO: Implement workpiece volume calculation

        return 0.0;
    }

    QStringList checkCollisions()
    {
        QMutexLocker locker(&m_mutex);

        if (!m_initialized) {
            VLOG_WARNING("Engine", "SimulationEngine not initialized");
            return QStringList() << "SimulationEngine not initialized";
        }

        // TODO: Implement collision detection

        return {};
    }

    Vizion3D::Utils::Result<void> simulateMaterialRemoval(const QString& gcode, double timeStep)
    {
        QMutexLocker locker(&m_mutex);

        if (!m_initialized) {
            VLOG_WARNING("Engine", "SimulationEngine not initialized");
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::NotInitialized,
                "SimulationEngine not initialized"
            );
        }

        if (gcode.isEmpty()) {
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::InvalidArgument,
                "G-code string is empty"
            );
        }

        if (timeStep <= 0) {
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::InvalidArgument,
                "Time step must be positive"
            );
        }

        // TODO: Implement material removal simulation

        return Vizion3D::Utils::Result<void>::success();
    }

    void setSimulationSpeed(double speed)
    {
        QMutexLocker locker(&m_mutex);
        m_simulationSpeed = qBound(0.1, speed, 10.0);
    }

    double getSimulationSpeed() const
    {
        QMutexLocker locker(&m_mutex);
        return m_simulationSpeed;
    }

    Interfaces::ToolpathPoint getCurrentPosition() const
    {
        QMutexLocker locker(&m_mutex);
        return m_currentPosition;
    }

    Vizion3D::Utils::Result<void> processNextLine()
    {
        // Use a local variable to store the current line and index to minimize the time the mutex is locked
        QString currentLine;
        int currentIndex = 0;
        bool isEmpty = false;
        bool isEndOfProgram = false;

        try {
            // Acquire the mutex only for reading the current state
            {
                QMutexLocker locker(&m_mutex);

                VLOG_DEBUG("Engine", QString("processNextLine called at %1, current index: %2 total lines: %3")
                           .arg(QTime::currentTime().toString("hh:mm:ss.zzz"))
                           .arg(m_currentLineIndex)
                           .arg(m_gCodeLines.size()));

                if (m_gCodeLines.isEmpty()) {
                    VLOG_WARNING("Engine", "G-code lines list is empty!");
                    isEmpty = true;
                } else if (m_currentLineIndex >= m_gCodeLines.size()) {
                    VLOG_DEBUG("Engine", QString("No more lines to process (index %1 >= size %2)").arg(m_currentLineIndex).arg(m_gCodeLines.size()));
                    isEndOfProgram = true;
                } else {
                    // Get the current line and index
                    currentLine = m_gCodeLines[m_currentLineIndex];
                    currentIndex = m_currentLineIndex;
                }
            }

            // Early return if there's no G-code or we've reached the end
            if (isEmpty || isEndOfProgram) {
                return Vizion3D::Utils::Result<void>::error(
                    Vizion3D::Utils::ErrorCode::EndOfProgram,
                    "No more lines to process"
                );
            }

            VLOG_DEBUG("Engine", QString("Processing line %1: %2").arg(currentIndex).arg(currentLine));

            // Skip empty lines and comments
            if (currentLine.trimmed().isEmpty() || currentLine.trimmed().startsWith(';') || currentLine.trimmed().startsWith('(')) {
                VLOG_DEBUG("Engine", "Skipping comment or empty line");

                // Update the current index and check if we've reached the end
                bool reachedEnd = false;
                double progress = 0.0;
                {
                    QMutexLocker locker(&m_mutex);
                    m_currentLineIndex++;

                    // Calculate progress
                    progress = static_cast<double>(m_currentLineIndex) / static_cast<double>(m_gCodeLines.size());

                    // Check if we've reached the end
                    if (m_currentLineIndex >= m_gCodeLines.size()) {
                        VLOG_DEBUG("Engine", "Reached end of G-code program after skipping comment/empty line");
                        reachedEnd = true;
                    }
                }

                // Emit progress signal outside mutex
                emit q->simulationProgress(progress);

                if (reachedEnd) {
                    // Don't emit simulationCompleted here, let onTimerTimeout handle it
                    return Vizion3D::Utils::Result<void>::error(
                        Vizion3D::Utils::ErrorCode::EndOfProgram,
                        "Reached end of G-code program"
                    );
                }

                return Vizion3D::Utils::Result<void>::success();
            }

            VLOG_DEBUG("Engine", "About to call GCodeInterpreter::processLine");

            // Verify that the G-code interpreter is valid
            if (!m_gCodeInterpreter) {
                VLOG_ERROR("Engine", "G-code interpreter is null!");
                auto error = Vizion3D::Utils::Error(
                    Vizion3D::Utils::ErrorCode::InternalError,
                    "Internal error: G-code interpreter is not available",
                    "SimulationEngine"
                );
                // Use queued invocation to emit error signal safely
                QMetaObject::invokeMethod(q, "simulationError", Qt::QueuedConnection,
                                          Q_ARG(Vizion3D::Utils::Error, error));
                return Vizion3D::Utils::Result<void>::error(error);
            }

            // Add a timeout mechanism to prevent infinite waiting
            QElapsedTimer timer;
            timer.start();

            try {
                VLOG_DEBUG("Engine", QString("About to call m_gCodeInterpreter->processLine with line: %1").arg(currentLine));

                // Process the line with the G-code interpreter - this is done outside the mutex lock
                auto pointResult = m_gCodeInterpreter->processLine(currentLine);

                VLOG_DEBUG("Engine", QString("GCodeInterpreter::processLine took %1ms and returned: %2").arg(timer.elapsed()).arg((pointResult.isSuccess() ? "valid point" : "error")));

                // Variables to store data for signal emission outside mutex
                Interfaces::ToolpathPoint interfacePointToEmit;
                bool shouldEmitStep = false;

                // If a new point was generated, add it to the toolpath
                if (pointResult.isSuccess()) {
                    auto newPoint = pointResult.value();
                    VLOG_DEBUG("Engine", QString("New point generated at position: (%1, %2, %3)").arg(newPoint.position().x()).arg(newPoint.position().y()).arg(newPoint.position().z()));

                    // Convert to interface point
                    Interfaces::MoveType moveType = Interfaces::MoveType::Unknown;

                    // Convert move type
                    switch (newPoint.moveType()) {
                        case MoveType::Rapid:
                            moveType = Interfaces::MoveType::Rapid;
                            break;
                        case MoveType::Linear:
                            moveType = Interfaces::MoveType::Linear;
                            break;
                        case MoveType::ArcCW:
                            moveType = Interfaces::MoveType::ArcCW;
                            break;
                        case MoveType::ArcCCW:
                            moveType = Interfaces::MoveType::ArcCCW;
                            break;
                        case MoveType::Dwell:
                            moveType = Interfaces::MoveType::Dwell;
                            break;
                        default:
                            moveType = Interfaces::MoveType::Unknown;
                            break;
                    }

                    // Create interface point
                    interfacePointToEmit = Interfaces::ToolpathPoint(
                        newPoint.position(),
                        moveType,
                        newPoint.feedRate(),
                        currentIndex  // Use current line index as the line number
                    );

                    // Update the toolpath and current position with the mutex locked
                    {
                        QMutexLocker locker(&m_mutex);

                        // Add to toolpath
                        m_toolpath.append(interfacePointToEmit);

                        // Update current position
                        m_currentPosition = interfacePointToEmit;

                        shouldEmitStep = true;
                    }

                    // Log the new position
                    VLOG_DEBUG("Engine", QString("New position: (%1, %2, %3)").arg(interfacePointToEmit.position().x()).arg(interfacePointToEmit.position().y()).arg(interfacePointToEmit.position().z()));
                } else {
                    VLOG_DEBUG("Engine", QString("No new point generated for line: %1").arg(currentLine));
                }

                // Emit step signal outside mutex if needed
                if (shouldEmitStep) {
                    emit q->simulationStep(interfacePointToEmit);
                }

            } catch (const std::exception& e) {
                VLOG_ERROR("Engine", QString("Exception in processNextLine: %1").arg(e.what()));
                auto error = Vizion3D::Utils::Error(
                    Vizion3D::Utils::ErrorCode::GCodeProcessingError,
                    QString("Error processing line %1: %2").arg(currentIndex).arg(e.what()),
                    "SimulationEngine",
                    QString("Line %1: %2").arg(currentIndex).arg(currentLine)
                );
                // Use queued invocation to emit error signal safely
                QMetaObject::invokeMethod(q, "simulationError", Qt::QueuedConnection,
                                          Q_ARG(Vizion3D::Utils::Error, error));

                // Continue to the next line despite the error
                VLOG_DEBUG("Engine", "Continuing to next line despite error");
            } catch (...) {
                VLOG_ERROR("Engine", "Unknown exception in processNextLine");
                auto error = Vizion3D::Utils::Error(
                    Vizion3D::Utils::ErrorCode::InternalError,
                    QString("Unknown error processing line %1").arg(currentIndex),
                    "SimulationEngine",
                    QString("Line %1: %2").arg(currentIndex).arg(currentLine)
                );
                // Use queued invocation to emit error signal safely
                QMetaObject::invokeMethod(q, "simulationError", Qt::QueuedConnection,
                                          Q_ARG(Vizion3D::Utils::Error, error));

                // Continue to the next line despite the error
                VLOG_DEBUG("Engine", "Continuing to next line despite error");
            }

            // Update the current index and check if we've reached the end
            bool reachedEnd = false;
            double progress = 0.0;

            {
                QMutexLocker locker(&m_mutex);

                // Move to next line
                m_currentLineIndex++;

                // Calculate progress
                progress = static_cast<double>(m_currentLineIndex) / static_cast<double>(m_gCodeLines.size());

                // Check if we've reached the end
                if (m_currentLineIndex >= m_gCodeLines.size()) {
                    VLOG_DEBUG("Engine", "Reached end of G-code program");
                    reachedEnd = true;
                }
            }

            // Emit progress signal outside the mutex lock
            emit q->simulationProgress(progress);

            if (reachedEnd) {
                // Don't emit simulationCompleted here, let onTimerTimeout handle it
                return Vizion3D::Utils::Result<void>::error(
                    Vizion3D::Utils::ErrorCode::EndOfProgram,
                    "Reached end of G-code program"
                );
            }

            return Vizion3D::Utils::Result<void>::success();
        } catch (const std::exception& e) {
            VLOG_ERROR("Engine", QString("Critical error - Exception in processNextLine: %1").arg(e.what()));
            auto error = Vizion3D::Utils::Error(
                Vizion3D::Utils::ErrorCode::InternalError,
                QString("Critical error: %1").arg(e.what()),
                "SimulationEngine"
            );
            // Use queued invocation to emit error signal safely
            QMetaObject::invokeMethod(q, "simulationError", Qt::QueuedConnection,
                                      Q_ARG(Vizion3D::Utils::Error, error));
            return Vizion3D::Utils::Result<void>::error(error);
        } catch (...) {
            VLOG_ERROR("Engine", "Critical error - Unknown exception in processNextLine");
            auto error = Vizion3D::Utils::Error(
                Vizion3D::Utils::ErrorCode::InternalError,
                "Critical error: Unknown exception",
                "SimulationEngine"
            );
            // Use queued invocation to emit error signal safely
            QMetaObject::invokeMethod(q, "simulationError", Qt::QueuedConnection,
                                      Q_ARG(Vizion3D::Utils::Error, error));
            return Vizion3D::Utils::Result<void>::error(error);
        }
    }

    void resetSimulation()
    {
        VLOG_DEBUG("Engine", "resetSimulation() called");

        bool shouldEmitSignal = false;
        QTimer* timerToCleanup = nullptr;

        {
            QMutexLocker locker(&m_mutex);

            // Stop any running simulation
            if (m_simulationRunning) {
                m_simulationRunning = false;
                m_simulationPaused = false;
                m_steppingMode = false;  // Clear stepping mode
                shouldEmitSignal = true;

                // Get timer reference for cleanup
                timerToCleanup = m_simulationTimer;
                m_simulationTimer = nullptr;
            }

            // Reset simulation state to beginning
            m_currentLineIndex = 0;

            // Reset toolpath to just the initial point
            if (!m_toolpath.isEmpty()) {
                // Keep the first point (origin) and clear the rest
                auto initialPoint = m_toolpath.first();
                m_toolpath.clear();
                m_toolpath.append(initialPoint);
                m_currentPosition = initialPoint;
            }
        }

        // Clean up timer outside the mutex to avoid deadlock
        if (timerToCleanup) {
            VLOG_DEBUG("Engine", "Stopping and cleaning up simulation timer for reset");
            timerToCleanup->stop();

            // Disconnect all signals to prevent any further callbacks
            disconnect(timerToCleanup, nullptr, nullptr, nullptr);

            // Schedule for deletion
            timerToCleanup->deleteLater();
        }

        // Emit signal outside mutex to prevent deadlock
        if (shouldEmitSignal) {
            emit q->simulationStopped();
        }
    }

private:
    SimulationEngine* q;

    bool m_initialized{false};
    bool m_simulationRunning{false};
    bool m_simulationPaused{false};
    bool m_steppingMode{false};  // New flag to track stepping mode vs automatic simulation
    double m_simulationSpeed{1.0};

    QVector<Interfaces::ToolpathPoint> m_toolpath;
    Interfaces::ToolpathPoint m_currentPosition;

    // For line-by-line processing
    QStringList m_gCodeLines;
    int m_currentLineIndex{0};
    QTimer *m_simulationTimer{nullptr};
    int m_baseTimeStepMs{100};

    QThread *m_workerThread{nullptr};
    SimulationWorker *m_worker{nullptr};
    GCodeInterpreter* m_gCodeInterpreter;

    mutable QMutex m_mutex;
};

// Implementation of SimulationEngine::Private::onTimerTimeout
void SimulationEngine::Private::onTimerTimeout()
{
    VLOG_DEBUG("Engine", QString("Timer timeout triggered at %1").arg(QTime::currentTime().toString("hh:mm:ss.zzz")));

    try {
        // Check if timer is valid before proceeding
        if (!m_simulationTimer) {
            VLOG_WARNING("Engine", "Timer timeout called but timer is null!");
            return;
        }

        // Use local variables to store state outside the mutex lock
        bool shouldStop = false;
        bool shouldContinue = false;
        int actualTimeStepMs = 0;

        // Use a smaller scope for the mutex lock to reduce lock contention
        {
            QMutexLocker locker(&m_mutex);

            if (!m_simulationRunning || m_simulationPaused) {
                VLOG_DEBUG("Engine", "Simulation not running or paused, skipping timer callback");
                return;
            }

            // Calculate actual time step based on simulation speed
            actualTimeStepMs = static_cast<int>(m_baseTimeStepMs / m_simulationSpeed);
            shouldContinue = true;
        }

        // Only proceed if simulation should continue
        if (!shouldContinue) {
            return;
        }

        VLOG_DEBUG("Engine", QString("Actual time step: %1ms").arg(actualTimeStepMs));

        // Process the next line outside the mutex lock
        VLOG_DEBUG("Engine", QString("About to process line at %1").arg(QTime::currentTime().toString("hh:mm:ss.zzz")));

        auto result = processNextLine();

        VLOG_DEBUG("Engine", QString("processNextLine result: %1").arg((result.isSuccess() ? "success" : "error")));

        // Check if we should stop the simulation
        shouldStop = !result.isSuccess();

        // Handle the result
        if (shouldStop) {
            // If no more lines, stop the timer and complete simulation
            VLOG_DEBUG("Engine", "No more lines to process, stopping simulation");

            // Use a separate method to handle completion to avoid deadlock
            QMetaObject::invokeMethod(this, "handleSimulationCompletion", Qt::QueuedConnection);
        } else {
            // Continue simulation - adjust timer interval if needed
            bool timerValid = false;
            {
                QMutexLocker locker(&m_mutex);
                timerValid = (m_simulationTimer != nullptr && m_simulationRunning && !m_simulationPaused);
            }

            if (timerValid && m_simulationTimer) {
                // Adjust timer interval based on current simulation speed
                m_simulationTimer->setInterval(actualTimeStepMs);

                // Verify the timer is still active
                if (!m_simulationTimer->isActive()) {
                    VLOG_WARNING("Engine", "Timer is not active! Restarting timer.");
                    m_simulationTimer->start(actualTimeStepMs);
                }
            }
        }

        VLOG_DEBUG("Engine", QString("onTimerTimeout completed at %1").arg(QTime::currentTime().toString("hh:mm:ss.zzz")));
    } catch (const std::exception& e) {
        VLOG_ERROR("Engine", QString("Critical error - Exception in onTimerTimeout: %1").arg(e.what()));

        // Handle error in a separate queued method to avoid deadlock
        QMetaObject::invokeMethod(this, "handleSimulationError", Qt::QueuedConnection,
                                  Q_ARG(QString, QString("Critical error: %1").arg(e.what())));
    } catch (...) {
        VLOG_ERROR("Engine", "Critical error - Unknown exception in onTimerTimeout");

        // Handle error in a separate queued method to avoid deadlock
        QMetaObject::invokeMethod(this, "handleSimulationError", Qt::QueuedConnection,
                                  Q_ARG(QString, "Critical error: Unknown exception"));
    }
}

// Helper method to handle simulation completion safely
void SimulationEngine::Private::handleSimulationCompletion()
{
    VLOG_DEBUG("Engine", "handleSimulationCompletion() called");

    QTimer* timerToCleanup = nullptr;
    bool shouldEmitSignal = false;

    {
        QMutexLocker locker(&m_mutex);

        if (m_simulationRunning) {
            m_simulationRunning = false;
            m_simulationPaused = false;
            shouldEmitSignal = true;

            // Get timer reference for cleanup
            timerToCleanup = m_simulationTimer;
            m_simulationTimer = nullptr;
        }
    }

    // Clean up timer outside mutex
    if (timerToCleanup) {
        VLOG_DEBUG("Engine", "Cleaning up timer for simulation completion");
        timerToCleanup->stop();
        disconnect(timerToCleanup, nullptr, nullptr, nullptr);
        timerToCleanup->deleteLater();
    }

    // Emit completion signal outside mutex
    if (shouldEmitSignal) {
        VLOG_DEBUG("Engine", "Emitting simulationCompleted signal");
        emit q->simulationCompleted();
    }
}

// Helper method to handle simulation errors safely
void SimulationEngine::Private::handleSimulationError(const QString& errorMessage)
{
    VLOG_DEBUG("Engine", QString("handleSimulationError() called with: %1").arg(errorMessage));

    QTimer* timerToCleanup = nullptr;
    bool shouldEmitSignal = false;

    {
        QMutexLocker locker(&m_mutex);

        if (m_simulationRunning) {
            m_simulationRunning = false;
            m_simulationPaused = false;
            shouldEmitSignal = true;

            // Get timer reference for cleanup
            timerToCleanup = m_simulationTimer;
            m_simulationTimer = nullptr;
        }
    }

    // Clean up timer outside mutex
    if (timerToCleanup) {
        VLOG_DEBUG("Engine", "Cleaning up timer for simulation error");
        timerToCleanup->stop();
        disconnect(timerToCleanup, nullptr, nullptr, nullptr);
        timerToCleanup->deleteLater();
    }

    // Emit error signal outside mutex
    if (shouldEmitSignal) {
        auto error = Vizion3D::Utils::Error(
            Vizion3D::Utils::ErrorCode::InternalError,
            errorMessage,
            "SimulationEngine"
        );
        VLOG_DEBUG("Engine", "Emitting simulationError signal");
        emit q->simulationError(error);
    }
}

// Implementation of SimulationWorker class
SimulationWorker::SimulationWorker(const QVector<Interfaces::ToolpathPoint> &toolpath, double timeStep, QObject *parent)
    : QObject(parent)
    , m_toolpath(toolpath)
    , m_timeStep(timeStep)
    , m_shouldStop(false)
    , m_isPaused(false)
{
}

void SimulationWorker::process()
{
    if (m_toolpath.isEmpty()) {
        auto errorObj = Vizion3D::Utils::Error(
            Vizion3D::Utils::ErrorCode::InvalidState,
            "Toolpath is empty",
            "SimulationWorker"
        );
        emit error(errorObj);
        emit finished();
        return;
    }

    int totalPoints = static_cast<int>(m_toolpath.size());
    QElapsedTimer timer;

    VLOG_DEBUG("Engine", QString("Starting simulation with %1 points and time step %2 seconds").arg(totalPoints).arg(m_timeStep));

    // First point is always emitted immediately
    emit step(m_toolpath[0]);

    // Wait a moment to show the initial position
    QThread::msleep(500);

    for (int i = 1; i < totalPoints; ++i) {
        // Check if we should stop
        {
            QMutexLocker locker(&m_mutex);
            if (m_shouldStop) {
                break;
            }

            // Check if we should pause
            while (m_isPaused) {
                m_pauseCondition.wait(&m_mutex);
                if (m_shouldStop) {
                    break;
                }
            }

            if (m_shouldStop) {
                break;
            }
        }

        // Get current and previous points
        const auto& currentPoint = m_toolpath[i];
        const auto& prevPoint = m_toolpath[i-1];

        // Calculate actual time to wait based on move type and distance
        double waitTime = m_timeStep;

        // For rapid moves, use a shorter time
        if (currentPoint.moveType() == Vizion3D::Engine::Interfaces::MoveType::Rapid) {
            waitTime = m_timeStep * 0.5;
        }

        // For very short moves, use a minimum time
        QVector3D distance = currentPoint.position() - prevPoint.position();
        if (distance.length() < 0.1) {
            waitTime = std::min(waitTime, 0.05);
        }

        // Emit the current position
        emit step(currentPoint);

        // Calculate progress
        double progressValue = static_cast<double>(i) / static_cast<double>(totalPoints - 1);
        emit progress(progressValue);

        VLOG_DEBUG("Engine", QString("Simulation step %1 of %2 position: %3 %4 %5 line: %6 progress: %7").arg(i).arg(totalPoints).arg(currentPoint.position().x()).arg(currentPoint.position().y()).arg(currentPoint.position().z()).arg(currentPoint.lineNumber()).arg(progressValue));

        // Wait for the time step
        timer.start();
        int waitTimeMs = static_cast<int>(waitTime * 1000);
        while (timer.elapsed() < waitTimeMs) {
            QThread::msleep(10);

            // Check if we should stop or pause
            {
                QMutexLocker locker(&m_mutex);
                if (m_shouldStop) {
                    break;
                }

                // Check if we should pause
                if (m_isPaused) {
                    break;
                }
            }
        }
    }

    VLOG_DEBUG("Engine", "Simulation completed");
    emit finished();
}

void SimulationWorker::pause()
{
    QMutexLocker locker(&m_mutex);
    m_isPaused = true;
}

void SimulationWorker::resume()
{
    QMutexLocker locker(&m_mutex);
    m_isPaused = false;
    m_pauseCondition.wakeAll();
}

void SimulationWorker::stop()
{
    QMutexLocker locker(&m_mutex);
    m_shouldStop = true;
    m_isPaused = false;
    m_pauseCondition.wakeAll();
}

// SimulationEngine implementation
SimulationEngine::SimulationEngine(QObject* parent)
    : Interfaces::ISimulationEngine(parent)
    , d(new Private(this))
{
}

SimulationEngine::~SimulationEngine() = default;

Vizion3D::Utils::Result<void> SimulationEngine::initialize(const QString& configPath)
{
    return d->initialize(configPath);
}

Vizion3D::Utils::Result<void> SimulationEngine::parseGCode(const QString& gcode)
{
    return d->parseGCode(gcode);
}

Vizion3D::Utils::Result<QVector<Interfaces::ToolpathPoint>> SimulationEngine::getToolpath() const
{
    return d->getToolpath();
}

Vizion3D::Utils::Result<void> SimulationEngine::startSimulation(double timeStep)
{
    return d->startSimulation(timeStep);
}

void SimulationEngine::pauseSimulation()
{
    d->pauseSimulation();
}

void SimulationEngine::resumeSimulation()
{
    d->resumeSimulation();
}

void SimulationEngine::stopSimulation()
{
    d->stopSimulation();
}

void SimulationEngine::stepSimulation()
{
    d->stepSimulation();
}

Vizion3D::Utils::Result<void> SimulationEngine::loadMachine(const QString& filePath)
{
    return d->loadMachine(filePath);
}

Vizion3D::Utils::Result<void> SimulationEngine::loadModel(const QString& filePath, const QString& name)
{
    return d->loadModel(filePath, name);
}

Vizion3D::Utils::Result<void> SimulationEngine::exportWorkpiece(const QString& filePath, const QString& fileFormat)
{
    return d->exportWorkpiece(filePath, fileFormat);
}

bool SimulationEngine::isSimulationRunning() const
{
    return d->isSimulationRunning();
}

bool SimulationEngine::isSimulationPaused() const
{
    return d->isSimulationPaused();
}

bool SimulationEngine::isSteppingMode() const
{
    return d->isSteppingMode();
}

double SimulationEngine::getWorkpieceVolume() const
{
    return d->getWorkpieceVolume();
}

QStringList SimulationEngine::checkCollisions()
{
    return d->checkCollisions();
}

Vizion3D::Utils::Result<void> SimulationEngine::simulateMaterialRemoval(const QString& gcode, double timeStep)
{
    return d->simulateMaterialRemoval(gcode, timeStep);
}

void SimulationEngine::setSimulationSpeed(double speed)
{
    d->setSimulationSpeed(speed);
}

double SimulationEngine::getSimulationSpeed() const
{
    return d->getSimulationSpeed();
}

Interfaces::ToolpathPoint SimulationEngine::getCurrentPosition() const
{
    return d->getCurrentPosition();
}

Vizion3D::Utils::Result<void> SimulationEngine::processNextLine()
{
    return d->processNextLine();
}

void SimulationEngine::resetSimulation()
{
    d->resetSimulation();
}

} // namespace Engine
} // namespace Vizion3D

// Include the moc file for the Private class
#include "simulation_engine.moc"
