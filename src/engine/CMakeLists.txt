# Define source files
set(ENGINE_SOURCES
    interfaces/toolpath_point.cpp
    engine_factory.cpp
    simulation_engine.cpp
    gcode_interpreter.cpp
    toolpath_point.cpp
    # Utils sources needed by engine
    ${CMAKE_SOURCE_DIR}/src/utils/result.cpp
    ${CMAKE_SOURCE_DIR}/src/utils/logger.cpp
    ${CMAKE_SOURCE_DIR}/src/utils/log_config.cpp
)

# Define header files
set(ENGINE_HEADERS
    ${CMAKE_SOURCE_DIR}/include/engine/interfaces/simulation_engine_interface.h
    ${CMAKE_SOURCE_DIR}/include/engine/interfaces/toolpath_point.h
    ${CMAKE_SOURCE_DIR}/include/engine/interfaces/engine_factory_interface.h
    ${CMAKE_SOURCE_DIR}/include/engine/engine_factory.h
    ${CMAKE_SOURCE_DIR}/include/engine/simulation_engine.h
    ${CMAKE_SOURCE_DIR}/include/engine/gcode_interpreter.h
    ${CMAKE_SOURCE_DIR}/include/engine/toolpath_point.h
    # Utils headers needed by engine
    ${CMAKE_SOURCE_DIR}/include/utils/result.h
    ${CMAKE_SOURCE_DIR}/include/utils/logger.h
    ${CMAKE_SOURCE_DIR}/include/utils/log_config.h
)

# Create library
add_library(Vizion3DEngine STATIC
    ${ENGINE_SOURCES}
    ${ENGINE_HEADERS}
)

# Set include directories
target_include_directories(Vizion3DEngine PUBLIC
    ${CMAKE_SOURCE_DIR}/include
)

# Link Qt libraries
if(QT_VERSION EQUAL 6)
    target_link_libraries(Vizion3DEngine PUBLIC
        Qt6::Core
        Qt6::Gui
        Qt6::OpenGL
    )
else()
    target_link_libraries(Vizion3DEngine PUBLIC
        Qt5::Core
        Qt5::Gui
        Qt5::OpenGL
    )
endif()

# Link OpenCASCADE 7.8.0 if found
if(OpenCASCADE_FOUND AND OpenCASCADE_VERSION VERSION_EQUAL "7.8.0")
    target_link_libraries(Vizion3DEngine PUBLIC ${OpenCASCADE_LIBRARIES})
    target_compile_definitions(Vizion3DEngine PUBLIC -DUSE_OPENCASCADE -DOPENCASCADE_VERSION=7.8.0)
endif()

# Install library
install(TARGETS Vizion3DEngine
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
)
