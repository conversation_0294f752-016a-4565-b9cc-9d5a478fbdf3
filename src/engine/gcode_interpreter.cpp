#include "engine/gcode_interpreter.h"

#include <cmath>
#include "utils/logger.h"
#include "utils/result.h"

#include <QDebug>
#include <QThread>
#include <QRegularExpression>
#include <QStringList>
#include <QFile>
#include <QTextStream>

namespace Vizion3D {
namespace Engine {

/**
 * @brief Private implementation class for GCodeInterpreter
 */
class GCodeInterpreter::Private : public QObject {
    Q_OBJECT

public:
    Private(GCodeInterpreter *parent);
    ~Private() override;

    // Prevent copying
    Private(const Private&) = delete;
    Private& operator=(const Private&) = delete;

    Vizion3D::Utils::Result<void> parseGCode(const QString &gcode);
    Vizion3D::Utils::Result<void> loadGCode(const QString &filePath);
    void cancelParsing();
    Vizion3D::Utils::Result<QVector<ToolpathPoint>> simulateExecution(double timeStep) const;
    Vizion3D::Utils::Result<ToolpathPoint> processLine(const QString &line, int lineNumber);

    // Accessor methods
    Vizion3D::Utils::Result<QVector<ToolpathPoint>> getToolpath() const {
        QMutexLocker locker(&m_mutex);
        return Vizion3D::Utils::Result<QVector<ToolpathPoint>>::success(m_toolpath);
    }

    Vizion3D::Utils::Result<QMap<int, QString>> getAnnotatedGCode() const {
        QMutexLocker locker(&m_mutex);
        return Vizion3D::Utils::Result<QMap<int, QString>>::success(m_annotatedGCode);
    }

    bool isParsingInProgress() const { return m_parsingInProgress; }

signals:
    void parsingStarted();
    void parsingProgress(int lineNumber, int totalLines);
    void parsingCompleted();
    void parsingError(const Vizion3D::Utils::Error &error);
    void parsingCanceled();

private:
    GCodeInterpreter *q;
    QVector<ToolpathPoint> m_toolpath;
    QMap<int, QString> m_annotatedGCode;
    bool m_parsingInProgress{false};
    bool m_cancelRequested{false};
    mutable QMutex m_mutex;
    QThread m_parsingThread;

    // G-code state
    struct Position {
        double x;

        double y;

        double z;
    };
    Position m_currentPosition;
    double m_currentFeedRate{0};
    bool m_absoluteMode{true};

    // Helper methods for parsing
    void parseGCodeInternal(const QString &gcode);
    bool parseLine(const QString &line, int lineNumber);
    bool parseGCommand(const QString &command, double value, int lineNumber);
    bool parseMCommand(const QString &command, double value, int lineNumber);
    void processMove(const QString &line, MoveType moveType, int lineNumber);
    void processArc(const QString &line, MoveType moveType, int lineNumber);
    double extractCoordinate(const QString &line, char axis);
    double extractFeedRate(const QString &line);
};

// Implementation of Private class
GCodeInterpreter::Private::Private(GCodeInterpreter *parent)
    : QObject(nullptr), q(parent), m_currentPosition{0, 0, 0}

{
    // Connect signals from this private implementation to the public class
    connect(this, &Private::parsingStarted, q, &GCodeInterpreter::parsingStarted);
    connect(this, &Private::parsingProgress, q, &GCodeInterpreter::parsingProgress);
    connect(this, &Private::parsingCompleted, q, &GCodeInterpreter::parsingCompleted);
    connect(this, &Private::parsingError, q, &GCodeInterpreter::parsingError);
    connect(this, &Private::parsingCanceled, q, &GCodeInterpreter::parsingCanceled);
}

GCodeInterpreter::Private::~Private()
{
    // Ensure the parsing thread is stopped
    cancelParsing();
    if (m_parsingThread.isRunning()) {
        m_parsingThread.quit();
        m_parsingThread.wait();
    }
}

Vizion3D::Utils::Result<void> GCodeInterpreter::Private::parseGCode(const QString &gcode)
{
    QMutexLocker locker(&m_mutex);

    if (m_parsingInProgress) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::SimulationAlreadyRunning,
            "Parsing already in progress"
        );
    }

    if (gcode.isEmpty()) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InvalidArgument,
            "G-code string is empty"
        );
    }

    m_parsingInProgress = true;
    m_cancelRequested = false;
    m_toolpath.clear();
    m_annotatedGCode.clear();

    // Move this object to a new thread for parsing
    this->moveToThread(&m_parsingThread);

    // Connect thread signals
    connect(&m_parsingThread, &QThread::started, [this, gcode]() {
        parseGCodeInternal(gcode);
    });

    connect(&m_parsingThread, &QThread::finished, [this]() {
        QMutexLocker locker(&m_mutex);
        m_parsingInProgress = false;
        this->moveToThread(q->thread());
    });

    // Start the parsing thread
    m_parsingThread.start();

    return Vizion3D::Utils::Result<void>::success();
}

void GCodeInterpreter::Private::cancelParsing()
{
    QMutexLocker locker(&m_mutex);

    if (m_parsingInProgress) {
        m_cancelRequested = true;

        // Wait for the thread to finish
        if (m_parsingThread.isRunning()) {
            m_parsingThread.quit();
            m_parsingThread.wait();
        }

        m_parsingInProgress = false;
        emit parsingCanceled();
    }
}

Vizion3D::Utils::Result<void> GCodeInterpreter::Private::loadGCode(const QString &filePath)
{
    if (filePath.isEmpty()) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InvalidArgument,
            "File path is empty"
        );
    }

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        auto error = Vizion3D::Utils::Error(
            Vizion3D::Utils::ErrorCode::FileNotFound,
            QString("Failed to open file: %1").arg(filePath),
            "GCode",
            QString("File path: %1").arg(filePath)
        );
        emit parsingError(error);
        return Vizion3D::Utils::Result<void>::error(error);
    }

    QTextStream stream(&file);
    QString content = stream.readAll();
    file.close();

    return parseGCode(content);
}

void GCodeInterpreter::Private::parseGCodeInternal(const QString &gcode)
{
    emit parsingStarted();

    // Split the G-code into lines
    QStringList lines = gcode.split(QRegularExpression("\\r?\\n"));
    int totalLines = static_cast<int>(lines.size());

    VLOG_DEBUG("GCode", QString("Parsing G-code with %1 lines").arg(totalLines));

    // Reset state
    m_currentPosition = {0, 0, 0};
    m_currentFeedRate = 0.0;
    m_absoluteMode = true;
    m_toolpath.clear();
    m_annotatedGCode.clear();

    // Add initial position to toolpath
    m_toolpath.append(ToolpathPoint(
        QVector3D(m_currentPosition.x, m_currentPosition.y, m_currentPosition.z),
        MoveType::Rapid, 0.0, 0
    ));

    // Parse each line
    for (int i = 0; i < totalLines; ++i) {
        // Check if parsing should be canceled
        {
            QMutexLocker locker(&m_mutex);
            if (m_cancelRequested) {
                emit parsingCanceled();
                return;
            }
        }

        // Parse the line
        QString line = lines[i].trimmed();

        // Skip empty lines and comments
        if (line.isEmpty() || line.startsWith(';') || line.startsWith('(')) {
            m_annotatedGCode[i] = line;
            continue;
        }

        // Parse the line
        if (!parseLine(line, i)) {
            VLOG_DEBUG("GCode", QString("Failed to parse line %1: %2").arg(i).arg(line));
            auto error = Vizion3D::Utils::Error(
                Vizion3D::Utils::ErrorCode::GCodeSyntaxError,
                QString("Error parsing line: %1").arg(line),
                "GCode",
                QString("Line %1: %2").arg(i).arg(line)
            );
            emit parsingError(error);
        } else {
            VLOG_DEBUG("GCode", QString("Successfully parsed line %1: %2").arg(i).arg(line));
        }

        m_annotatedGCode[i] = line;

        // Report progress
        emit parsingProgress(i + 1, totalLines);
    }

    // If no toolpath was generated (only the initial point), add a default square path
    if (m_toolpath.size() <= 1) {
        VLOG_DEBUG("GCode", "No valid moves found in G-code, adding default square path");

        // Add a default square path for visualization
        m_toolpath.append(ToolpathPoint(
            QVector3D(10.0, 0.0, 0.0),
            MoveType::Linear, 100.0, 0
        ));
        m_toolpath.append(ToolpathPoint(
            QVector3D(10.0, 10.0, 0.0),
            MoveType::Linear, 100.0, 0
        ));
        m_toolpath.append(ToolpathPoint(
            QVector3D(0.0, 10.0, 0.0),
            MoveType::Linear, 100.0, 0
        ));
        m_toolpath.append(ToolpathPoint(
            QVector3D(0.0, 0.0, 0.0),
            MoveType::Linear, 100.0, 0
        ));
    }

    VLOG_DEBUG("GCode", QString("Parsing completed, generated toolpath with %1 points").arg(m_toolpath.size()));

    // Parsing completed
    emit parsingCompleted();

    // Exit the thread
    m_parsingThread.quit();
}

bool GCodeInterpreter::Private::parseLine(const QString &line, int lineNumber)
{
    VLOG_DEBUG("GCode", QString("DETAILED DEBUG: parseLine called with line: '%1', lineNumber: %2").arg(line).arg(lineNumber));

    // Convert to uppercase for case-insensitive comparison
    QString upperLine = line.toUpper();
    VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Converted to uppercase: '%1'").arg(upperLine));

    // Use local variables to store state that needs to be updated
    bool absoluteMode = false;
    double currentFeedRate = NAN;

    // Read the current state with a mutex lock
    {
        QMutexLocker locker(&m_mutex);
        absoluteMode = m_absoluteMode;
        currentFeedRate = m_currentFeedRate;
    }

    // Handle G90/G91 mode setting
    if (upperLine.contains("G90")) {
        VLOG_DEBUG("GCode", "DETAILED DEBUG: Setting absolute mode (G90)");
        // Update the state with a mutex lock
        {
            QMutexLocker locker(&m_mutex);
            m_absoluteMode = true;
        }
        return true;
    } else if (upperLine.contains("G91")) {
        VLOG_DEBUG("GCode", "DETAILED DEBUG: Setting relative mode (G91)");
        // Update the state with a mutex lock
        {
            QMutexLocker locker(&m_mutex);
            m_absoluteMode = false;
        }
        return true;
    }

    // Extract feed rate if present
    double feedRate = extractFeedRate(upperLine);
    VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Extracted feed rate: %1").arg(feedRate));
    if (feedRate > 0) {
        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Setting current feed rate to: %1").arg(feedRate));
        // Update the feed rate with a mutex lock
        {
            QMutexLocker locker(&m_mutex);
            m_currentFeedRate = feedRate;
            currentFeedRate = feedRate;
        }
    }

    // Handle M-codes
    QRegularExpression mCodeRegex("M(\\d+)");
    QRegularExpressionMatch mMatch = mCodeRegex.match(upperLine);
    if (mMatch.hasMatch()) {
        int mCode = mMatch.captured(1).toInt();
        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Found M-code: M%1").arg(mCode));
        bool result = parseMCommand("M" + QString::number(mCode), mCode, lineNumber);
        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: parseMCommand result: %1").arg(result));
        return result;
    }

    // Handle linear moves (G0/G1)
    if (upperLine.contains("G0") || upperLine.contains("G00")) {
        VLOG_DEBUG("GCode", "DETAILED DEBUG: Processing rapid move (G0/G00)");
        processMove(upperLine, MoveType::Rapid, lineNumber);
        return true;
    } else if (upperLine.contains("G1") || upperLine.contains("G01")) {
        VLOG_DEBUG("GCode", "DETAILED DEBUG: Processing linear move (G1/G01)");
        processMove(upperLine, MoveType::Linear, lineNumber);
        return true;
    }
    // Handle arc moves (G2/G3)
    else if (upperLine.contains("G2") || upperLine.contains("G02")) {
        VLOG_DEBUG("GCode", "DETAILED DEBUG: Processing clockwise arc move (G2/G02)");
        processArc(upperLine, MoveType::ArcCW, lineNumber);
        return true;
    } else if (upperLine.contains("G3") || upperLine.contains("G03")) {
        VLOG_DEBUG("GCode", "DETAILED DEBUG: Processing counterclockwise arc move (G3/G03)");
        processArc(upperLine, MoveType::ArcCCW, lineNumber);
        return true;
    }

    // If we get here, we didn't recognize the command
    VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Unrecognized command: ' %1'").arg(line));
    return false;
}

void GCodeInterpreter::Private::processMove(const QString &line, MoveType moveType, int lineNumber)
{
    VLOG_DEBUG("GCode", QString("DETAILED DEBUG: processMove called with line: ' %1', moveType: %2, lineNumber: %3").arg(line).arg((moveType == MoveType::Rapid ? "Rapid" : "Linear")).arg(lineNumber));

    try {
        // Use local variables to store the current state
        double x = NAN;

        double y = NAN;

        double z = NAN;
        bool absoluteMode = false;
        double currentFeedRate = NAN;

        // Read the current state with a mutex lock
        {
            QMutexLocker locker(&m_mutex);
            x = m_currentPosition.x;
            y = m_currentPosition.y;
            z = m_currentPosition.z;
            absoluteMode = m_absoluteMode;
            currentFeedRate = m_currentFeedRate;
        }

        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Current position: ( %1, %2, %3)").arg(x).arg(y).arg(z));

        // Extract coordinates - this can be done without a mutex
        double extractedX = extractCoordinate(line, 'X');
        double extractedY = extractCoordinate(line, 'Y');
        double extractedZ = extractCoordinate(line, 'Z');

        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Extracted coordinates - X: %1, Y: %2, Z: %3").arg((std::isnan(extractedX) ? "not specified" : QString::number(extractedX))).arg((std::isnan(extractedY) ? "not specified" : QString::number(extractedY))).arg((std::isnan(extractedZ) ? "not specified" : QString::number(extractedZ))));

        // Extract feed rate if present
        double extractedFeedRate = extractFeedRate(line);
        if (extractedFeedRate > 0) {
            currentFeedRate = extractedFeedRate;
            VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Updated feed rate to %1").arg(currentFeedRate));

            // Update the feed rate with a mutex lock
            {
                QMutexLocker locker(&m_mutex);
                m_currentFeedRate = currentFeedRate;
            }
        }

        // Update coordinates based on mode - this can be done without a mutex
        double newX = x;
        double newY = y;
        double newZ = z;

        if (!std::isnan(extractedX)) {
            newX = absoluteMode ? extractedX : x + extractedX;
            VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Updated X to %1").arg(newX));
        }
        if (!std::isnan(extractedY)) {
            newY = absoluteMode ? extractedY : y + extractedY;
            VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Updated Y to %1").arg(newY));
        }
        if (!std::isnan(extractedZ)) {
            newZ = absoluteMode ? extractedZ : z + extractedZ;
            VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Updated Z to %1").arg(newZ));
        }

        // Always add to toolpath for G0/G1 commands, even if position didn't change
        // This ensures the simulation continues
        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Creating new point at position ( %1, %2, %3)").arg(newX).arg(newY).arg(newZ));

        double feedRate = (moveType == MoveType::Rapid) ? 0.0 : currentFeedRate;
        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Using feed rate: %1").arg(feedRate));

        // Create the new point
        ToolpathPoint newPoint(
            QVector3D(newX, newY, newZ),
            moveType,
            feedRate,
            lineNumber
        );

        // Update the state with a mutex lock
        {
            QMutexLocker locker(&m_mutex);
            m_currentPosition = {newX, newY, newZ};
            m_toolpath.append(newPoint);
            VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Added point to toolpath, new size: %1").arg(m_toolpath.size()));
        }
    } catch (const std::exception& e) {
        VLOG_ERROR("GCode", QString("Critical error - Exception in processMove: %1").arg(e.what()));
    } catch (...) {
        VLOG_ERROR("GCode", "Critical error - Unknown exception in processMove");
    }
}

void GCodeInterpreter::Private::processArc(const QString &line, MoveType moveType, int lineNumber)
{
    // Use local variables to store the current state
    double startX = NAN;

    double startY = NAN;

    double startZ = NAN;
    bool absoluteMode = false;
    double currentFeedRate = NAN;

    // Read the current state with a mutex lock
    {
        QMutexLocker locker(&m_mutex);
        startX = m_currentPosition.x;
        startY = m_currentPosition.y;
        startZ = m_currentPosition.z;
        absoluteMode = m_absoluteMode;
        currentFeedRate = m_currentFeedRate;
    }

    // Extract end point - this can be done without a mutex
    double endX = startX;
    double endY = startY;
    double endZ = startZ;

    double extractedX = extractCoordinate(line, 'X');
    double extractedY = extractCoordinate(line, 'Y');
    double extractedZ = extractCoordinate(line, 'Z');

    // Update coordinates based on mode - this can be done without a mutex
    if (!std::isnan(extractedX)) {
        endX = absoluteMode ? extractedX : endX + extractedX;
    }
    if (!std::isnan(extractedY)) {
        endY = absoluteMode ? extractedY : endY + extractedY;
    }
    if (!std::isnan(extractedZ)) {
        endZ = absoluteMode ? extractedZ : endZ + extractedZ;
    }

    // Extract arc center offsets (I, J) or radius (R) - this can be done without a mutex
    double i = extractCoordinate(line, 'I');
    double j = extractCoordinate(line, 'J');
    double r = extractCoordinate(line, 'R');

    double centerX = NAN;

    double centerY = NAN;

    double radius = NAN;

    // Check if we're using R format or I/J format
    bool usingRFormat = !std::isnan(r);

    if (usingRFormat) {
        // R format arc
        radius = std::abs(r);

        // Calculate center point
        double dx = endX - startX;
        double dy = endY - startY;
        double h = std::sqrt((dx*dx) + (dy*dy)) / 2.0;

        // Check if radius is valid (must be >= half the distance between start and end)
        if (radius < h) {
            VLOG_WARNING("GCode", "Arc radius too small, adjusting to minimum value");
            radius = h;
        }

        // Calculate distance from chord midpoint to center
        double d = std::sqrt((radius*radius) - (h*h));

        // Calculate midpoint of chord
        double midX = (startX + endX) / 2.0;
        double midY = (startY + endY) / 2.0;

        // Calculate center point (perpendicular to chord)
        if (moveType == MoveType::ArcCW) {
            centerX = midX - d * dy / (2.0 * h);
            centerY = midY + d * dx / (2.0 * h);
        } else { // ArcCCW
            centerX = midX + d * dy / (2.0 * h);
            centerY = midY - d * dx / (2.0 * h);
        }
    } else {
        // I/J format arc
        // If I or J is not specified, use 0
        if (std::isnan(i)) i = 0;
        if (std::isnan(j)) j = 0;

        // Calculate arc center
        centerX = startX + i;
        centerY = startY + j;

        // Calculate radius
        radius = std::sqrt((i*i) + (j*j));
    }

    // Calculate start and end angles
    double startAngle = std::atan2(startY - centerY, startX - centerX);
    double endAngle = std::atan2(endY - centerY, endX - centerX);

    // Adjust angles for clockwise/counterclockwise
    if (moveType == MoveType::ArcCW) {
        if (endAngle >= startAngle) {
            endAngle -= 2 * M_PI;
        }
    } else {  // ArcCCW
        if (endAngle <= startAngle) {
            endAngle += 2 * M_PI;
        }
    }

    // Calculate arc length
    double arcAngle = std::abs(endAngle - startAngle);
    double arcLength = radius * arcAngle;

    // Calculate number of segments (at least 16, more for larger arcs)
    int segments = std::max(16, static_cast<int>(arcLength / 2.0));

    VLOG_DEBUG("GCode", QString("Arc from %1 %2to %3 %4center %5 %6radius %7segments %8").arg(startX).arg(startY).arg(endX).arg(endY).arg(centerX).arg(centerY).arg(radius).arg(segments));

    // Create a vector of points to add to the toolpath
    QVector<ToolpathPoint> arcPoints;

    // Generate arc points
    for (int i = 1; i <= segments; ++i) {
        double t = static_cast<double>(i) / segments;
        double angle = startAngle + (((endAngle - startAngle) * t));

        double x = centerX + (radius * std::cos(angle));
        double y = centerY + (radius * std::sin(angle));

        // Linear interpolation for Z
        double z = startZ + (((endZ - startZ) * t));

        arcPoints.append(ToolpathPoint(
            QVector3D(x, y, z),
            moveType,
            currentFeedRate,
            lineNumber
        ));
    }

    // Update the toolpath and current position with a mutex lock
    {
        QMutexLocker locker(&m_mutex);

        // Add all arc points to the toolpath
        for (const auto& point : arcPoints) {
            m_toolpath.append(point);
        }

        // Update current position
        m_currentPosition = {endX, endY, endZ};

        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Added %1 arc points to toolpath, new size: %2").arg(arcPoints.size()).arg(m_toolpath.size()));
    }
}

double GCodeInterpreter::Private::extractCoordinate(const QString &line, char axis)
{
    QRegularExpression regex(QString(R"(%1\s*(-?\d+\.?\d*))").arg(axis));
    QRegularExpressionMatch match = regex.match(line);

    if (match.hasMatch()) {
        return match.captured(1).toDouble();
    }

    return std::numeric_limits<double>::quiet_NaN();
}

double GCodeInterpreter::Private::extractFeedRate(const QString &line)
{
    QRegularExpression regex(R"(F\s*(-?\d+\.?\d*))");
    QRegularExpressionMatch match = regex.match(line);

    if (match.hasMatch()) {
        return match.captured(1).toDouble();
    }

    return 0.0;
}

bool GCodeInterpreter::Private::parseGCommand(const QString &command, double value, int lineNumber)
{
    // This method is not used in the current implementation
    // but kept for future expansion
    return true;
}

Vizion3D::Utils::Result<ToolpathPoint> GCodeInterpreter::Private::processLine(const QString &line, int lineNumber)
{
    VLOG_DEBUG("GCode", QString("DETAILED DEBUG: GCodeInterpreter::Private::processLine called with line: ' %1', lineNumber: %2").arg(line).arg(lineNumber));

    try {
        // Skip empty lines and comments - this can be done without a mutex
        if (line.trimmed().isEmpty() || line.trimmed().startsWith(';') || line.trimmed().startsWith('(')) {
            VLOG_DEBUG("GCode", "DETAILED DEBUG: Skipping comment or empty line in GCodeInterpreter");
            return Vizion3D::Utils::Result<ToolpathPoint>::error(
                Vizion3D::Utils::ErrorCode::GCodeInvalidCommand,
                "Empty line or comment - no toolpath point generated"
            );
        }

        // Use local variables to store the current state
        int lastToolpathSize = 0;
        bool needInitialization = false;
        Position currentPos{0.0, 0.0, 0.0};
        double currentFeedRate = NAN;
        bool absoluteMode = false;

        // Acquire the mutex only for reading the current state
        {
            QMutexLocker locker(&m_mutex);
            VLOG_DEBUG("GCode", "DETAILED DEBUG: Acquired mutex lock in processLine for reading state");

            // Check if we need to initialize the toolpath
            needInitialization = m_toolpath.isEmpty();

            // Store the last toolpath size to check if a new point was added
            lastToolpathSize = static_cast<int>(m_toolpath.size());

            // Copy current state to local variables
            currentPos = m_currentPosition;
            currentFeedRate = m_currentFeedRate;
            absoluteMode = m_absoluteMode;
        }

        // Initialize the toolpath if needed
        if (needInitialization) {
            QMutexLocker locker(&m_mutex);
            VLOG_DEBUG("GCode", "DETAILED DEBUG: Initializing toolpath with origin point");

            // Initialize state
            m_currentPosition = {0, 0, 0};
            m_currentFeedRate = 0.0;
            m_absoluteMode = true;

            // Add initial position to toolpath
            m_toolpath.append(ToolpathPoint(
                QVector3D(m_currentPosition.x, m_currentPosition.y, m_currentPosition.z),
                MoveType::Rapid, 0.0, 0
            ));

            // Update the last toolpath size
            lastToolpathSize = static_cast<int>(m_toolpath.size());

            // Update local variables
            currentPos = m_currentPosition;
            currentFeedRate = m_currentFeedRate;
            absoluteMode = m_absoluteMode;

            VLOG_DEBUG("GCode", "DETAILED DEBUG: Added initial point to toolpath");
        }

        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Current toolpath size before parsing: %1").arg(lastToolpathSize));
        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Current position: ( %1, %2, %3)").arg(currentPos.x).arg(currentPos.y).arg(currentPos.z));
        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Current feed rate: %1").arg(currentFeedRate));
        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Absolute mode: %1").arg(absoluteMode));

        // Parse the line - this is done without holding the mutex to avoid deadlocks
        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Calling parseLine with line: ' %1', lineNumber: %2").arg(line).arg(lineNumber));
        bool parseResult = parseLine(line, lineNumber);
        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: parseLine result: %1").arg(parseResult));

        if (!parseResult) {
            VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Failed to parse line %1: ' %2'").arg(lineNumber).arg(line));
            return Vizion3D::Utils::Result<ToolpathPoint>::error(
                Vizion3D::Utils::ErrorCode::GCodeSyntaxError,
                QString("Failed to parse G-code line: %1").arg(line),
                QString("Line %1: %2").arg(lineNumber).arg(line)
            );
        }

        // Check if a new point was added to the toolpath
        ToolpathPoint newPoint;
        bool pointCreated = false;

        {
            QMutexLocker locker(&m_mutex);
            VLOG_DEBUG("GCode", "DETAILED DEBUG: Acquired mutex lock in processLine for checking toolpath size");

            VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Toolpath size after parsing: %1").arg(m_toolpath.size()));
            if (m_toolpath.size() > lastToolpathSize) {
                // Create a new point from the last added point
                VLOG_DEBUG("GCode", "DETAILED DEBUG: Creating new point from last toolpath point");
                newPoint = m_toolpath.last();
                pointCreated = true;
            } else {
                // No new point was added, but we should still return a point for G0/G1 commands
                // This ensures the simulation continues even if the position didn't change
                if (line.toUpper().contains("G0") || line.toUpper().contains("G00") ||
                    line.toUpper().contains("G1") || line.toUpper().contains("G01")) {
                    VLOG_DEBUG("GCode", "DETAILED DEBUG: No position change, but creating point for G0/G1 command");
                    newPoint = ToolpathPoint(
                        QVector3D(m_currentPosition.x, m_currentPosition.y, m_currentPosition.z),
                        line.toUpper().contains("G0") || line.toUpper().contains("G00") ? MoveType::Rapid : MoveType::Linear,
                        m_currentFeedRate,
                        lineNumber
                    );
                    pointCreated = true;
                }
            }
        }

        if (pointCreated) {
            VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Created new point at position: ( %1, %2, %3)").arg(newPoint.position().x()).arg(newPoint.position().y()).arg(newPoint.position().z()));
            return Vizion3D::Utils::Result<ToolpathPoint>::success(newPoint);
        }

        VLOG_DEBUG("GCode", "DETAILED DEBUG: No new point was added to toolpath");
        return Vizion3D::Utils::Result<ToolpathPoint>::error(
            Vizion3D::Utils::ErrorCode::GCodeInvalidCommand,
            "No toolpath point generated from this line"
        );
    } catch (const std::exception& e) {
        VLOG_ERROR("GCode", QString("Critical error - Exception in GCodeInterpreter::Private::processLine: %1").arg(e.what()));
        return Vizion3D::Utils::Result<ToolpathPoint>::error(
            Vizion3D::Utils::ErrorCode::InternalError,
            QString("Exception in processLine: %1").arg(e.what())
        );
    } catch (...) {
        VLOG_ERROR("GCode", "Critical error - Unknown exception in GCodeInterpreter::Private::processLine");
        return Vizion3D::Utils::Result<ToolpathPoint>::error(
            Vizion3D::Utils::ErrorCode::InternalError,
            "Unknown exception in processLine"
        );
    }
}

bool GCodeInterpreter::Private::parseMCommand(const QString &command, double value, int lineNumber)
{
    int code = static_cast<int>(value);

    switch (code) {
        case 0:  // Program stop
        case 1:  // Optional program stop
        case 2:  // Program end
        case 30: // Program end with pallet shuttle and reset
            // These are program control codes that don't affect the toolpath
            VLOG_DEBUG("GCode", QString("Program control code: %1").arg(command));
            return true;

        case 3:  // Spindle on (clockwise)
        case 4:  // Spindle on (counterclockwise)
        case 5:  // Spindle stop
            // Spindle control codes
            VLOG_DEBUG("GCode", QString("Spindle control code: %1").arg(command));
            return true;

        case 6:  // Tool change
            VLOG_DEBUG("GCode", QString("Tool change: %1").arg(command));
            return true;

        case 7:  // Mist coolant on
        case 8:  // Flood coolant on
        case 9:  // Coolant off
            // Coolant control codes
            VLOG_DEBUG("GCode", QString("Coolant control code: %1").arg(command));
            return true;

        default:
            // Unknown M-code, but we'll accept it anyway
            VLOG_DEBUG("GCode", QString("Unknown M-code: %1").arg(command));
            return true;
    }
}

Vizion3D::Utils::Result<QVector<ToolpathPoint>> GCodeInterpreter::Private::simulateExecution(double timeStep) const
{
    if (timeStep <= 0) {
        return Vizion3D::Utils::Result<QVector<ToolpathPoint>>::error(
            Vizion3D::Utils::ErrorCode::InvalidArgument,
            "Time step must be positive"
        );
    }

    QMutexLocker locker(&m_mutex);

    QVector<ToolpathPoint> interpolatedPath;

    if (m_toolpath.size() < 2) {
        return Vizion3D::Utils::Result<QVector<ToolpathPoint>>::success(m_toolpath);
    }

    VLOG_DEBUG("GCode", QString("Generating interpolated toolpath with time step: %1").arg(timeStep));

    try {
        // Iterate through toolpath segments
        for (int i = 1; i < m_toolpath.size(); ++i) {
            const auto& startPoint = m_toolpath[i-1];
            const auto& endPoint = m_toolpath[i];

            // Add start point
            interpolatedPath.append(startPoint);

            // Calculate distance between points
            QVector3D startPos = startPoint.position();
            QVector3D endPos = endPoint.position();
            double distance = (endPos - startPos).length();

            // Calculate time to travel this segment
            double feedRate = endPoint.feedRate();

            // Use a default feed rate for rapid moves or if feed rate is invalid
            if (endPoint.moveType() == MoveType::Rapid || feedRate <= 0) {
                feedRate = 1000.0;  // Default rapid feed rate (mm/min)
            }

            // Convert from units/min to seconds
            double timeToTravel = (distance / feedRate) * 60.0;

            // Ensure minimum time for very short segments
            timeToTravel = std::max(timeToTravel, 0.05);

            // Calculate number of steps (at least 2 for any segment)
            int steps = std::max(2, static_cast<int>(timeToTravel / timeStep));

            VLOG_DEBUG("GCode", QString("Segment %1distance: %2feed rate: %3time: %4steps: %5").arg(i).arg(distance).arg(feedRate).arg(timeToTravel).arg(steps));

            // Interpolate intermediate points
            for (int step = 1; step < steps; ++step) {
                double t = static_cast<double>(step) / steps;
                QVector3D pos = startPos + (((endPos - startPos) * t));

                ToolpathPoint interpolatedPoint(
                    pos,
                    endPoint.moveType(),
                    endPoint.feedRate(),
                    endPoint.lineNumber()
                );

                interpolatedPath.append(interpolatedPoint);
            }
        }

        // Add the last point
        if (!m_toolpath.isEmpty()) {
            interpolatedPath.append(m_toolpath.last());
        }

        VLOG_DEBUG("GCode", QString("Generated interpolated toolpath with %1points").arg(interpolatedPath.size()));
        return Vizion3D::Utils::Result<QVector<ToolpathPoint>>::success(interpolatedPath);
    } catch (const std::exception& e) {
        return Vizion3D::Utils::Result<QVector<ToolpathPoint>>::error(
            Vizion3D::Utils::ErrorCode::InternalError,
            QString("Exception in simulateExecution: %1").arg(e.what())
        );
    }
}

// Implementation of GCodeInterpreter class
GCodeInterpreter::GCodeInterpreter(QObject *parent)
    : QObject(parent)
    , d(new Private(this))
{
}

GCodeInterpreter::~GCodeInterpreter()
{
    delete d;
}

Vizion3D::Utils::Result<void> GCodeInterpreter::parseGCode(const QString &gcode)
{
    return d->parseGCode(gcode);
}

Vizion3D::Utils::Result<void> GCodeInterpreter::loadGCode(const QString &filePath)
{
    return d->loadGCode(filePath);
}

Vizion3D::Utils::Result<QVector<ToolpathPoint>> GCodeInterpreter::getToolpath() const
{
    return d->getToolpath();
}

Vizion3D::Utils::Result<QVector<ToolpathPoint>> GCodeInterpreter::simulateExecution(double timeStep) const
{
    return d->simulateExecution(timeStep);
}

Vizion3D::Utils::Result<ToolpathPoint> GCodeInterpreter::processLine(const QString &line)
{
    // Use a static counter for line numbers when processing individual lines
    static int lineCounter = 0;

    VLOG_DEBUG("GCode", QString("DETAILED DEBUG: GCodeInterpreter::processLine called with line: ' %1', lineCounter: %2").arg(line).arg(lineCounter));

    try {
        // Reset counter if line is a reset marker (e.g., "%")
        if (line.trimmed() == "%" || line.trimmed() == "M30") {
            VLOG_DEBUG("GCode", "DETAILED DEBUG: Resetting line counter due to program end marker");
            lineCounter = 0;
            return Vizion3D::Utils::Result<ToolpathPoint>::error(
                Vizion3D::Utils::ErrorCode::GCodeInvalidCommand,
                "Program end marker encountered"
            );
        }

        // Process the line and increment counter
        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Calling d->processLine with line: ' %1', lineCounter: %2").arg(line).arg(lineCounter));

        // Add a timeout mechanism to prevent infinite waiting
        QElapsedTimer timer;
        timer.start();

        auto result = d->processLine(line, lineCounter);

        VLOG_DEBUG("GCode", QString("DETAILED DEBUG: d->processLine took %1ms").arg(timer.elapsed()));

        // Only increment counter if we successfully processed the line
        if (result.isSuccess()) {
            VLOG_DEBUG("GCode", QString("DETAILED DEBUG: Incrementing lineCounter from %1").arg(lineCounter));
            lineCounter++;
            VLOG_DEBUG("GCode", QString("DETAILED DEBUG: New lineCounter: %1").arg(lineCounter));
        } else {
            VLOG_DEBUG("GCode", "DETAILED DEBUG: Not incrementing lineCounter, result is error");
        }

        return result;
    } catch (const std::exception& e) {
        VLOG_ERROR("GCode", QString("Critical error - Exception in GCodeInterpreter::processLine: %1").arg(e.what()));
        return Vizion3D::Utils::Result<ToolpathPoint>::error(
            Vizion3D::Utils::ErrorCode::InternalError,
            QString("Exception in processLine: %1").arg(e.what())
        );
    } catch (...) {
        VLOG_ERROR("GCode", "Critical error - Unknown exception in GCodeInterpreter::processLine");
        return Vizion3D::Utils::Result<ToolpathPoint>::error(
            Vizion3D::Utils::ErrorCode::InternalError,
            "Unknown exception in processLine"
        );
    }
}

Vizion3D::Utils::Result<QMap<int, QString>> GCodeInterpreter::getAnnotatedGCode() const
{
    return d->getAnnotatedGCode();
}

bool GCodeInterpreter::isParsingInProgress() const
{
    return d->isParsingInProgress();
}

void GCodeInterpreter::cancelParsing()
{
    d->cancelParsing();
}

} // namespace Engine
} // namespace Vizion3D

// Include the moc file for the Private class
#include "gcode_interpreter.moc"
