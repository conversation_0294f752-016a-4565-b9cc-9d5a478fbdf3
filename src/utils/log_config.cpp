#include "utils/log_config.h"
#include "utils/logger.h"
#include <QStandardPaths>
#include <QDir>

namespace Vizion3D {
namespace Utils {

LogConfig& LogConfig::instance() {
    static LogConfig instance;
    return instance;
}

LogConfig::LogConfig()
    : QObject(nullptr)
    , m_globalLogLevel(LogLevel::Info)
    , m_fileLoggingEnabled(false)  // Default to file logging disabled
    , m_maxLogFileSizeBytes(10 * 1024 * 1024) // 10 MB
    , m_maxLogBackupCount(5)
    , m_consoleLoggingEnabled(true) {
    // Log the initial state for debugging
    fprintf(stdout, "[DEBUG] [System] LogConfig constructor: file logging disabled by default\n");
    fflush(stdout);
}

LogConfig::~LogConfig() {
}

void LogConfig::initialize(const QString& appName, const QString& configFilePath) {
    m_appName = appName;

    // Set default log file path
    QString logDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    m_logFilePath = QDir(logDir).filePath(m_appName + ".log");

    // Load configuration if provided
    if (!configFilePath.isEmpty()) {
        m_configFilePath = configFilePath;
        loadConfiguration(m_configFilePath);
    } else {
        m_configFilePath = defaultConfigFilePath();

        // Try to load from default location
        QFile file(m_configFilePath);
        if (file.exists()) {
            loadConfiguration(m_configFilePath);
        }
    }

    // Apply configuration to the logger
    applyConfiguration();
}

void LogConfig::setGlobalLogLevel(LogLevel level) {
    if (m_globalLogLevel != level) {
        m_globalLogLevel = level;
        applyConfiguration();
        emit configurationChanged();
    }
}

LogLevel LogConfig::globalLogLevel() const {
    return m_globalLogLevel;
}

void LogConfig::setCategoryLogLevel(const QString& category, LogLevel level) {
    if (m_categoryLogLevels.value(category, m_globalLogLevel) != level) {
        m_categoryLogLevels[category] = level;
        applyConfiguration();
        emit configurationChanged();
    }
}

LogLevel LogConfig::categoryLogLevel(const QString& category) const {
    return m_categoryLogLevels.value(category, m_globalLogLevel);
}

QMap<QString, LogLevel> LogConfig::categoryLogLevels() const {
    return m_categoryLogLevels;
}

void LogConfig::setFileLoggingEnabled(bool enabled,
                                     const QString& filePath,
                                     qint64 maxSizeBytes,
                                     int maxBackupCount) {
    // Add debug output to track file logging state changes
    fprintf(stdout, "[DEBUG] [System] LogConfig::setFileLoggingEnabled called with enabled=%d, current state=%d\n",
            enabled ? 1 : 0, m_fileLoggingEnabled ? 1 : 0);
    fflush(stdout);

    bool changed = false;

    if (m_fileLoggingEnabled != enabled) {
        fprintf(stdout, "[DEBUG] [System] LogConfig: Changing file logging enabled from %d to %d\n",
                m_fileLoggingEnabled ? 1 : 0, enabled ? 1 : 0);
        fflush(stdout);

        m_fileLoggingEnabled = enabled;
        changed = true;
    }

    if (!filePath.isEmpty() && m_logFilePath != filePath) {
        fprintf(stdout, "[DEBUG] [System] LogConfig: Changing log file path from '%s' to '%s'\n",
                qPrintable(m_logFilePath), qPrintable(filePath));
        fflush(stdout);

        m_logFilePath = filePath;
        changed = true;
    }

    if (m_maxLogFileSizeBytes != maxSizeBytes) {
        fprintf(stdout, "[DEBUG] [System] LogConfig: Changing max log file size from %lld to %lld bytes\n",
                (long long)m_maxLogFileSizeBytes, (long long)maxSizeBytes);
        fflush(stdout);

        m_maxLogFileSizeBytes = maxSizeBytes;
        changed = true;
    }

    if (m_maxLogBackupCount != maxBackupCount) {
        fprintf(stdout, "LogConfig: Changing max backup count from %d to %d\n",
                m_maxLogBackupCount, maxBackupCount);
        fflush(stdout);

        m_maxLogBackupCount = maxBackupCount;
        changed = true;
    }

    if (changed) {
        fprintf(stdout, "LogConfig: Applying updated file logging configuration\n");
        fflush(stdout);

        applyConfiguration();
        emit configurationChanged();
    } else {
        fprintf(stdout, "LogConfig: No changes to file logging configuration\n");
        fflush(stdout);
    }
}

bool LogConfig::isFileLoggingEnabled() const {
    return m_fileLoggingEnabled;
}

QString LogConfig::logFilePath() const {
    return m_logFilePath;
}

qint64 LogConfig::maxLogFileSizeBytes() const {
    return m_maxLogFileSizeBytes;
}

int LogConfig::maxLogBackupCount() const {
    return m_maxLogBackupCount;
}

void LogConfig::setConsoleLoggingEnabled(bool enabled) {
    if (m_consoleLoggingEnabled != enabled) {
        m_consoleLoggingEnabled = enabled;
        applyConfiguration();
        emit configurationChanged();
    }
}

bool LogConfig::isConsoleLoggingEnabled() const {
    return m_consoleLoggingEnabled;
}

void LogConfig::loadConfiguration(const QString& filePath) {
    QSettings settings(filePath, QSettings::IniFormat);

    // Load global log level
    QString globalLevelStr = settings.value("Logging/GlobalLevel", "INFO").toString();
    m_globalLogLevel = stringToLogLevel(globalLevelStr);

    // Load category log levels
    m_categoryLogLevels.clear();
    settings.beginGroup("CategoryLevels");
    QStringList categories = settings.childKeys();
    for (const QString& category : categories) {
        QString levelStr = settings.value(category).toString();
        m_categoryLogLevels[category] = stringToLogLevel(levelStr);
    }
    settings.endGroup();

    // Load file logging settings - default to disabled
    m_fileLoggingEnabled = settings.value("FileLogging/Enabled", false).toBool();
    QString logFilePath = settings.value("FileLogging/FilePath", "").toString();
    if (!logFilePath.isEmpty()) {
        m_logFilePath = logFilePath;
    }
    m_maxLogFileSizeBytes = settings.value("FileLogging/MaxSizeBytes", 10 * 1024 * 1024).toLongLong();
    m_maxLogBackupCount = settings.value("FileLogging/MaxBackupCount", 5).toInt();

    fprintf(stdout, "LogConfig::loadConfiguration: File logging enabled=%d\n",
            m_fileLoggingEnabled ? 1 : 0);
    fflush(stdout);

    // Load console logging settings
    m_consoleLoggingEnabled = settings.value("ConsoleLogging/Enabled", true).toBool();
}

void LogConfig::saveConfiguration(const QString& filePath) {
    QSettings settings(filePath, QSettings::IniFormat);

    // Save global log level
    settings.setValue("Logging/GlobalLevel", logLevelToString(m_globalLogLevel));

    // Save category log levels
    settings.beginGroup("CategoryLevels");
    for (auto it = m_categoryLogLevels.constBegin(); it != m_categoryLogLevels.constEnd(); ++it) {
        settings.setValue(it.key(), logLevelToString(it.value()));
    }
    settings.endGroup();

    // Save file logging settings
    settings.setValue("FileLogging/Enabled", m_fileLoggingEnabled);
    settings.setValue("FileLogging/FilePath", m_logFilePath);
    settings.setValue("FileLogging/MaxSizeBytes", m_maxLogFileSizeBytes);
    settings.setValue("FileLogging/MaxBackupCount", m_maxLogBackupCount);

    // Save console logging settings
    settings.setValue("ConsoleLogging/Enabled", m_consoleLoggingEnabled);

    settings.sync();
}

QString LogConfig::defaultConfigFilePath() const {
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    return QDir(configDir).filePath(m_appName + "_logging.ini");
}

void LogConfig::applyConfiguration() {
    // Use a static flag to prevent recursive calls during initialization
    // This prevents potential deadlocks when Qt debug messages are generated
    // during the configuration process
    static bool isApplyingConfiguration = false;

    // Add debug output to track configuration application
    fprintf(stdout, "LogConfig::applyConfiguration called, file logging enabled=%d\n",
            m_fileLoggingEnabled ? 1 : 0);
    fflush(stdout);

    // If we're already applying configuration, return to prevent recursion
    if (isApplyingConfiguration) {
        fprintf(stdout, "Preventing recursive call to applyConfiguration()\n");
        fflush(stdout);
        return;
    }

    // Set the flag to indicate we're applying configuration
    isApplyingConfiguration = true;

    try {
        // Apply global log level
        fprintf(stdout, "LogConfig: Applying global log level: %s\n",
                qPrintable(logLevelToString(m_globalLogLevel)));
        fflush(stdout);

        Logger::instance().setGlobalLogLevel(m_globalLogLevel);

        // Apply category log levels
        fprintf(stdout, "LogConfig: Applying %lld category log levels\n", (long long)m_categoryLogLevels.size());
        fflush(stdout);

        for (auto it = m_categoryLogLevels.constBegin(); it != m_categoryLogLevels.constEnd(); ++it) {
            Logger::instance().setCategoryLogLevel(it.key(), it.value());
        }

        // Update Logger's sinks based on current configuration
        fprintf(stdout, "LogConfig: Updating Logger sinks (fileLogging=%d, consoleLogging=%d)\n",
                m_fileLoggingEnabled ? 1 : 0, m_consoleLoggingEnabled ? 1 : 0);
        fflush(stdout);

        // Call the updateSinks method to apply all sink-related configuration at once
        Logger::instance().updateSinks();

        fprintf(stdout, "LogConfig: Configuration applied successfully\n");
        fflush(stdout);
    }
    catch (const std::exception& e) {
        fprintf(stderr, "[ERROR] [System] Exception in LogConfig::applyConfiguration: %s\n", e.what());
        fflush(stderr);
        // Use fprintf instead of qCritical to avoid circular dependency
    }
    catch (...) {
        fprintf(stderr, "[ERROR] [System] Unknown exception in LogConfig::applyConfiguration\n");
        fflush(stderr);
        // Use fprintf instead of qCritical to avoid circular dependency
    }

    // Reset the flag
    isApplyingConfiguration = false;
}

} // namespace Utils
} // namespace Vizion3D
