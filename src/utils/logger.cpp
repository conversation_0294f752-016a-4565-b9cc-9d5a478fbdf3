#include "utils/logger.h"
#include "utils/log_config.h"

namespace Vizion3D {
namespace Utils {

QString logLevelToString(LogLevel level) {
    switch (level) {
        case LogLevel::Trace:   return "TRACE";
        case LogLevel::Debug:   return "DEBUG";
        case LogLevel::Info:    return "INFO";
        case LogLevel::Warning: return "WARNING";
        case LogLevel::Error:   return "ERROR";
        case LogLevel::Fatal:   return "FATAL";
        default:                return "UNKNOWN";
    }
}

LogLevel stringToLogLevel(const QString& levelStr) {
    QString upperLevel = levelStr.toUpper();
    if (upperLevel == "TRACE")   return LogLevel::Trace;
    if (upperLevel == "DEBUG")   return LogLevel::Debug;
    if (upperLevel == "INFO")    return LogLevel::Info;
    if (upperLevel == "WARNING") return LogLevel::Warning;
    if (upperLevel == "ERROR")   return LogLevel::Error;
    if (upperLevel == "FATAL")   return LogLevel::Fatal;
    return LogLevel::Info; // Default
}

// ConsoleLogSink implementation
void ConsoleLogSink::write(const QDateTime& timestamp,
                          LogLevel level,
                          const QString& category,
                          const QString& message,
                          const QString& file,
                          int line,
                          const QString& function,
                          Qt::HANDLE threadId) {
    QString formattedMessage = QString("[%1] [%2] [%3] [%4:%5] [%6] [Thread %7] %8")
        .arg(timestamp.toString("yyyy-MM-dd hh:mm:ss.zzz"))
        .arg(logLevelToString(level))
        .arg(category)
        .arg(file)
        .arg(line)
        .arg(function)
        .arg(reinterpret_cast<quintptr>(threadId))
        .arg(message);

    QTextStream stream(level >= LogLevel::Error ? stderr : stdout);
    stream << formattedMessage << Qt::endl;
}

void ConsoleLogSink::flush() {
    fflush(stdout);
    fflush(stderr);
}

// FileLogSink implementation
FileLogSink::FileLogSink(const QString& filePath, qint64 maxSizeBytes, int maxBackupCount)
    : m_filePath(filePath)
    , m_maxSizeBytes(maxSizeBytes)
    , m_maxBackupCount(maxBackupCount) {
    // Don't open the file in the constructor to avoid blocking
    // It will be opened on the first write operation
}

FileLogSink::~FileLogSink() {
    if (m_file.isOpen()) {
        m_stream.flush();
        m_file.close();
    }
}

void FileLogSink::write(const QDateTime& timestamp,
                       LogLevel level,
                       const QString& category,
                       const QString& message,
                       const QString& file,
                       int line,
                       const QString& function,
                       Qt::HANDLE threadId) {
    QMutexLocker locker(&m_mutex);

    if (!m_file.isOpen() && !openFile()) {
        return;
    }

    // Check if we need to rotate the log file
    if (m_file.size() > m_maxSizeBytes) {
        m_stream.flush();
        m_file.close();
        rotateLogFiles();
        if (!openFile()) {
            return;
        }
    }

    QString formattedMessage = QString("[%1] [%2] [%3] [%4:%5] [%6] [Thread %7] %8")
        .arg(timestamp.toString("yyyy-MM-dd hh:mm:ss.zzz"))
        .arg(logLevelToString(level))
        .arg(category)
        .arg(file)
        .arg(line)
        .arg(function)
        .arg(reinterpret_cast<quintptr>(threadId))
        .arg(message);

    m_stream << formattedMessage << Qt::endl;
}

void FileLogSink::flush() {
    QMutexLocker locker(&m_mutex);
    if (m_file.isOpen()) {
        m_stream.flush();
    }
}

bool FileLogSink::openFile() {
    try {
        // If the file is already open, return success
        if (m_file.isOpen()) {
            return true;
        }

        // Ensure the directory exists, but don't block if it fails
        QDir dir = QFileInfo(m_filePath).dir();
        if (!dir.exists()) {
            // Try to create the directory, but don't block if it fails
            if (!dir.mkpath(".")) {
                fprintf(stderr, "[ERROR] [System] Failed to create log directory: %s\n", qPrintable(dir.path()));
                fflush(stderr);
                return false;
            }
        }

        // Set the file name and try to open it
        m_file.setFileName(m_filePath);

        // Try to open the file with a timeout to avoid blocking indefinitely
        QElapsedTimer timer;
        timer.start();

        while (!m_file.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
            // If we've been trying for more than 100ms, give up to avoid blocking
            if (timer.elapsed() > 100) {
                fprintf(stderr, "[ERROR] [System] Failed to open log file after 100ms: %s\n", qPrintable(m_filePath));
                fflush(stderr);
                return false;
            }

            // Sleep for a short time before trying again
            QThread::msleep(10);
        }

        // Set up the text stream
        m_stream.setDevice(&m_file);
        return true;
    }
    catch (const std::exception& e) {
        fprintf(stderr, "[ERROR] [System] Exception in FileLogSink::openFile: %s\n", e.what());
        fflush(stderr);
        return false;
    }
    catch (...) {
        fprintf(stderr, "[ERROR] [System] Unknown exception in FileLogSink::openFile\n");
        fflush(stderr);
        return false;
    }
}

void FileLogSink::rotateLogFiles() {
    // Remove the oldest log file if it exists
    QString oldestBackupPath = m_filePath + QString(".%1").arg(m_maxBackupCount);
    QFile::remove(oldestBackupPath);

    // Rename existing backup files to make room for the new one
    for (int i = m_maxBackupCount - 1; i > 0; --i) {
        QString oldPath = m_filePath + QString(".%1").arg(i);
        QString newPath = m_filePath + QString(".%1").arg(i + 1);
        QFile::rename(oldPath, newPath);
    }

    // Rename the current log file to .1
    QFile::rename(m_filePath, m_filePath + ".1");
}

// Qt message handler
void qtMessageHandler(QtMsgType type, const QMessageLogContext& context, const QString& msg) {
    // Check if the logger is initialized to avoid potential issues during startup/shutdown
    if (!Logger::instance().isInitialized()) {
        // Fall back to default Qt message handler behavior
        fprintf(stderr, "[ERROR] [System] Qt Message (Logger not initialized): %s\n", qPrintable(msg));
        fflush(stderr);
        return;
    }

    try {
        LogLevel level;
        switch (type) {
            case QtDebugMsg:    level = LogLevel::Debug; break;
            case QtInfoMsg:     level = LogLevel::Info; break;
            case QtWarningMsg:  level = LogLevel::Warning; break;
            case QtCriticalMsg: level = LogLevel::Error; break;
            case QtFatalMsg:    level = LogLevel::Fatal; break;
            default:            level = LogLevel::Info; break;
        }

        Logger::instance().log(level, "Qt", msg, context.file, context.line, context.function);

        // If it's a fatal message, flush the logs before the application terminates
        if (type == QtFatalMsg) {
            Logger::instance().flush();
        }
    }
    catch (const std::exception& e) {
        // If there's an exception in the logging system, fall back to stderr
        fprintf(stderr, "Exception in qtMessageHandler: %s\nOriginal message: %s\n",
                e.what(), qPrintable(msg));
    }
    catch (...) {
        // If there's an unknown exception, fall back to stderr
        fprintf(stderr, "Unknown exception in qtMessageHandler\nOriginal message: %s\n",
                qPrintable(msg));
    }
}

// Logger implementation
Logger& Logger::instance() {
    static Logger instance;
    return instance;
}

Logger::Logger()
    : m_initialized(false) {
    // No longer need to initialize m_globalLogLevel as it's now managed by LogConfig
}

Logger::~Logger() {
    shutdown();
}

void Logger::initialize(const QString& appName, const QString& configFilePath) {
    // Initialize without message handler
    initializeWithoutMessageHandler(appName, configFilePath);

    // Install Qt message handler AFTER initialization and OUTSIDE the mutex lock
    // to prevent potential deadlocks when Qt logging functions call back into our logger
    qInstallMessageHandler(qtMessageHandler);
}

void Logger::initializeWithoutMessageHandler(const QString& appName, const QString& configFilePath) {
    try {
        // Print a debug message to indicate we're starting initialization
        fprintf(stdout, "[INFO] [System] Starting logger initialization for %s...\n", qPrintable(appName));
        fflush(stdout);

        // Use a scope for the mutex locker to ensure it's released before returning
        QMutexLocker locker(&m_mutex);

        if (m_initialized) {
            fprintf(stdout, "[INFO] [System] Logger already initialized, returning early\n");
            fflush(stdout);
            return;
        }

        m_appName = appName;

        // Set up default sinks
        fprintf(stdout, "[INFO] [System] Setting up console sink...\n");
        fflush(stdout);
        m_consoleSink = std::make_shared<ConsoleLogSink>();

        // Add the console sink first so we can log messages during initialization
        m_sinks.push_back(m_consoleSink);
        fprintf(stdout, "[INFO] [System] Console sink added\n");
        fflush(stdout);

        // Mark as initialized early so we can use logging during the rest of initialization
        m_initialized = true;

        // Release the mutex temporarily to avoid potential deadlocks
        locker.unlock();

        // Log a message to indicate progress
        fprintf(stdout, "[INFO] [System] Basic logger initialization complete, setting up file sink...\n");
        fflush(stdout);

        // Set up default file sink in the application data directory
        // Do this outside the mutex lock to avoid blocking
        QString logDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
        QString logFilePath = QDir(logDir).filePath(m_appName + ".log");

        // Create the file sink but don't add it to the sinks list yet
        std::shared_ptr<FileLogSink> fileSink = std::make_shared<FileLogSink>(logFilePath);

        // Re-acquire the mutex to update the sinks list
        locker.relock();

        // Store the file sink and add it to the sinks list
        m_fileSink = fileSink;
        m_sinks.push_back(m_fileSink);

        // Release the mutex again before loading configuration
        locker.unlock();

        fprintf(stdout, "[INFO] [System] File sink added, loading configuration...\n");
        fflush(stdout);

        // Initialize LogConfig with the same app name and config file path
        // This ensures LogConfig is initialized before we use it
        LogConfig::instance().initialize(m_appName, configFilePath);

        fprintf(stdout, "[INFO] [System] LogConfig initialized\n");
        fflush(stdout);

        // Log initialization message using fprintf to avoid potential deadlocks
        fprintf(stdout, "[INFO] [System] Logging system initialized\n");
        fflush(stdout);

        // Log using the actual logging system, but outside the mutex lock
        log(LogLevel::Info, "System", "Logging system initialized", __FILE__, __LINE__, __FUNCTION__);

        fprintf(stdout, "[INFO] [System] Logger initialization complete\n");
        fflush(stdout);
    }
    catch (const std::exception& e) {
        fprintf(stderr, "[ERROR] [System] Exception in Logger::initializeWithoutMessageHandler: %s\n", e.what());
        fflush(stderr);

        // Make sure we're initialized even if there was an error
        QMutexLocker locker(&m_mutex);
        if (!m_initialized) {
            m_initialized = true;
        }
    }
    catch (...) {
        fprintf(stderr, "[ERROR] [System] Unknown exception in Logger::initializeWithoutMessageHandler\n");
        fflush(stderr);

        // Make sure we're initialized even if there was an error
        QMutexLocker locker(&m_mutex);
        if (!m_initialized) {
            m_initialized = true;
        }
    }
}

void Logger::shutdown() {
    QMutexLocker locker(&m_mutex);

    if (!m_initialized) {
        return;
    }

    log(LogLevel::Info, "System", "Logging system shutting down", __FILE__, __LINE__, __FUNCTION__);

    // Flush all sinks
    flush();

    // Remove all sinks
    m_sinks.clear();
    m_fileSink.reset();
    m_consoleSink.reset();

    // Restore default Qt message handler
    qInstallMessageHandler(nullptr);

    m_initialized = false;
}

void Logger::addSink(const std::shared_ptr<ILogSink>& sink) {
    QMutexLocker locker(&m_mutex);
    m_sinks.push_back(sink);
}

void Logger::removeSink(const std::shared_ptr<ILogSink>& sink) {
    QMutexLocker locker(&m_mutex);
    auto it = std::find(m_sinks.begin(), m_sinks.end(), sink);
    if (it != m_sinks.end()) {
        m_sinks.erase(it);
    }
}

void Logger::setGlobalLogLevel(LogLevel level) {
    // Delegate to LogConfig for the single source of truth
    LogConfig::instance().setGlobalLogLevel(level);
}

LogLevel Logger::globalLogLevel() const {
    // Delegate to LogConfig for the single source of truth
    return LogConfig::instance().globalLogLevel();
}

void Logger::setCategoryLogLevel(const QString& category, LogLevel level) {
    // Delegate to LogConfig for the single source of truth
    LogConfig::instance().setCategoryLogLevel(category, level);
}

LogLevel Logger::categoryLogLevel(const QString& category) const {
    // Delegate to LogConfig for the single source of truth
    return LogConfig::instance().categoryLogLevel(category);
}

void Logger::log(LogLevel level,
                const QString& category,
                const QString& message,
                const QString& file,
                int line,
                const QString& function) {
    try {
        // Check if we're initialized first to avoid potential issues
        if (!m_initialized) {
            // Fall back to fprintf if we're not initialized
            fprintf(stderr, "[%s] %s\n", qPrintable(category), qPrintable(message));
            return;
        }

        // Get the category log level from LogConfig
        LogLevel categoryLevel = LogConfig::instance().categoryLogLevel(category);

        // Check if the message should be logged based on the category log level
        if (level < categoryLevel) {
            return;
        }

        QDateTime timestamp = QDateTime::currentDateTime();
        Qt::HANDLE threadId = QThread::currentThreadId();

        // Make a copy of the sinks vector to avoid holding the mutex while writing
        std::vector<std::shared_ptr<ILogSink>> sinksCopy;
        {
            QMutexLocker locker(&m_mutex);
            sinksCopy = m_sinks;
        }

        // Write to all sinks without holding the mutex
        for (auto& sink : sinksCopy) {
            if (sink) {
                try {
                    sink->write(timestamp, level, category, message, file, line, function, threadId);
                }
                catch (const std::exception& e) {
                    fprintf(stderr, "Exception in sink->write: %s\n", e.what());
                }
                catch (...) {
                    fprintf(stderr, "Unknown exception in sink->write\n");
                }
            }
        }

        // Flush immediately for error and fatal messages
        if (level >= LogLevel::Error) {
            // Flush without holding the mutex
            for (auto& sink : sinksCopy) {
                if (sink) {
                    try {
                        sink->flush();
                    }
                    catch (const std::exception& e) {
                        fprintf(stderr, "Exception in sink->flush: %s\n", e.what());
                    }
                    catch (...) {
                        fprintf(stderr, "Unknown exception in sink->flush\n");
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        // Fall back to fprintf if there's an exception
        fprintf(stderr, "Exception in Logger::log: %s\nOriginal message: [%s] %s\n",
                e.what(), qPrintable(category), qPrintable(message));
    }
    catch (...) {
        // Fall back to fprintf if there's an unknown exception
        fprintf(stderr, "Unknown exception in Logger::log\nOriginal message: [%s] %s\n",
                qPrintable(category), qPrintable(message));
    }
}

void Logger::flush() {
    try {
        // Make a copy of the sinks vector to avoid holding the mutex while flushing
        std::vector<std::shared_ptr<ILogSink>> sinksCopy;
        {
            QMutexLocker locker(&m_mutex);
            sinksCopy = m_sinks;
        }

        // Flush all sinks without holding the mutex
        for (auto& sink : sinksCopy) {
            if (sink) {
                try {
                    sink->flush();
                }
                catch (const std::exception& e) {
                    fprintf(stderr, "Exception in sink->flush: %s\n", e.what());
                }
                catch (...) {
                    fprintf(stderr, "Unknown exception in sink->flush\n");
                }
            }
        }
    }
    catch (const std::exception& e) {
        fprintf(stderr, "Exception in Logger::flush: %s\n", e.what());
    }
    catch (...) {
        fprintf(stderr, "Unknown exception in Logger::flush\n");
    }
}

void Logger::loadConfiguration(const QString& filePath) {
    // Delegate to LogConfig for the single source of truth
    LogConfig::instance().loadConfiguration(filePath);
}

void Logger::saveConfiguration(const QString& filePath) {
    // Delegate to LogConfig for the single source of truth
    LogConfig::instance().saveConfiguration(filePath);
}

void Logger::setFileLoggingEnabled(bool enabled,
                                  const QString& filePath,
                                  qint64 maxSizeBytes,
                                  int maxBackupCount) {
    // Delegate to LogConfig for the single source of truth
    LogConfig::instance().setFileLoggingEnabled(enabled, filePath, maxSizeBytes, maxBackupCount);
}

void Logger::setConsoleLoggingEnabled(bool enabled) {
    // Delegate to LogConfig for the single source of truth
    LogConfig::instance().setConsoleLoggingEnabled(enabled);
}

bool Logger::isInitialized() const {
    QMutex& mutex = const_cast<QMutex&>(m_mutex);
    QMutexLocker locker(&mutex);
    return m_initialized;
}

void Logger::updateSinks() {
    try {
        fprintf(stdout, "Logger::updateSinks: Starting sink update\n");
        fflush(stdout);

        // Get configuration from LogConfig
        bool fileLoggingEnabled = LogConfig::instance().isFileLoggingEnabled();
        QString logFilePath = LogConfig::instance().logFilePath();
        qint64 maxSizeBytes = LogConfig::instance().maxLogFileSizeBytes();
        int maxBackupCount = LogConfig::instance().maxLogBackupCount();
        bool consoleLoggingEnabled = LogConfig::instance().isConsoleLoggingEnabled();

        fprintf(stdout, "Logger::updateSinks: Config - fileLogging=%d, consoleLogging=%d\n",
                fileLoggingEnabled ? 1 : 0, consoleLoggingEnabled ? 1 : 0);
        fflush(stdout);

        // Create new sinks outside the mutex lock
        std::shared_ptr<FileLogSink> newFileSink;
        std::shared_ptr<ConsoleLogSink> newConsoleSink;

        if (fileLoggingEnabled) {
            try {
                newFileSink = std::make_shared<FileLogSink>(logFilePath, maxSizeBytes, maxBackupCount);
                fprintf(stdout, "Logger::updateSinks: FileLogSink created successfully\n");
                fflush(stdout);
            } catch (const std::exception& e) {
                fprintf(stderr, "Exception creating FileLogSink: %s\n", e.what());
                fflush(stderr);
            }
        }

        if (consoleLoggingEnabled) {
            try {
                newConsoleSink = std::make_shared<ConsoleLogSink>();
                fprintf(stdout, "Logger::updateSinks: ConsoleLogSink created successfully\n");
                fflush(stdout);
            } catch (const std::exception& e) {
                fprintf(stderr, "Exception creating ConsoleLogSink: %s\n", e.what());
                fflush(stderr);
            }
        }

        // Now acquire the mutex to update the sinks list
        fprintf(stdout, "Logger::updateSinks: Acquiring mutex\n");
        fflush(stdout);

        std::shared_ptr<FileLogSink> oldFileSink;
        std::shared_ptr<ConsoleLogSink> oldConsoleSink;
        std::vector<std::shared_ptr<ILogSink>> updatedSinks;

        {
            QMutexLocker locker(&m_mutex);

            fprintf(stdout, "Logger::updateSinks: Mutex acquired\n");
            fflush(stdout);

            // Save the old sinks to be destroyed outside the mutex lock
            oldFileSink = m_fileSink;
            oldConsoleSink = m_consoleSink;

            // Create a new sinks list without the old sinks
            updatedSinks.reserve(m_sinks.size() +
                                (newFileSink ? 1 : 0) +
                                (newConsoleSink ? 1 : 0) -
                                (oldFileSink ? 1 : 0) -
                                (oldConsoleSink ? 1 : 0));

            for (const auto& sink : m_sinks) {
                if (sink != oldFileSink && sink != oldConsoleSink) {
                    updatedSinks.push_back(sink);
                }
            }

            // Add the new sinks
            if (newFileSink) {
                updatedSinks.push_back(newFileSink);
            }
            if (newConsoleSink) {
                updatedSinks.push_back(newConsoleSink);
            }

            // Update the sinks list and sink pointers
            m_sinks = updatedSinks;
            m_fileSink = newFileSink;
            m_consoleSink = newConsoleSink;

            fprintf(stdout, "Logger::updateSinks: Sinks updated\n");
            fflush(stdout);
        }

        // Reset the old sinks outside the mutex lock
        fprintf(stdout, "Logger::updateSinks: Releasing old sinks\n");
        fflush(stdout);

        oldFileSink.reset();
        oldConsoleSink.reset();

        fprintf(stdout, "Logger::updateSinks: Complete\n");
        fflush(stdout);
    } catch (const std::exception& e) {
        fprintf(stderr, "Exception in Logger::updateSinks: %s\n", e.what());
        fflush(stderr);
    } catch (...) {
        fprintf(stderr, "Unknown exception in Logger::updateSinks\n");
        fflush(stderr);
    }
}

} // namespace Utils
} // namespace Vizion3D
