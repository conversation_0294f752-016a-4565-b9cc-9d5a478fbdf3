#include "utils/result.h"

namespace Vizion3D::Utils {

// Error struct implementation
Error::Error(ErrorCode code, const QString& message, const QString& category,
             const QString& context, const char* file, int line, const char* function)
    : code(code), message(message),
      category(category.isEmpty() ? getCategoryForErrorCode(code) : category),
      context(context),
      file(QString::fromUtf8(file)), line(line),
      function(function ? QString::fromUtf8(function) : QString("unknown")) {
}

Error& Error::log(LogLevel level) {
    // Use default log level if Error level is specified (which means auto-determine)
    if (level == LogLevel::Error) {
        level = getDefaultLogLevel(code);
    }

    QString fullMessage = context.isEmpty() ? message : QString("%1: %2").arg(context, message);

    // For unit tests, we'll use a simple fallback instead of the full logging system
    #ifdef QT_TESTLIB_LIB
        // In test mode, just output to stderr for debugging without using Logger
        fprintf(stderr, "[%s] %s\n",
                errorCodeToString(code).toLocal8Bit().constData(),
                fullMessage.toLocal8Bit().constData());
        fflush(stderr);
    #else
        // In production, use the full logging system
        Logger::instance().log(level, category,
            QString("[%1] %2").arg(errorCodeToString(code), fullMessage),
            file, line, function);
    #endif

    return *this;
}

QString errorCodeToString(ErrorCode code) {
    switch (code) {
        // Success
        case ErrorCode::Success:
            return "Success";

        // G-code Processing Errors (1000-1999)
        case ErrorCode::GCodeParsingError:
            return "GCodeParsingError";
        case ErrorCode::GCodeSyntaxError:
            return "GCodeSyntaxError";
        case ErrorCode::GCodeFileNotFound:
            return "GCodeFileNotFound";
        case ErrorCode::GCodeInvalidCommand:
            return "GCodeInvalidCommand";
        case ErrorCode::GCodeInvalidCoordinate:
            return "GCodeInvalidCoordinate";
        case ErrorCode::GCodeUnsupportedFeature:
            return "GCodeUnsupportedFeature";
        case ErrorCode::GCodeInvalidFeedRate:
            return "GCodeInvalidFeedRate";
        case ErrorCode::GCodeInvalidToolNumber:
            return "GCodeInvalidToolNumber";
        case ErrorCode::GCodeInvalidSpindleSpeed:
            return "GCodeInvalidSpindleSpeed";
        case ErrorCode::GCodeMissingParameter:
            return "GCodeMissingParameter";

        // Simulation Engine Errors (2000-2999)
        case ErrorCode::SimulationNotInitialized:
            return "SimulationNotInitialized";
        case ErrorCode::SimulationAlreadyRunning:
            return "SimulationAlreadyRunning";
        case ErrorCode::SimulationInvalidState:
            return "SimulationInvalidState";
        case ErrorCode::SimulationNoGCode:
            return "SimulationNoGCode";
        case ErrorCode::SimulationThreadError:
            return "SimulationThreadError";
        case ErrorCode::SimulationStepError:
            return "SimulationStepError";
        case ErrorCode::SimulationTimeoutError:
            return "SimulationTimeoutError";
        case ErrorCode::SimulationCancelled:
            return "SimulationCancelled";

        // OpenGL Rendering Errors (3000-3999)
        case ErrorCode::OpenGLContextError:
            return "OpenGLContextError";
        case ErrorCode::OpenGLShaderCompileError:
            return "OpenGLShaderCompileError";
        case ErrorCode::OpenGLShaderLinkError:
            return "OpenGLShaderLinkError";
        case ErrorCode::OpenGLBufferError:
            return "OpenGLBufferError";
        case ErrorCode::OpenGLVAOError:
            return "OpenGLVAOError";
        case ErrorCode::OpenGLTextureError:
            return "OpenGLTextureError";
        case ErrorCode::OpenGLFramebufferError:
            return "OpenGLFramebufferError";
        case ErrorCode::OpenGLUniformError:
            return "OpenGLUniformError";

        // File I/O Errors (4000-4999)
        case ErrorCode::FileNotFound:
            return "FileNotFound";
        case ErrorCode::FilePermissionDenied:
            return "FilePermissionDenied";
        case ErrorCode::FileCorrupted:
            return "FileCorrupted";
        case ErrorCode::FileWriteError:
            return "FileWriteError";
        case ErrorCode::FileReadError:
            return "FileReadError";
        case ErrorCode::FileFormatError:
            return "FileFormatError";
        case ErrorCode::DirectoryNotFound:
            return "DirectoryNotFound";
        case ErrorCode::DirectoryCreationFailed:
            return "DirectoryCreationFailed";

        // Memory and Resource Errors (5000-5999)
        case ErrorCode::OutOfMemory:
            return "OutOfMemory";
        case ErrorCode::ResourceAllocationFailed:
            return "ResourceAllocationFailed";
        case ErrorCode::ResourceNotAvailable:
            return "ResourceNotAvailable";
        case ErrorCode::ResourceLimitExceeded:
            return "ResourceLimitExceeded";
        case ErrorCode::ResourceLeakDetected:
            return "ResourceLeakDetected";

        // Threading Errors (6000-6999)
        case ErrorCode::ThreadCreationFailed:
            return "ThreadCreationFailed";
        case ErrorCode::ThreadSynchronizationError:
            return "ThreadSynchronizationError";
        case ErrorCode::ThreadTimeoutError:
            return "ThreadTimeoutError";
        case ErrorCode::ThreadDeadlockDetected:
            return "ThreadDeadlockDetected";
        case ErrorCode::ThreadPoolExhausted:
            return "ThreadPoolExhausted";

        // Validation Errors (7000-7999)
        case ErrorCode::InvalidArgument:
            return "InvalidArgument";
        case ErrorCode::InvalidState:
            return "InvalidState";
        case ErrorCode::InvalidConfiguration:
            return "InvalidConfiguration";
        case ErrorCode::InvalidRange:
            return "InvalidRange";
        case ErrorCode::InvalidFormat:
            return "InvalidFormat";
        case ErrorCode::InvalidOperation:
            return "InvalidOperation";

        // System Errors (8000-8999)
        case ErrorCode::InternalError:
            return "InternalError";
        case ErrorCode::NotImplemented:
            return "NotImplemented";
        case ErrorCode::OperationCancelled:
            return "OperationCancelled";
        case ErrorCode::OperationTimeout:
            return "OperationTimeout";
        case ErrorCode::NetworkError:
            return "NetworkError";
        case ErrorCode::DatabaseError:
            return "DatabaseError";

        default:
            return QString("UnknownError(%1)").arg(static_cast<int>(code));
    }
}

LogLevel getDefaultLogLevel(ErrorCode code) {
    int codeValue = static_cast<int>(code);

    // Critical system errors that should be fatal
    if (code == ErrorCode::OutOfMemory ||
        code == ErrorCode::InternalError ||
        code == ErrorCode::ThreadDeadlockDetected) {
        return LogLevel::Fatal;
    }

    // File not found and some validation errors might be warnings in certain contexts
    if (code == ErrorCode::FileNotFound ||
        code == ErrorCode::DirectoryNotFound ||
        code == ErrorCode::GCodeUnsupportedFeature ||
        code == ErrorCode::NotImplemented ||
        code == ErrorCode::OperationCancelled) {
        return LogLevel::Warning;
    }

    // Most errors are standard Error level
    return LogLevel::Error;
}

QString getCategoryForErrorCode(ErrorCode code) {
    int codeValue = static_cast<int>(code);

    if (codeValue >= 1000 && codeValue < 2000) {
        return "GCode";
    } else if (codeValue >= 2000 && codeValue < 3000) {
        return "Engine";
    } else if (codeValue >= 3000 && codeValue < 4000) {
        return "OpenGL";
    } else if (codeValue >= 4000 && codeValue < 5000) {
        return "FileIO";
    } else if (codeValue >= 5000 && codeValue < 6000) {
        return "Memory";
    } else if (codeValue >= 6000 && codeValue < 7000) {
        return "Threading";
    } else if (codeValue >= 7000 && codeValue < 8000) {
        return "Validation";
    } else if (codeValue >= 8000 && codeValue < 9000) {
        return "System";
    } else {
        return "Unknown";
    }
}

} // namespace Vizion3D::Utils
