#include "ui/mainwindow.h"
#include "utils/logger.h"
#include "utils/log_config.h"
#include "utils/debug_config.h"

// Include OpenCASCADE headers if available
#ifdef USE_OPENCASCADE
#include <Standard_Version.hxx>
#endif

#include <QApplication>
#include <QSurfaceFormat>
#include <QCommandLineParser>
#include <QDebug>
#include <QStandardPaths>
#include <QDir>
#include <QOpenGLContext>
#include <QOpenGLFunctions>
#include <QOffscreenSurface>
#include <QMessageBox>
#include <QDateTime>
#include <QElapsedTimer>
#include <QAtomicInt>

/**
 * @brief Sets up the OpenGL format for the application.
 *
 * This function configures the OpenGL format to use the core profile,
 * set the version to 4.1 (maximum supported on macOS), and enable various features
 * like depth buffer, stencil buffer, and multisampling.
 */
void setupOpenGLFormat()
{
    QSurfaceFormat format;

    // Use OpenGL 4.1 which is supported on macOS and Windows
    // This is the standardized version for Vizion3D
    format.setVersion(4, 1);

    // Use core profile for modern OpenGL features
    format.setProfile(QSurfaceFormat::CoreProfile);

    format.setDepthBufferSize(24);
    format.setStencilBufferSize(8);
    format.setSamples(4);  // Multisampling
    format.setSwapBehavior(QSurfaceFormat::DoubleBuffer);

    // Print OpenGL version information for debugging
    VLOG_DEBUG("System", QString("Setting up OpenGL format: %1.%2%3")
               .arg(format.version().first)
               .arg(format.version().second)
               .arg(format.profile() == QSurfaceFormat::CoreProfile ? " Core" : " Compatibility"));

    QSurfaceFormat::setDefaultFormat(format);
}

/**
 * @brief Checks if the system supports OpenGL 4.1 Core Profile.
 *
 * This function creates a temporary OpenGL context and checks if the system
 * supports OpenGL 4.1 Core Profile, which is required for Vizion3D.
 *
 * @return True if OpenGL 4.1 Core Profile is supported, false otherwise.
 */
bool checkOpenGLVersion()
{
    try {
        // Create a temporary surface to host an OpenGL context
        QOffscreenSurface surface;
        surface.create();

        // Create an OpenGL context with the default format
        QOpenGLContext context;
        if (!context.create()) {
            VLOG_ERROR("System", "Failed to create OpenGL context");
            return false;
        }

        // Make the context current on the surface
        if (!context.makeCurrent(&surface)) {
            VLOG_ERROR("System", "Failed to make OpenGL context current");
            return false;
        }

        // Get OpenGL version
        QOpenGLFunctions* f = context.functions();
        if (!f) {
            VLOG_ERROR("System", "Failed to get OpenGL functions");
            return false;
        }

        QString glVersion = reinterpret_cast<const char*>(f->glGetString(GL_VERSION));
        QString glRenderer = reinterpret_cast<const char*>(f->glGetString(GL_RENDERER));
        QString glVendor = reinterpret_cast<const char*>(f->glGetString(GL_VENDOR));

        // Log OpenGL information
        VLOG_DEBUG("System", QString("OpenGL Version: %1").arg(glVersion));
        VLOG_DEBUG("System", QString("OpenGL Renderer: %1").arg(glRenderer));
        VLOG_DEBUG("System", QString("OpenGL Vendor: %1").arg(glVendor));

        // Check if the context format matches our requirements
        QSurfaceFormat format = context.format();
        int major = format.majorVersion();
        int minor = format.minorVersion();
        bool isCore = (format.profile() == QSurfaceFormat::CoreProfile);

        VLOG_DEBUG("System", QString("Actual OpenGL version: %1.%2%3")
                   .arg(major)
                   .arg(minor)
                   .arg(isCore ? " Core" : " Compatibility"));

        // Release the context
        context.doneCurrent();

        // Check if the version is at least 4.1 and is Core profile
        if (major < 4 || (major == 4 && minor < 1) || !isCore) {
            VLOG_ERROR("System", QString("OpenGL 4.1 Core Profile is required, but the system only supports %1.%2 %3")
                       .arg(major).arg(minor).arg(isCore ? "Core" : "Compatibility"));
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        VLOG_ERROR("System", QString("Exception in checkOpenGLVersion: %1").arg(e.what()));
        return false;
    } catch (...) {
        VLOG_ERROR("System", "Unknown exception in checkOpenGLVersion");
        return false;
    }
}

/**
 * @brief Logs a diagnostic message with a timestamp and step number
 *
 * @param step The step number in the initialization sequence
 * @param message The message to display
 * @param elapsedTimer Timer to track elapsed time since application start
 */
void logDiagnosticMessage(int step, const QString& message, const QElapsedTimer& elapsedTimer) {
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    QString elapsedMs = QString::number(elapsedTimer.elapsed());
    QString fullMessage = QString("[%1] Step %2 (%3 ms): %4")
                              .arg(timestamp)
                              .arg(step)
                              .arg(elapsedMs)
                              .arg(message);

    VLOG_DEBUG("System", QString("DIAGNOSTIC: %1").arg(fullMessage));
}

/**
 * @brief Checks if OpenCASCADE 7.8.0 is available.
 *
 * This function checks if OpenCASCADE is available and if the version is 7.8.0,
 * which is the standardized version for Vizion3D.
 *
 * @return True if OpenCASCADE 7.8.0 is available, false otherwise.
 */
bool checkOpenCASCADEVersion()
{
#ifdef USE_OPENCASCADE
    // Log OpenCASCADE version information
    VLOG_DEBUG("System", QString("OpenCASCADE Version: %1").arg(OCC_VERSION_COMPLETE));

    // Check if the version is 7.8.0
    if (OCC_VERSION_MAJOR != 7 || OCC_VERSION_MINOR != 8 || OCC_VERSION_MAINTENANCE != 0) {
        VLOG_WARNING("System", "OpenCASCADE 7.8.0 is required, but version")
                  << OCC_VERSION_MAJOR << "." << OCC_VERSION_MINOR << "." << OCC_VERSION_MAINTENANCE
                  << "is installed. Material removal simulation may not work correctly.";
        return false;
    }

    return true;
#else
    VLOG_WARNING("System", "OpenCASCADE is not available. Material removal simulation will be disabled.");
    return false;
#endif
}

/**
 * @brief The main entry point for the application.
 *
 * @param argc The number of command-line arguments.
 * @param argv The command-line arguments.
 * @return The application exit code.
 */
int main(int argc, char *argv[])
{
    // Start a timer to track elapsed time
    QElapsedTimer elapsedTimer;
    elapsedTimer.start();

    try {
        // Set application information
        QCoreApplication::setApplicationName("Vizion3D");
        QCoreApplication::setOrganizationName("Vizion3D");
        QCoreApplication::setApplicationVersion("1.0.0");

        // Create application
        QApplication app(argc, argv);

        // Log diagnostic message after creating QApplication to confirm the application is starting
        logDiagnosticMessage(1, "Application starting...", elapsedTimer);

        // Initialize logging system
        logDiagnosticMessage(2, "About to initialize logging system...", elapsedTimer);

        try {
            // IMPORTANT: We'll install the Qt message handler AFTER all logging initialization is complete
            // to prevent potential deadlocks during initialization

            // Use fprintf for diagnostic output to avoid potential issues with qDebug
            VLOG_INFO("System", "Starting logging system initialization...");
            fflush(stdout);

            // Initialize Logger with console logging only (disable file logging)
            VLOG_INFO("System", "Getting Logger instance...");
            fflush(stdout);

            Vizion3D::Utils::Logger& logger = Vizion3D::Utils::Logger::instance();

            VLOG_INFO("System", "Initializing Logger WITHOUT Qt message handler...");
            fflush(stdout);

            // Set a timeout for logger initialization to avoid hanging indefinitely
            QElapsedTimer initTimer;
            initTimer.start();

            // Create a separate thread to monitor the initialization process
            QThread* monitorThread = new QThread();
            monitorThread->start();

            // Connect a lambda to the thread to monitor initialization
            QObject::connect(monitorThread, &QThread::started, [&]() {
                while (initTimer.elapsed() < 5000) { // 5 second timeout
                    QThread::msleep(100);
                    if (logger.isInitialized()) {
                        VLOG_INFO("System", "Logger initialized successfully (detected by monitor thread)");
                        fflush(stdout);
                        break;
                    }
                }

                if (!logger.isInitialized() && initTimer.elapsed() >= 5000) {
                    VLOG_WARNING("System", "Logger initialization timeout (5 seconds)");
                    fflush(stderr);
                }

                monitorThread->quit();
            });

            // Temporarily disable Qt message handler installation
            logger.initializeWithoutMessageHandler("Vizion3D");

            VLOG_INFO("System", "Logger initialized without message handler");
            fflush(stdout);

            // Wait for the monitor thread to finish
            monitorThread->wait();
            delete monitorThread;

            // Disable file logging to avoid potential file access issues
            VLOG_INFO("System", "Disabling file logging...");
            fflush(stdout);

            // Set a timeout for disabling file logging to avoid hanging indefinitely
            QElapsedTimer fileLogTimer;
            fileLogTimer.start();

            // Create a separate thread to monitor the file logging operation
            QThread* fileLogMonitorThread = new QThread();
            QAtomicInt operationCompleted(0); // Flag to indicate operation completion
            fileLogMonitorThread->start();

            // Connect a lambda to the thread to monitor the operation
            QObject::connect(fileLogMonitorThread, &QThread::started, [&]() {
                int lastReportTime = 0;
                while (fileLogTimer.elapsed() < 5000 && operationCompleted.loadAcquire() == 0) { // 5 second timeout
                    QThread::msleep(50); // Check more frequently but report less often

                    // Only report every 500ms to reduce spam
                    int currentTime = fileLogTimer.elapsed();
                    if (currentTime - lastReportTime >= 500) {
                        VLOG_INFO("System", QString("File logging disable operation in progress... (%1 ms elapsed)").arg(currentTime));
                        fflush(stdout);
                        lastReportTime = currentTime;
                    }
                }

                if (fileLogTimer.elapsed() >= 5000 && operationCompleted.loadAcquire() == 0) {
                    VLOG_WARNING("System", "File logging disable operation timeout (5 seconds)");
                    fflush(stderr);
                }

                fileLogMonitorThread->quit();
            });

            try {
                logger.setFileLoggingEnabled(false);
                operationCompleted.storeRelease(1); // Signal operation completion
                VLOG_INFO("System", "File logging disabled successfully");
                fflush(stdout);
            } catch (const std::exception& e) {
                operationCompleted.storeRelease(1); // Signal operation completion even on error
                VLOG_ERROR("System", QString("Exception disabling file logging: %1").arg(e.what()));
                fflush(stderr);
            } catch (...) {
                operationCompleted.storeRelease(1); // Signal operation completion even on error
                VLOG_ERROR("System", "Unknown exception disabling file logging");
                fflush(stderr);
            }

            // Wait for the monitor thread to finish
            fileLogMonitorThread->wait();
            delete fileLogMonitorThread;

            VLOG_INFO("System", QString("File logging disable operation completed in %1 ms").arg(fileLogTimer.elapsed()));
            fflush(stdout);

            // Initialize LogConfig
            VLOG_INFO("System", "Getting LogConfig instance...");
            fflush(stdout);

            Vizion3D::Utils::LogConfig& logConfig = Vizion3D::Utils::LogConfig::instance();

            fprintf(stdout, "Initializing LogConfig...\n");
            fflush(stdout);

            logConfig.initialize("Vizion3D");

            fprintf(stdout, "LogConfig initialized\n");
            fflush(stdout);

            // Note: File logging is now disabled by default in LogConfig constructor
            // No need to explicitly disable it here

            // Set default log levels for different components
            fprintf(stdout, "Setting category log levels...\n");
            fflush(stdout);

            logConfig.setCategoryLogLevel("UI", Vizion3D::Utils::LogLevel::Info);
            logConfig.setCategoryLogLevel("Engine", Vizion3D::Utils::LogLevel::Info);
            logConfig.setCategoryLogLevel("GCode", Vizion3D::Utils::LogLevel::Info);
            logConfig.setCategoryLogLevel("OpenGL", Vizion3D::Utils::LogLevel::Info);
            logConfig.setCategoryLogLevel("System", Vizion3D::Utils::LogLevel::Info);

            fprintf(stdout, "Category log levels set\n");
            fflush(stdout);

            // NOW install the Qt message handler AFTER all logging initialization is complete
            fprintf(stdout, "Installing Qt message handler...\n");
            fflush(stdout);

            qInstallMessageHandler(Vizion3D::Utils::qtMessageHandler);

            fprintf(stdout, "Qt message handler installed\n");
            fflush(stdout);

            // Now that everything is set up, we can use qDebug again
            VLOG_DEBUG("System", "Logging system fully initialized");
        } catch (const std::exception& e) {
            QString errorMsg = QString("Exception during logging initialization: %1").arg(e.what());
            VLOG_ERROR("System", QString("ERROR: %1").arg(errorMsg));
            fflush(stderr);
            QMessageBox::critical(nullptr, "Logging Error", errorMsg);
        } catch (...) {
            QString errorMsg = "Unknown exception during logging initialization";
            VLOG_ERROR("System", QString("ERROR: %1").arg(errorMsg));
            fflush(stderr);
            QMessageBox::critical(nullptr, "Logging Error", errorMsg);
        }

        logDiagnosticMessage(3, "Logging system initialized", elapsedTimer);

        // Log application startup using qDebug instead of VLOG_INFO to avoid potential issues
        VLOG_DEBUG("System", QString("Vizion3D v%1 starting up").arg(QCoreApplication::applicationVersion()));

        // Set up OpenGL format
        logDiagnosticMessage(4, "About to set up OpenGL format...", elapsedTimer);
        try {
            VLOG_DEBUG("System", "Setting up OpenGL format...");
            setupOpenGLFormat();
            VLOG_DEBUG("System", "OpenGL format set up successfully");
        } catch (const std::exception& e) {
            QString errorMsg = QString("Exception during OpenGL format setup: %1").arg(e.what());
            VLOG_FATAL("System", errorMsg);
            QMessageBox::critical(nullptr, "OpenGL Error", errorMsg);
        } catch (...) {
            QString errorMsg = "Unknown exception during OpenGL format setup";
            VLOG_FATAL("System", errorMsg);
            QMessageBox::critical(nullptr, "OpenGL Error", errorMsg);
        }
        logDiagnosticMessage(5, "OpenGL format set up", elapsedTimer);

        // Check OpenGL version
        logDiagnosticMessage(6, "About to check OpenGL version...", elapsedTimer);
        VLOG_INFO("System", "Checking OpenGL version...");
        VLOG_DEBUG("System", "Checking OpenGL version...");
        bool openGLVersionOK = false;
        try {
            openGLVersionOK = checkOpenGLVersion();
        } catch (const std::exception& e) {
            QString errorMsg = QString("Exception during OpenGL version check: %1").arg(e.what());
            VLOG_FATAL("System", errorMsg);
            QMessageBox::critical(nullptr, "OpenGL Error", errorMsg);
        } catch (...) {
            QString errorMsg = "Unknown exception during OpenGL version check";
            VLOG_FATAL("System", errorMsg);
            QMessageBox::critical(nullptr, "OpenGL Error", errorMsg);
        }

        if (!openGLVersionOK) {
            VLOG_ERROR("System", "OpenGL 4.1 Core Profile is required but not available on this system.");
            QMessageBox::critical(nullptr, "OpenGL Error",
                                "Vizion3D requires OpenGL 4.1 Core Profile, but it is not available on your system.\n\n"
                                "Please update your graphics drivers or use a system with OpenGL 4.1 support.");
            return 1;
        }
        VLOG_INFO("System", "OpenGL 4.1 Core Profile is available.");
        logDiagnosticMessage(7, "OpenGL 4.1 Core Profile is available", elapsedTimer);

        // Check OpenCASCADE version
        logDiagnosticMessage(8, "About to check OpenCASCADE version...", elapsedTimer);
        VLOG_INFO("System", "Checking OpenCASCADE version...");
        VLOG_DEBUG("System", "Checking OpenCASCADE version...");
        bool openCascadeAvailable = false;
        try {
            openCascadeAvailable = checkOpenCASCADEVersion();
        } catch (const std::exception& e) {
            QString errorMsg = QString("Exception during OpenCASCADE version check: %1").arg(e.what());
            VLOG_FATAL("System", errorMsg);
            QMessageBox::critical(nullptr, "OpenCASCADE Error", errorMsg);
        } catch (...) {
            QString errorMsg = "Unknown exception during OpenCASCADE version check";
            VLOG_FATAL("System", errorMsg);
            QMessageBox::critical(nullptr, "OpenCASCADE Error", errorMsg);
        }

        if (!openCascadeAvailable) {
            VLOG_WARNING("System", "OpenCASCADE 7.8.0 is not available. Material removal simulation will be disabled.");
        } else {
            VLOG_INFO("System", "OpenCASCADE 7.8.0 is available.");
        }
        logDiagnosticMessage(9, openCascadeAvailable ?
                           "OpenCASCADE 7.8.0 is available" :
                           "OpenCASCADE 7.8.0 is not available", elapsedTimer);

        // Parse command-line arguments
        logDiagnosticMessage(10, "About to parse command-line arguments...", elapsedTimer);
        QCommandLineParser parser;
        parser.setApplicationDescription("Vizion3D - CNC Simulation");
        parser.addHelpOption();
        parser.addVersionOption();

        // Add custom command-line options
        QCommandLineOption fileOption(QStringList() << "f" << "file", "Open G-code file", "file");
        parser.addOption(fileOption);

        // Add logging-related command-line options
        QCommandLineOption logLevelOption(QStringList() << "log-level", "Set global log level (trace, debug, info, warning, error, fatal)", "level", "info");
        parser.addOption(logLevelOption);

        QCommandLineOption logFileOption(QStringList() << "log-file", "Set log file path", "path");
        parser.addOption(logFileOption);

        QCommandLineOption noConsoleLogOption(QStringList() << "no-console-log", "Disable console logging");
        parser.addOption(noConsoleLogOption);

        VLOG_DEBUG("System", "Processing command-line arguments...");
        parser.process(app);
        VLOG_DEBUG("System", "Command-line arguments processed");

        // Apply command-line logging options
        VLOG_DEBUG("System", "Applying command-line logging options...");
        if (parser.isSet(logLevelOption)) {
            QString levelStr = parser.value(logLevelOption);
            Vizion3D::Utils::LogLevel level = Vizion3D::Utils::stringToLogLevel(levelStr);
            Vizion3D::Utils::LogConfig::instance().setGlobalLogLevel(level);
            VLOG_INFO("System", QString("Global log level set to %1").arg(Vizion3D::Utils::logLevelToString(level)));
        }

        if (parser.isSet(logFileOption)) {
            QString logFilePath = parser.value(logFileOption);
            Vizion3D::Utils::LogConfig::instance().setFileLoggingEnabled(true, logFilePath);
            VLOG_INFO("System", QString("Log file set to %1").arg(logFilePath));
        }

        if (parser.isSet(noConsoleLogOption)) {
            Vizion3D::Utils::LogConfig::instance().setConsoleLoggingEnabled(false);
            // No log message here since console logging is disabled
        }
        VLOG_DEBUG("System", "Command-line logging options applied");
        logDiagnosticMessage(11, "Command-line arguments processed", elapsedTimer);

        // Create main window
        logDiagnosticMessage(12, "About to create main window...", elapsedTimer);
        VLOG_INFO("UI", "Creating main window");
        VLOG_DEBUG("System", "Creating main window");
        MainWindow* mainWindow = nullptr;

        try {
            // Create MainWindow on the heap so it stays in scope for the entire application lifetime
            VLOG_DEBUG("System", "About to create MainWindow");
            mainWindow = new MainWindow();
            VLOG_DEBUG("System", "MainWindow created successfully");
            VLOG_INFO("UI", "MainWindow created successfully, showing window");
            logDiagnosticMessage(13, "MainWindow created successfully", elapsedTimer);

            // Force processing of events before showing the window
            VLOG_DEBUG("System", "Processing events before showing window...");
            QCoreApplication::processEvents();
            VLOG_DEBUG("System", "Events processed before showing window");

            VLOG_DEBUG("System", "About to show MainWindow");
            logDiagnosticMessage(14, "About to show MainWindow", elapsedTimer);
            mainWindow->show();
            VLOG_DEBUG("System", "MainWindow show() called");
            VLOG_INFO("UI", "MainWindow show() called");

            // Force processing of events after showing the window
            VLOG_DEBUG("System", "Processing events after showing window...");
            QCoreApplication::processEvents();
            VLOG_DEBUG("System", "Events processed after show()");
            logDiagnosticMessage(15, "MainWindow shown and events processed", elapsedTimer);

            // If a file was specified on the command line, open it
            if (parser.isSet(fileOption)) {
                QString filePath = parser.value(fileOption);
                VLOG_INFO("System", QString("Opening file: %1").arg(filePath));
                logDiagnosticMessage(16, QString("Opening file: %1").arg(filePath), elapsedTimer);
                // TODO: Open the file
            }
        } catch (const std::exception& e) {
            QString errorMsg = QString("Exception creating MainWindow: %1").arg(e.what());
            VLOG_DEBUG("System", errorMsg);
            VLOG_FATAL("UI", errorMsg);
            QMessageBox::critical(nullptr, "Fatal Error", errorMsg);
            delete mainWindow; // Clean up if we failed
            return 1;
        } catch (...) {
            QString errorMsg = "Unknown exception creating MainWindow";
            VLOG_DEBUG("System", errorMsg);
            VLOG_FATAL("UI", errorMsg);
            QMessageBox::critical(nullptr, "Fatal Error", errorMsg);
            delete mainWindow; // Clean up if we failed
            return 1;
        }

        // Run the application
        logDiagnosticMessage(17, "About to enter main event loop", elapsedTimer);
        VLOG_INFO("System", "Entering main event loop");
        VLOG_DEBUG("System", "Entering main event loop");
        int result = app.exec();

        // Shutdown logging system
        VLOG_DEBUG("System", QString("Application exiting with code%1").arg(result));
        VLOG_INFO("System", QString("Vizion3D exiting with code %1").arg(result));
        VLOG_DEBUG("System", "Shutting down logging system...");
        Vizion3D::Utils::Logger::instance().shutdown();
        VLOG_DEBUG("System", "Logging system shut down");

        return result;
    } catch (const std::exception& e) {
        QString errorMsg = QString("Exception in main function: %1").arg(e.what());
        VLOG_FATAL("System", errorMsg);
        QMessageBox::critical(nullptr, "Fatal Error", errorMsg);
        return 1;
    } catch (...) {
        QString errorMsg = "Unknown exception in main function";
        VLOG_FATAL("System", errorMsg);
        QMessageBox::critical(nullptr, "Fatal Error", errorMsg);
        return 1;
    }
}
