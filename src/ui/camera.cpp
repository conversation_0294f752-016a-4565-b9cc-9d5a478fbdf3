#include "ui/camera.h"
#include "utils/logger.h"
#include <QDebug>
#include <cmath>

namespace Vizion3D {
namespace UI {

Camera::Camera()
    : m_position(-50.0f, 75.0f, 75.0f)
    , m_target(12.5f, 0.0f, 12.5f)
    , m_up(0.0f, 1.0f, 0.0f)
    , m_fov(60.0f)
    , m_nearPlane(0.1f)
    , m_farPlane(1000.0f)
{
    VLOG_DEBUG("UI", QString("Camera created with position: %1 %2 %3 target: %4 %5 %6 up: %7 %8 %9 FOV: %10")
               .arg(m_position.x()).arg(m_position.y()).arg(m_position.z())
               .arg(m_target.x()).arg(m_target.y()).arg(m_target.z())
               .arg(m_up.x()).arg(m_up.y()).arg(m_up.z())
               .arg(m_fov));
}

Camera::Camera(const QVector3D& position, const QVector3D& target, const QVector3D& up)
    : m_position(position)
    , m_target(target)
    , m_up(up)
    , m_fov(60.0f)
    , m_nearPlane(0.1f)
    , m_farPlane(1000.0f)
{
    VLOG_DEBUG("UI", QString("Camera created with position: %1 %2 %3 target: %4 %5 %6 up: %7 %8 %9 FOV: %10")
               .arg(m_position.x()).arg(m_position.y()).arg(m_position.z())
               .arg(m_target.x()).arg(m_target.y()).arg(m_target.z())
               .arg(m_up.x()).arg(m_up.y()).arg(m_up.z())
               .arg(m_fov));
}

QVector3D Camera::position() const
{
    return m_position;
}

void Camera::setPosition(const QVector3D& position)
{
    m_position = position;
}

QVector3D Camera::target() const
{
    return m_target;
}

void Camera::setTarget(const QVector3D& target)
{
    m_target = target;
}

QVector3D Camera::up() const
{
    return m_up;
}

void Camera::setUp(const QVector3D& up)
{
    m_up = up;
}

float Camera::fov() const
{
    return m_fov;
}

void Camera::setFov(float fov)
{
    m_fov = fov;
}

float Camera::nearPlane() const
{
    return m_nearPlane;
}

void Camera::setNearPlane(float nearPlane)
{
    m_nearPlane = nearPlane;
}

float Camera::farPlane() const
{
    return m_farPlane;
}

void Camera::setFarPlane(float farPlane)
{
    m_farPlane = farPlane;
}

void Camera::orbit(float deltaX, float deltaY)
{
    // Rotate around Y axis (horizontal orbit)
    QMatrix4x4 rotationY;
    rotationY.setToIdentity();
    rotationY.rotate(deltaX, 0, 1, 0);
    m_position = rotationY.map(m_position - m_target) + m_target;

    // Rotate around X axis (vertical orbit)
    QVector3D right = QVector3D::crossProduct(m_up, m_position - m_target).normalized();
    QMatrix4x4 rotationX;
    rotationX.setToIdentity();
    rotationX.rotate(deltaY, right.x(), right.y(), right.z());
    m_position = rotationX.map(m_position - m_target) + m_target;

    VLOG_DEBUG("UI", QString("Camera orbited to position: %1 %2 %3").arg(m_position.x()).arg(m_position.y()).arg(m_position.z()));
}

void Camera::pan(float deltaX, float deltaY)
{
    // Calculate pan vectors
    QVector3D forward = (m_target - m_position).normalized();
    QVector3D right = QVector3D::crossProduct(forward, m_up).normalized();
    QVector3D up = QVector3D::crossProduct(right, forward).normalized();

    // Pan camera and target
    QVector3D pan = right * deltaX + up * deltaY;
    m_position += pan;
    m_target += pan;

    VLOG_DEBUG("UI", QString("Camera panned to position: %1 %2 %3 target: %4 %5 %6")
               .arg(m_position.x()).arg(m_position.y()).arg(m_position.z())
               .arg(m_target.x()).arg(m_target.y()).arg(m_target.z()));
}

void Camera::zoom(float factor)
{
    // Calculate new camera position
    QVector3D forward = (m_target - m_position).normalized();
    m_position = m_position + forward * (1.0f - factor) * (m_position - m_target).length();

    VLOG_DEBUG("UI", QString("Camera zoomed to position: %1 %2 %3").arg(m_position.x()).arg(m_position.y()).arg(m_position.z()));
}

void Camera::setTopView(const QVector3D& center)
{
    m_target = center;
    m_position = QVector3D(center.x(), center.y() + 100.0f, center.z());
    m_up = QVector3D(0, 0, -1);

    VLOG_DEBUG("UI", QString("Camera set to top view with position: %1 %2 %3 target: %4 %5 %6 up: %7 %8 %9")
               .arg(m_position.x()).arg(m_position.y()).arg(m_position.z())
               .arg(m_target.x()).arg(m_target.y()).arg(m_target.z())
               .arg(m_up.x()).arg(m_up.y()).arg(m_up.z()));
}

void Camera::setFrontView(const QVector3D& center)
{
    m_target = center;
    m_position = QVector3D(center.x(), center.y(), center.z() + 100.0f);
    m_up = QVector3D(0, 1, 0);

    VLOG_DEBUG("UI", QString("Camera set to front view with position: %1 %2 %3 target: %4 %5 %6 up: %7 %8 %9")
               .arg(m_position.x()).arg(m_position.y()).arg(m_position.z())
               .arg(m_target.x()).arg(m_target.y()).arg(m_target.z())
               .arg(m_up.x()).arg(m_up.y()).arg(m_up.z()));
}

void Camera::setSideView(const QVector3D& center)
{
    m_target = center;
    m_position = QVector3D(center.x() + 100.0f, center.y(), center.z());
    m_up = QVector3D(0, 1, 0);

    VLOG_DEBUG("UI", QString("Camera set to side view with position: %1 %2 %3 target: %4 %5 %6 up: %7 %8 %9")
               .arg(m_position.x()).arg(m_position.y()).arg(m_position.z())
               .arg(m_target.x()).arg(m_target.y()).arg(m_target.z())
               .arg(m_up.x()).arg(m_up.y()).arg(m_up.z()));
}

void Camera::setIsometricView(const QVector3D& center)
{
    m_target = center;
    float distance = 75.0f;
    m_position = QVector3D(
        center.x() + distance,
        center.y() + distance,
        center.z() + distance
    );
    m_up = QVector3D(0, 1, 0);

    VLOG_DEBUG("UI", QString("Camera set to isometric view with position: %1 %2 %3 target: %4 %5 %6 up: %7 %8 %9")
               .arg(m_position.x()).arg(m_position.y()).arg(m_position.z())
               .arg(m_target.x()).arg(m_target.y()).arg(m_target.z())
               .arg(m_up.x()).arg(m_up.y()).arg(m_up.z()));
}

void Camera::reset(const QVector3D& center)
{
    m_target = center;
    m_position = QVector3D(center.x(), center.y() + 50.0f, center.z() + 100.0f);
    m_up = QVector3D(0, 1, 0);
    m_fov = 45.0f;

    VLOG_DEBUG("UI", QString("Camera reset with position: %1 %2 %3 target: %4 %5 %6 up: %7 %8 %9 FOV: %10")
               .arg(m_position.x()).arg(m_position.y()).arg(m_position.z())
               .arg(m_target.x()).arg(m_target.y()).arg(m_target.z())
               .arg(m_up.x()).arg(m_up.y()).arg(m_up.z())
               .arg(m_fov));
}

QMatrix4x4 Camera::viewMatrix() const
{
    QMatrix4x4 view;
    view.setToIdentity();
    view.lookAt(m_position, m_target, m_up);
    return view;
}

QMatrix4x4 Camera::projectionMatrix(float aspect) const
{
    QMatrix4x4 projection;
    projection.setToIdentity();
    projection.perspective(m_fov, aspect, m_nearPlane, m_farPlane);
    return projection;
}

} // namespace UI
} // namespace Vizion3D
