#include "ui/log_settings_dialog.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QHeaderView>
#include <QFileDialog>
#include <QMessageBox>
#include <QInputDialog>
#include <QStandardPaths>
#include <QDir>

namespace Vizion3D {
namespace UI {

LogSettingsDialog::LogSettingsDialog(QWidget* parent)
    : QDialog(parent) {
    setWindowTitle(tr("Logging Settings"));
    setMinimumSize(600, 400);

    initializeUi();
    loadConfiguration();
}

LogSettingsDialog::~LogSettingsDialog() {
}

void LogSettingsDialog::initializeUi() {
    QVBoxLayout* mainLayout = new QVBoxLayout(this);

    // Global log level
    QGroupBox* globalLevelGroup = new QGroupBox(tr("Global Log Level"));
    QHBoxLayout* globalLevelLayout = new QHBoxLayout(globalLevelGroup);

    m_globalLevelCombo = new QComboBox();
    m_globalLevelCombo->addItem(tr("Trace"), static_cast<int>(Utils::LogLevel::Trace));
    m_globalLevelCombo->addItem(tr("Debug"), static_cast<int>(Utils::LogLevel::Debug));
    m_globalLevelCombo->addItem(tr("Info"), static_cast<int>(Utils::LogLevel::Info));
    m_globalLevelCombo->addItem(tr("Warning"), static_cast<int>(Utils::LogLevel::Warning));
    m_globalLevelCombo->addItem(tr("Error"), static_cast<int>(Utils::LogLevel::Error));
    m_globalLevelCombo->addItem(tr("Fatal"), static_cast<int>(Utils::LogLevel::Fatal));

    globalLevelLayout->addWidget(m_globalLevelCombo);
    mainLayout->addWidget(globalLevelGroup);

    // Category log levels
    QGroupBox* categoryGroup = new QGroupBox(tr("Category Log Levels"));
    QVBoxLayout* categoryLayout = new QVBoxLayout(categoryGroup);

    m_categoryTable = new QTableWidget();
    m_categoryTable->setColumnCount(2);
    m_categoryTable->setHorizontalHeaderLabels(QStringList() << tr("Category") << tr("Log Level"));
    m_categoryTable->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Stretch);
    m_categoryTable->horizontalHeader()->setSectionResizeMode(1, QHeaderView::ResizeToContents);
    m_categoryTable->verticalHeader()->setVisible(false);
    m_categoryTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_categoryTable->setSelectionMode(QAbstractItemView::SingleSelection);

    QHBoxLayout* categoryButtonLayout = new QHBoxLayout();
    m_addCategoryButton = new QPushButton(tr("Add Category"));
    m_removeCategoryButton = new QPushButton(tr("Remove Category"));
    categoryButtonLayout->addWidget(m_addCategoryButton);
    categoryButtonLayout->addWidget(m_removeCategoryButton);
    categoryButtonLayout->addStretch();

    categoryLayout->addWidget(m_categoryTable);
    categoryLayout->addLayout(categoryButtonLayout);

    mainLayout->addWidget(categoryGroup);

    // File logging settings
    QGroupBox* fileLoggingGroup = new QGroupBox(tr("File Logging"));
    QGridLayout* fileLoggingLayout = new QGridLayout(fileLoggingGroup);

    m_fileLoggingEnabledCheck = new QCheckBox(tr("Enable File Logging"));
    fileLoggingLayout->addWidget(m_fileLoggingEnabledCheck, 0, 0, 1, 3);

    fileLoggingLayout->addWidget(new QLabel(tr("Log File Path:")), 1, 0);
    m_logFilePathEdit = new QLineEdit();
    fileLoggingLayout->addWidget(m_logFilePathEdit, 1, 1);
    m_browseButton = new QPushButton(tr("Browse..."));
    fileLoggingLayout->addWidget(m_browseButton, 1, 2);

    fileLoggingLayout->addWidget(new QLabel(tr("Max File Size (MB):")), 2, 0);
    m_maxFileSizeSpin = new QSpinBox();
    m_maxFileSizeSpin->setRange(1, 1000);
    m_maxFileSizeSpin->setValue(10);
    fileLoggingLayout->addWidget(m_maxFileSizeSpin, 2, 1);

    fileLoggingLayout->addWidget(new QLabel(tr("Max Backup Count:")), 3, 0);
    m_maxBackupCountSpin = new QSpinBox();
    m_maxBackupCountSpin->setRange(0, 100);
    m_maxBackupCountSpin->setValue(5);
    fileLoggingLayout->addWidget(m_maxBackupCountSpin, 3, 1);

    mainLayout->addWidget(fileLoggingGroup);

    // Console logging settings
    QGroupBox* consoleLoggingGroup = new QGroupBox(tr("Console Logging"));
    QHBoxLayout* consoleLoggingLayout = new QHBoxLayout(consoleLoggingGroup);

    m_consoleLoggingEnabledCheck = new QCheckBox(tr("Enable Console Logging"));
    consoleLoggingLayout->addWidget(m_consoleLoggingEnabledCheck);

    mainLayout->addWidget(consoleLoggingGroup);

    // Buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_resetButton = new QPushButton(tr("Reset"));
    m_okButton = new QPushButton(tr("OK"));
    m_cancelButton = new QPushButton(tr("Cancel"));
    m_applyButton = new QPushButton(tr("Apply"));

    buttonLayout->addWidget(m_resetButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_okButton);
    buttonLayout->addWidget(m_cancelButton);
    buttonLayout->addWidget(m_applyButton);

    mainLayout->addLayout(buttonLayout);

    // Connect signals and slots
    connect(m_okButton, &QPushButton::clicked, this, &LogSettingsDialog::onOkClicked);
    connect(m_cancelButton, &QPushButton::clicked, this, &LogSettingsDialog::onCancelClicked);
    connect(m_applyButton, &QPushButton::clicked, this, &LogSettingsDialog::onApplyClicked);
    connect(m_resetButton, &QPushButton::clicked, this, &LogSettingsDialog::onResetClicked);
    connect(m_addCategoryButton, &QPushButton::clicked, this, &LogSettingsDialog::onAddCategoryClicked);
    connect(m_removeCategoryButton, &QPushButton::clicked, this, &LogSettingsDialog::onRemoveCategoryClicked);
    connect(m_browseButton, &QPushButton::clicked, this, &LogSettingsDialog::onBrowseClicked);
    connect(m_fileLoggingEnabledCheck, &QCheckBox::toggled, this, &LogSettingsDialog::onFileLoggingEnabledChanged);
}

void LogSettingsDialog::loadConfiguration() {
    Utils::LogConfig& config = Utils::LogConfig::instance();

    // Load global log level
    m_globalLevelCombo->setCurrentIndex(logLevelToIndex(config.globalLogLevel()));

    // Load category log levels
    m_categoryLevels = config.categoryLogLevels();
    m_categoryTable->setRowCount(0);

    for (auto it = m_categoryLevels.constBegin(); it != m_categoryLevels.constEnd(); ++it) {
        int row = m_categoryTable->rowCount();
        m_categoryTable->insertRow(row);

        QTableWidgetItem* categoryItem = new QTableWidgetItem(it.key());
        categoryItem->setFlags(categoryItem->flags() & ~Qt::ItemIsEditable);
        m_categoryTable->setItem(row, 0, categoryItem);

        QComboBox* levelCombo = new QComboBox();
        levelCombo->addItem(tr("Trace"), static_cast<int>(Utils::LogLevel::Trace));
        levelCombo->addItem(tr("Debug"), static_cast<int>(Utils::LogLevel::Debug));
        levelCombo->addItem(tr("Info"), static_cast<int>(Utils::LogLevel::Info));
        levelCombo->addItem(tr("Warning"), static_cast<int>(Utils::LogLevel::Warning));
        levelCombo->addItem(tr("Error"), static_cast<int>(Utils::LogLevel::Error));
        levelCombo->addItem(tr("Fatal"), static_cast<int>(Utils::LogLevel::Fatal));
        levelCombo->setCurrentIndex(logLevelToIndex(it.value()));

        m_categoryTable->setCellWidget(row, 1, levelCombo);
    }

    // Load file logging settings
    m_fileLoggingEnabledCheck->setChecked(config.isFileLoggingEnabled());
    m_logFilePathEdit->setText(config.logFilePath());
    m_maxFileSizeSpin->setValue(config.maxLogFileSizeBytes() / (1024 * 1024)); // Convert bytes to MB
    m_maxBackupCountSpin->setValue(config.maxLogBackupCount());

    // Load console logging settings
    m_consoleLoggingEnabledCheck->setChecked(config.isConsoleLoggingEnabled());

    // Update UI state
    onFileLoggingEnabledChanged(m_fileLoggingEnabledCheck->isChecked());
}

void LogSettingsDialog::applyConfiguration() {
    Utils::LogConfig& config = Utils::LogConfig::instance();

    // Apply global log level
    config.setGlobalLogLevel(indexToLogLevel(m_globalLevelCombo->currentIndex()));

    // Apply category log levels
    for (int row = 0; row < m_categoryTable->rowCount(); ++row) {
        QString category = m_categoryTable->item(row, 0)->text();
        QComboBox* levelCombo = qobject_cast<QComboBox*>(m_categoryTable->cellWidget(row, 1));
        if (levelCombo) {
            config.setCategoryLogLevel(category, indexToLogLevel(levelCombo->currentIndex()));
        }
    }

    // Apply file logging settings
    config.setFileLoggingEnabled(
        m_fileLoggingEnabledCheck->isChecked(),
        m_logFilePathEdit->text(),
        static_cast<qint64>(m_maxFileSizeSpin->value()) * 1024 * 1024, // Convert MB to bytes
        m_maxBackupCountSpin->value()
    );

    // Apply console logging settings
    config.setConsoleLoggingEnabled(m_consoleLoggingEnabledCheck->isChecked());

    // Save configuration to file
    config.saveConfiguration(config.defaultConfigFilePath());
}

int LogSettingsDialog::logLevelToIndex(Utils::LogLevel level) const {
    switch (level) {
        case Utils::LogLevel::Trace:   return 0;
        case Utils::LogLevel::Debug:   return 1;
        case Utils::LogLevel::Info:    return 2;
        case Utils::LogLevel::Warning: return 3;
        case Utils::LogLevel::Error:   return 4;
        case Utils::LogLevel::Fatal:   return 5;
        default:                       return 2; // Info
    }
}

Utils::LogLevel LogSettingsDialog::indexToLogLevel(int index) const {
    switch (index) {
        case 0:  return Utils::LogLevel::Trace;
        case 1:  return Utils::LogLevel::Debug;
        case 2:  return Utils::LogLevel::Info;
        case 3:  return Utils::LogLevel::Warning;
        case 4:  return Utils::LogLevel::Error;
        case 5:  return Utils::LogLevel::Fatal;
        default: return Utils::LogLevel::Info;
    }
}

void LogSettingsDialog::onOkClicked() {
    applyConfiguration();
    accept();
}

void LogSettingsDialog::onCancelClicked() {
    reject();
}

void LogSettingsDialog::onApplyClicked() {
    applyConfiguration();
}

void LogSettingsDialog::onResetClicked() {
    loadConfiguration();
}

void LogSettingsDialog::onAddCategoryClicked() {
    bool ok;
    QString category = QInputDialog::getText(this, tr("Add Category"),
                                           tr("Category Name:"), QLineEdit::Normal,
                                           "", &ok);
    if (ok && !category.isEmpty()) {
        // Check if the category already exists
        for (int row = 0; row < m_categoryTable->rowCount(); ++row) {
            if (m_categoryTable->item(row, 0)->text() == category) {
                QMessageBox::warning(this, tr("Warning"),
                                    tr("Category '%1' already exists.").arg(category));
                return;
            }
        }

        // Add the category
        int row = m_categoryTable->rowCount();
        m_categoryTable->insertRow(row);

        QTableWidgetItem* categoryItem = new QTableWidgetItem(category);
        categoryItem->setFlags(categoryItem->flags() & ~Qt::ItemIsEditable);
        m_categoryTable->setItem(row, 0, categoryItem);

        QComboBox* levelCombo = new QComboBox();
        levelCombo->addItem(tr("Trace"), static_cast<int>(Utils::LogLevel::Trace));
        levelCombo->addItem(tr("Debug"), static_cast<int>(Utils::LogLevel::Debug));
        levelCombo->addItem(tr("Info"), static_cast<int>(Utils::LogLevel::Info));
        levelCombo->addItem(tr("Warning"), static_cast<int>(Utils::LogLevel::Warning));
        levelCombo->addItem(tr("Error"), static_cast<int>(Utils::LogLevel::Error));
        levelCombo->addItem(tr("Fatal"), static_cast<int>(Utils::LogLevel::Fatal));
        levelCombo->setCurrentIndex(logLevelToIndex(Utils::LogLevel::Info));

        m_categoryTable->setCellWidget(row, 1, levelCombo);
    }
}

void LogSettingsDialog::onRemoveCategoryClicked() {
    int row = m_categoryTable->currentRow();
    if (row >= 0) {
        QString category = m_categoryTable->item(row, 0)->text();

        QMessageBox::StandardButton result = QMessageBox::question(this, tr("Confirm Removal"),
                                                                 tr("Are you sure you want to remove the category '%1'?").arg(category),
                                                                 QMessageBox::Yes | QMessageBox::No);
        if (result == QMessageBox::Yes) {
            m_categoryTable->removeRow(row);
        }
    } else {
        QMessageBox::information(this, tr("Information"),
                               tr("Please select a category to remove."));
    }
}

void LogSettingsDialog::onBrowseClicked() {
    QString defaultDir = QFileInfo(m_logFilePathEdit->text()).absolutePath();
    if (defaultDir.isEmpty() || !QDir(defaultDir).exists()) {
        defaultDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    }

    QString filePath = QFileDialog::getSaveFileName(this, tr("Select Log File"),
                                                  defaultDir,
                                                  tr("Log Files (*.log);;All Files (*)"));
    if (!filePath.isEmpty()) {
        m_logFilePathEdit->setText(filePath);
    }
}

void LogSettingsDialog::onFileLoggingEnabledChanged(bool checked) {
    m_logFilePathEdit->setEnabled(checked);
    m_browseButton->setEnabled(checked);
    m_maxFileSizeSpin->setEnabled(checked);
    m_maxBackupCountSpin->setEnabled(checked);
}

} // namespace UI
} // namespace Vizion3D
