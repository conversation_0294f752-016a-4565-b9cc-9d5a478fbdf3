#include "ui/project_tree_widget.h"
#include "utils/logger.h"
#include <QDirIterator>
#include <QHBoxLayout>

namespace Vizion3D {
namespace UI {

ProjectTreeWidget::ProjectTreeWidget(QWidget* parent)
    : QWidget(parent)
    , m_treeWidget(nullptr)
    , m_selectedNcProgramItem(nullptr)
    , m_ncProgramsFolder(nullptr)
{
    initializeUi();
    initializeDefaultTree();
}

ProjectTreeWidget::~ProjectTreeWidget() = default;

void ProjectTreeWidget::initializeUi()
{
    // Create layout
    auto* layout = new QVBoxLayout(this); // NOLINT(cppcoreguidelines-owning-memory) Qt parent-child ownership
    layout->setContentsMargins(0, 0, 0, 0);

    // Create tree widget
    m_treeWidget = new QTreeWidget(this); // NOLINT(cppcoreguidelines-owning-memory) Qt parent-child ownership
    m_treeWidget->setHeaderHidden(true);
    m_treeWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    m_treeWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_treeWidget->setAnimated(true);
    m_treeWidget->setIndentation(20);

    // Create top spacer layout (reserved for future controls)
    auto* buttonLayout = new QHBoxLayout(); // NOLINT(cppcoreguidelines-owning-memory) Qt parent-child ownership
    buttonLayout->addStretch();

    // Add widgets to layout
    layout->addLayout(buttonLayout);
    layout->addWidget(m_treeWidget);

    // Connect signals
    connect(m_treeWidget, &QTreeWidget::itemDoubleClicked, this, &ProjectTreeWidget::onItemDoubleClicked);
    connect(m_treeWidget, &QTreeWidget::customContextMenuRequested, this, &ProjectTreeWidget::onCustomContextMenu);
}

void ProjectTreeWidget::initializeDefaultTree()
{
    // Clear the tree
    m_treeWidget->clear();

    // Create project root
    auto* projectRoot = new QTreeWidgetItem(m_treeWidget); // NOLINT(cppcoreguidelines-owning-memory) QTreeWidget takes ownership
    projectRoot->setText(0, "My Vizion3D Project");
    projectRoot->setIcon(0, QIcon::fromTheme("folder", QIcon(":/icons/folder.png")));
    setNodeType(projectRoot, NodeType::PROJECT);
    projectRoot->setExpanded(true);

    // Create NC Programs folder
    m_ncProgramsFolder = new QTreeWidgetItem(projectRoot); // NOLINT(cppcoreguidelines-owning-memory) QTreeWidgetItem parent takes ownership
    m_ncProgramsFolder->setText(0, "NC Programs");
    m_ncProgramsFolder->setIcon(0, QIcon::fromTheme("folder", QIcon(":/icons/folder.png")));
    setNodeType(m_ncProgramsFolder, NodeType::NC_PROGRAMS_FOLDER);
    m_ncProgramsFolder->setExpanded(true);

    // Create Machine Type folder
    auto* machineTypeFolder = new QTreeWidgetItem(projectRoot); // NOLINT(cppcoreguidelines-owning-memory) QTreeWidgetItem parent takes ownership
    machineTypeFolder->setText(0, "CNC Machine");
    machineTypeFolder->setIcon(0, QIcon::fromTheme("folder", QIcon(":/icons/folder.png")));
    setNodeType(machineTypeFolder, NodeType::MACHINE_TYPE_FOLDER);
    machineTypeFolder->setExpanded(true);

    // Create Control folder
    auto* controlFolder = new QTreeWidgetItem(machineTypeFolder); // NOLINT(cppcoreguidelines-owning-memory) QTreeWidgetItem parent takes ownership
    controlFolder->setText(0, "Control: MCAMV_control");
    controlFolder->setIcon(0, QIcon::fromTheme("folder", QIcon(":/icons/folder.png")));
    setNodeType(controlFolder, NodeType::CONTROL_FOLDER);

    // Create Machine folder
    auto* machineFolder = new QTreeWidgetItem(machineTypeFolder); // NOLINT(cppcoreguidelines-owning-memory) QTreeWidgetItem parent takes ownership
    machineFolder->setText(0, "Machine: 3-Axis Mill");
    machineFolder->setIcon(0, QIcon::fromTheme("folder", QIcon(":/icons/folder.png")));
    setNodeType(machineFolder, NodeType::MACHINE_FOLDER);

    // Create Axis Config folder
    auto* axisConfigFolder = new QTreeWidgetItem(machineFolder); // NOLINT(cppcoreguidelines-owning-memory) QTreeWidgetItem parent takes ownership
    axisConfigFolder->setText(0, "Axes");
    axisConfigFolder->setIcon(0, QIcon::fromTheme("folder", QIcon(":/icons/folder.png")));
    setNodeType(axisConfigFolder, NodeType::AXIS_CONFIG_FOLDER);

    // Create X Axis
    auto* xAxis = new QTreeWidgetItem(axisConfigFolder); // NOLINT(cppcoreguidelines-owning-memory) QTreeWidgetItem parent takes ownership
    xAxis->setText(0, "X Axis");
    xAxis->setIcon(0, QIcon::fromTheme("folder", QIcon(":/icons/axis.png")));
    setNodeType(xAxis, NodeType::AXIS);

    // Create Y Axis
    auto* yAxis = new QTreeWidgetItem(axisConfigFolder); // NOLINT(cppcoreguidelines-owning-memory) QTreeWidgetItem parent takes ownership
    yAxis->setText(0, "Y Axis");
    yAxis->setIcon(0, QIcon::fromTheme("folder", QIcon(":/icons/axis.png")));
    setNodeType(yAxis, NodeType::AXIS);

    // Create Z Axis
    auto* zAxis = new QTreeWidgetItem(axisConfigFolder); // NOLINT(cppcoreguidelines-owning-memory) QTreeWidgetItem parent takes ownership
    zAxis->setText(0, "Z Axis");
    zAxis->setIcon(0, QIcon::fromTheme("folder", QIcon(":/icons/axis.png")));
    setNodeType(zAxis, NodeType::AXIS);

    // Create Spindle
    auto* spindle = new QTreeWidgetItem(machineFolder); // NOLINT(cppcoreguidelines-owning-memory) QTreeWidgetItem parent takes ownership
    spindle->setText(0, "Spindle");
    spindle->setIcon(0, QIcon::fromTheme("folder", QIcon(":/icons/spindle.png")));
    setNodeType(spindle, NodeType::SPINDLE);

    // Create Tool Geometry
    auto* toolGeometry = new QTreeWidgetItem(spindle); // NOLINT(cppcoreguidelines-owning-memory) QTreeWidgetItem parent takes ownership
    toolGeometry->setText(0, "Tool: End Mill");
    toolGeometry->setIcon(0, QIcon::fromTheme("folder", QIcon(":/icons/tool.png")));
    setNodeType(toolGeometry, NodeType::TOOL_GEOMETRY);
}

QTreeWidgetItem* ProjectTreeWidget::addNcProgram(const QString& name, const QString& content, QTreeWidgetItem* parentItem)
{
    // If no parent item is specified, use the NC Programs folder
    if (!parentItem) {
        parentItem = m_ncProgramsFolder;
    }

    // Create NC Program item
    auto* ncProgramItem = new QTreeWidgetItem(parentItem); // NOLINT(cppcoreguidelines-owning-memory) QTreeWidgetItem parent takes ownership
    ncProgramItem->setText(0, name);
    ncProgramItem->setIcon(0, QIcon::fromTheme("text-x-generic", QIcon(":/icons/file.png")));
    setNodeType(ncProgramItem, NodeType::NC_PROGRAM_FILE);
    ncProgramItem->setData(0, NcProgramContentRole, content);

    // Expand the parent item
    parentItem->setExpanded(true);

    return ncProgramItem;
}

void ProjectTreeWidget::onItemDoubleClicked(QTreeWidgetItem* item, int column)
{
    if (!item) {
        return;
    }

    NodeType type = getNodeType(item);
    if (type == NodeType::NC_PROGRAM_FILE) {
        // Set as selected NC program
        m_selectedNcProgramItem = item;

        // Get the content
        QString content = item->data(0, NcProgramContentRole).toString();
        QString name = item->text(0);

        // Emit signal
        emit ncProgramSelected(content, name);
    }
}

void ProjectTreeWidget::onCustomContextMenu(const QPoint& pos)
{
    QTreeWidgetItem* item = m_treeWidget->itemAt(pos);
    if (!item) {
        return;
    }

    // Create context menu
    QMenu contextMenu(this);

    // Add actions based on node type
    NodeType type = getNodeType(item);
    if (type == NodeType::NC_PROGRAMS_FOLDER) {
        QAction* importAction = contextMenu.addAction("Import NC Program");
        connect(importAction, &QAction::triggered, this, &ProjectTreeWidget::onImportNcProgram);
    } else if (type == NodeType::NC_PROGRAM_FILE) {
        QAction* selectAction = contextMenu.addAction("Select for Simulation");
        connect(selectAction, &QAction::triggered, [this, item]() {
            onItemDoubleClicked(item, 0);
        });
    }

    // Add common actions
    contextMenu.addSeparator();
    QAction* renameAction = contextMenu.addAction("Rename");
    connect(renameAction, &QAction::triggered, this, &ProjectTreeWidget::onRenameItem);

    if (type == NodeType::NC_PROGRAM_FILE) {
        QAction* deleteAction = contextMenu.addAction("Delete");
        connect(deleteAction, &QAction::triggered, this, &ProjectTreeWidget::onDeleteItem);
    }

    // Show the context menu
    contextMenu.exec(m_treeWidget->mapToGlobal(pos));
}

void ProjectTreeWidget::onRenameItem()
{
    QTreeWidgetItem* item = m_treeWidget->currentItem();
    if (!item) {
        return;
    }

    // Get current name
    QString currentName = item->text(0);

    // Show input dialog
    bool ok = false;
    QString newName = QInputDialog::getText(this, "Rename", "New name:", QLineEdit::Normal, currentName, &ok);
    if (ok && !newName.isEmpty()) {
        item->setText(0, newName);
    }
}

void ProjectTreeWidget::onDeleteItem()
{
    QTreeWidgetItem* item = m_treeWidget->currentItem();
    if (!item) {
        return;
    }

    // Confirm deletion
    QMessageBox::StandardButton reply = QMessageBox::question(this, "Delete Item", "Are you sure you want to delete this item?", QMessageBox::Yes | QMessageBox::No);
    if (reply == QMessageBox::Yes) {
        // Check if this is the selected NC program
        if (item == m_selectedNcProgramItem) {
            m_selectedNcProgramItem = nullptr;
        }

        // Delete the item
        if (item->parent()) {
            item->parent()->removeChild(item); // Removes and deletes the item via Qt parent-child ownership
        } else {
            m_treeWidget->takeTopLevelItem(m_treeWidget->indexOfTopLevelItem(item)); // Removes and deletes
        }
    }
}

void ProjectTreeWidget::onImportNcProgram()
{
    // Open file dialog
    QString filePath = QFileDialog::getOpenFileName(this, "Import NC Program", "", "NC Programs (*.nc *.gcode *.ngc);;All Files (*)");
    if (filePath.isEmpty()) {
        return;
    }

    // Read file content
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::critical(this, "Error", "Could not open file: " + file.errorString());
        return;
    }

    QTextStream in(&file);
    QString content = in.readAll();
    file.close();

    // Get file name
    QFileInfo fileInfo(filePath);
    QString fileName = fileInfo.fileName();

    // Add NC program
    QTreeWidgetItem* item = addNcProgram(fileName, content);

    // Select the new item
    m_treeWidget->setCurrentItem(item);
    onItemDoubleClicked(item, 0);
}

QString ProjectTreeWidget::getSelectedNcProgram() const
{
    if (m_selectedNcProgramItem) {
        return m_selectedNcProgramItem->data(0, NcProgramContentRole).toString();
    }
    return {};
}

QString ProjectTreeWidget::getSelectedNcProgramName() const
{
    if (m_selectedNcProgramItem) {
        return m_selectedNcProgramItem->text(0);
    }
    return {};
}

NodeType ProjectTreeWidget::getNodeType(QTreeWidgetItem* item) const
{
    if (!item) {
        return NodeType::UNKNOWN;
    }

    QVariant data = item->data(0, NodeTypeRole);
    if (data.isValid()) {
        return static_cast<NodeType>(data.toInt());
    }
    return NodeType::UNKNOWN;
}

void ProjectTreeWidget::setNodeType(QTreeWidgetItem* item, NodeType type)
{
    if (item) {
        item->setData(0, NodeTypeRole, static_cast<int>(type));
    }
}

QString ProjectTreeWidget::getIconForNodeType(NodeType type) const
{
    switch (type) {
        case NodeType::PROJECT:
            return ":/icons/project.png";
        case NodeType::NC_PROGRAMS_FOLDER:
            return ":/icons/folder.png";
        case NodeType::NC_PROGRAM_FILE:
            return ":/icons/file.png";
        case NodeType::MACHINE_TYPE_FOLDER:
            return ":/icons/machine.png";
        case NodeType::CONTROL_FOLDER:
            return ":/icons/control.png";
        case NodeType::MACHINE_FOLDER:
            return ":/icons/machine.png";
        case NodeType::AXIS_CONFIG_FOLDER:
            return ":/icons/folder.png";
        case NodeType::AXIS:
            return ":/icons/axis.png";
        case NodeType::SPINDLE:
            return ":/icons/spindle.png";
        case NodeType::TOOL_GEOMETRY:
            return ":/icons/tool.png";
        default:
            return ":/icons/folder.png";
    }
}

void ProjectTreeWidget::loadProject(const QString& projectDirPath)
{
    // Reset the tree to default structure
    initializeDefaultTree();

    // Update project root name to folder name
    if (!m_treeWidget->topLevelItemCount()) {
        return;
    }

    QTreeWidgetItem* projectRoot = m_treeWidget->topLevelItem(0);
    QFileInfo projectInfo(projectDirPath);
    projectRoot->setText(0, projectInfo.fileName());

    // Recursively iterate through the directory and add NC programs
    QStringList nameFilters = {"*.nc", "*.gcode", "*.ngc"};
    QDirIterator it(projectDirPath, nameFilters, QDir::Files, QDirIterator::Subdirectories);
    while (it.hasNext()) {
        QString filePath = it.next();
        QFileInfo fi(filePath);

        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            VLOG_WARNING("UI", QString("Failed to open NC program: %1. Reason: %2").arg(filePath, file.errorString()));
            continue;
        }

        QTextStream in(&file);
        QString content = in.readAll();
        file.close();

        addNcProgram(fi.fileName(), content);
    }
}

} // namespace UI
} // namespace Vizion3D
