#include "ui/mainwindow.h"
#include "ui_mainwindow.h"
#include "ui/simulation_view.h"
#include "ui/project_tree_widget.h"
#include "ui/gcode_editor.h"
#include "ui/debug_console_widget.h"
#include "ui/log_settings_dialog.h"
#include "engine/engine_factory.h"
#include "engine/interfaces/simulation_engine_interface.h"
#include "utils/logger.h"
#include "utils/log_config.h"
#include "utils/debug_config.h"

#include <QDebug>
#include <QFile>
#include <QTextStream>
#include <QDateTime>
#include <QVBoxLayout>
#include <QWheelEvent>
#include <QApplication>
#include <QFileInfo>
#include <QCloseEvent>
#include <QTabWidget>
#include <QDockWidget>
#include <QElapsedTimer>
#include <QDateTime>

/**
 * @brief Logs a diagnostic message with a timestamp and step number
 *
 * @param step The step number in the initialization sequence
 * @param message The message to display
 * @param elapsedTimer Timer to track elapsed time since MainWindow construction started
 */
void logMainWindowDiagnosticMessage(int step, const QString& message, const QElapsedTimer& elapsedTimer) {
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    QString elapsedMs = QString::number(elapsedTimer.elapsed());
    QString fullMessage = QString("[%1] MainWindow Step %2 (%3 ms): %4")
                              .arg(timestamp)
                              .arg(step)
                              .arg(elapsedMs)
                              .arg(message);

    VLOG_DEBUG("UI", QString("MAINWINDOW DIAGNOSTIC: %1").arg(fullMessage));
}

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_engineFactory(new Vizion3D::Engine::EngineFactory())
    , m_simulationEngine(nullptr)
    , m_simulationView(nullptr)
    , m_debugConsoleWidget(nullptr)
    , m_logSettingsDialog(nullptr)
{
    // Start a timer to track elapsed time
    QElapsedTimer elapsedTimer;
    elapsedTimer.start();

    VLOG_DEBUG("UI", "MainWindow constructor started");

    try {
        logMainWindowDiagnosticMessage(1, "MainWindow constructor started", elapsedTimer);

        VLOG_DEBUG("UI", "Setting up UI");
        try {
            ui->setupUi(this);
            VLOG_DEBUG("UI", "UI setup complete");
        } catch (const std::exception& e) {
            QString errorMsg = QString("Exception in setupUi: %1").arg(e.what());
            VLOG_ERROR("UI", errorMsg);
            QMessageBox::critical(this, "UI Setup Error", errorMsg);
            throw; // Re-throw to be caught in main.cpp
        } catch (...) {
            QString errorMsg = "Unknown exception in setupUi";
            VLOG_ERROR("UI", errorMsg);
            QMessageBox::critical(this, "UI Setup Error", errorMsg);
            throw; // Re-throw to be caught in main.cpp
        }
        logMainWindowDiagnosticMessage(2, "UI setup complete", elapsedTimer);

        // Initialize simulation engine
        VLOG_DEBUG("UI", "Initializing simulation engine");
        try {
            logMainWindowDiagnosticMessage(3, "About to initialize simulation engine", elapsedTimer);
            initializeSimulationEngine();
            VLOG_DEBUG("UI", "Simulation engine initialized");
            logMainWindowDiagnosticMessage(4, "Simulation engine initialized", elapsedTimer);
        } catch (const std::exception& e) {
            QString errorMsg = QString("Exception in initializeSimulationEngine: %1").arg(e.what());
            VLOG_ERROR("UI", errorMsg);
            QMessageBox::critical(this, "Engine Initialization Error", errorMsg);
            throw; // Re-throw to be caught in main.cpp
        } catch (...) {
            QString errorMsg = "Unknown exception in initializeSimulationEngine";
            VLOG_ERROR("UI", errorMsg);
            QMessageBox::critical(this, "Engine Initialization Error", errorMsg);
            throw; // Re-throw to be caught in main.cpp
        }

        // Initialize UI
        VLOG_DEBUG("UI", "Initializing UI components");
        try {
            logMainWindowDiagnosticMessage(5, "About to initialize UI components", elapsedTimer);
            initializeUi();
            VLOG_DEBUG("UI", "UI components initialized");
            logMainWindowDiagnosticMessage(6, "UI components initialized", elapsedTimer);
        } catch (const std::exception& e) {
            QString errorMsg = QString("Exception in initializeUi: %1").arg(e.what());
            VLOG_ERROR("UI", errorMsg);
            QMessageBox::critical(this, "UI Initialization Error", errorMsg);
            throw; // Re-throw to be caught in main.cpp
        } catch (...) {
            QString errorMsg = "Unknown exception in initializeUi";
            VLOG_ERROR("UI", errorMsg);
            QMessageBox::critical(this, "UI Initialization Error", errorMsg);
            throw; // Re-throw to be caught in main.cpp
        }

        VLOG_DEBUG("UI", "Connecting signals and slots");
        try {
            logMainWindowDiagnosticMessage(7, "About to connect signals and slots", elapsedTimer);
            connectSignalsAndSlots();
            VLOG_DEBUG("UI", "Signals and slots connected");
            logMainWindowDiagnosticMessage(8, "Signals and slots connected", elapsedTimer);
        } catch (const std::exception& e) {
            QString errorMsg = QString("Exception in connectSignalsAndSlots: %1").arg(e.what());
            VLOG_ERROR("UI", errorMsg);
            QMessageBox::critical(this, "Signal/Slot Connection Error", errorMsg);
            throw; // Re-throw to be caught in main.cpp
        } catch (...) {
            QString errorMsg = "Unknown exception in connectSignalsAndSlots";
            VLOG_ERROR("UI", errorMsg);
            QMessageBox::critical(this, "Signal/Slot Connection Error", errorMsg);
            throw; // Re-throw to be caught in main.cpp
        }

        VLOG_DEBUG("UI", "Loading settings");
        try {
            logMainWindowDiagnosticMessage(9, "About to load settings", elapsedTimer);
            loadSettings();
            VLOG_DEBUG("UI", "Settings loaded");
            logMainWindowDiagnosticMessage(10, "Settings loaded", elapsedTimer);
        } catch (const std::exception& e) {
            QString errorMsg = QString("Exception in loadSettings: %1").arg(e.what());
            VLOG_ERROR("UI", errorMsg);
            QMessageBox::critical(this, "Settings Error", errorMsg);
            throw; // Re-throw to be caught in main.cpp
        } catch (...) {
            QString errorMsg = "Unknown exception in loadSettings";
            VLOG_ERROR("UI", errorMsg);
            QMessageBox::critical(this, "Settings Error", errorMsg);
            throw; // Re-throw to be caught in main.cpp
        }

        // Skip creating debug console widget for now to avoid potential issues
        VLOG_DEBUG("UI", "Skipping debug console widget creation for troubleshooting");
        m_debugConsoleWidget = nullptr; // Set to nullptr instead of creating it
        logMainWindowDiagnosticMessage(11, "Debug console widget creation skipped", elapsedTimer);

        // Log application initialization using qDebug instead of VLOG_INFO
        VLOG_DEBUG("UI", "MainWindow initialized");

        // Set window title with version
        setWindowTitle("Vizion3D - CNC Simulation v1.0.0");

        // Set status bar message
        statusBar()->showMessage("Ready");

        VLOG_DEBUG("UI", "MainWindow constructor completed successfully");
        logMainWindowDiagnosticMessage(12, "MainWindow constructor completed successfully", elapsedTimer);
    } catch (const std::exception& e) {
        VLOG_ERROR("UI", QString("Exception in MainWindow constructor: %1").arg(e.what()));
        QMessageBox::critical(this, "MainWindow Error", QString("Exception in MainWindow constructor: %1").arg(e.what()));
        throw; // Re-throw to be caught in main.cpp
    } catch (...) {
        VLOG_ERROR("UI", "Unknown exception in MainWindow constructor");
        QMessageBox::critical(this, "MainWindow Error", "Unknown exception in MainWindow constructor");
        throw; // Re-throw to be caught in main.cpp
    }
}

MainWindow::~MainWindow()
{
    // Log application shutdown using qDebug instead of VLOG_INFO
    VLOG_DEBUG("UI", "MainWindow shutting down");

    saveSettings();

    // Clean up simulation engine
    if (m_simulationEngine) {
        m_simulationEngine->stopSimulation();
        delete m_simulationEngine;
        m_simulationEngine = nullptr;
    }

    // Clean up debug console widget
    if (m_debugConsoleWidget) {
        delete m_debugConsoleWidget;
        m_debugConsoleWidget = nullptr;
    }

    // Clean up log settings dialog
    if (m_logSettingsDialog) {
        delete m_logSettingsDialog;
        m_logSettingsDialog = nullptr;
    }

    delete ui;
}

void MainWindow::initializeSimulationEngine()
{
    // Start a timer to track elapsed time
    QElapsedTimer engineTimer;
    engineTimer.start();

    VLOG_DEBUG("UI", "initializeSimulationEngine: Creating simulation view");
    try {
        // Create simulation view first
        logMainWindowDiagnosticMessage(101, "About to create SimulationView", engineTimer);
        m_simulationView = new Vizion3D::UI::SimulationView(this); // NOLINT(cppcoreguidelines-owning-memory) - Qt parent-child ownership
        VLOG_DEBUG("UI", "initializeSimulationEngine: SimulationView created");
        logMainWindowDiagnosticMessage(102, "SimulationView created", engineTimer);

        // Add simulation view to viewport container
        VLOG_DEBUG("UI", "initializeSimulationEngine: Setting up viewport layout");
        logMainWindowDiagnosticMessage(103, "About to set up viewport layout", engineTimer);
        auto* layout = new QVBoxLayout(ui->viewportContainer); // NOLINT(cppcoreguidelines-owning-memory) - Qt parent-child ownership
        layout->setContentsMargins(0, 0, 0, 0);
        layout->addWidget(m_simulationView);
        ui->viewportContainer->setLayout(layout);
        VLOG_DEBUG("UI", "initializeSimulationEngine: Viewport layout set up");
        logMainWindowDiagnosticMessage(104, "Viewport layout set up", engineTimer);

        // Create simulation engine
        VLOG_DEBUG("UI", "initializeSimulationEngine: Creating simulation engine");
        logMainWindowDiagnosticMessage(105, "About to create simulation engine", engineTimer);
        m_simulationEngine = m_engineFactory->createSimulationEngine(this);
        VLOG_DEBUG("UI", "initializeSimulationEngine: Simulation engine created");
        logMainWindowDiagnosticMessage(106, "Simulation engine created", engineTimer);

        // Initialize the engine
        VLOG_DEBUG("UI", "initializeSimulationEngine: Initializing engine");
        logMainWindowDiagnosticMessage(107, "About to initialize engine", engineTimer);
        auto initResult = m_simulationEngine->initialize();
        if (!initResult.isSuccess()) {
            const auto& error = initResult.error();
            VLOG_ERROR("UI", QString("Failed to initialize simulation engine: %1").arg(error.message));
            // Already logged with VLOG_ERROR above, remove duplicate qCritical
            QMessageBox::critical(this, "Error", QString("Failed to initialize simulation engine: %1").arg(error.message));
            return;
        }
        VLOG_DEBUG("UI", "initializeSimulationEngine: Engine initialized successfully");
        logMainWindowDiagnosticMessage(108, "Engine initialized successfully", engineTimer);

        // Connect simulation view to engine
        VLOG_DEBUG("UI", "initializeSimulationEngine: Connecting view to engine");
        logMainWindowDiagnosticMessage(109, "About to connect view to engine", engineTimer);
        m_simulationView->connectToEngine(m_simulationEngine);
        VLOG_DEBUG("UI", "initializeSimulationEngine: View connected to engine");
        logMainWindowDiagnosticMessage(110, "View connected to engine", engineTimer);

        // Connect engine signals to MainWindow slots
        VLOG_DEBUG("UI", "initializeSimulationEngine: Connecting engine signals to slots");
        logMainWindowDiagnosticMessage(111, "About to connect engine signals to slots", engineTimer);
        connect(m_simulationEngine, &Vizion3D::Engine::Interfaces::ISimulationEngine::simulationStarted,
                this, &MainWindow::onSimulationStarted);
        connect(m_simulationEngine, &Vizion3D::Engine::Interfaces::ISimulationEngine::simulationPaused,
                this, &MainWindow::onSimulationPaused);
        connect(m_simulationEngine, &Vizion3D::Engine::Interfaces::ISimulationEngine::simulationResumed,
                this, &MainWindow::onSimulationResumed);
        connect(m_simulationEngine, &Vizion3D::Engine::Interfaces::ISimulationEngine::simulationStopped,
                this, &MainWindow::onSimulationStopped);
        connect(m_simulationEngine, &Vizion3D::Engine::Interfaces::ISimulationEngine::simulationCompleted,
                this, &MainWindow::onSimulationCompleted);
        connect(m_simulationEngine, &Vizion3D::Engine::Interfaces::ISimulationEngine::simulationError,
                this, &MainWindow::onSimulationError);
        connect(m_simulationEngine, &Vizion3D::Engine::Interfaces::ISimulationEngine::parsingCompleted,
                this, &MainWindow::onParsingCompleted);
        connect(m_simulationEngine, &Vizion3D::Engine::Interfaces::ISimulationEngine::parsingError,
                this, &MainWindow::onParsingError);
        VLOG_DEBUG("UI", "initializeSimulationEngine: Engine signals connected");
        logMainWindowDiagnosticMessage(112, "Engine signals connected", engineTimer);
    } catch (const std::exception& e) {
        QString errorMsg = QString("Exception in initializeSimulationEngine: %1").arg(e.what());
        VLOG_ERROR("UI", errorMsg);
        QMessageBox::critical(this, "Error", errorMsg);
        throw; // Re-throw to be caught in MainWindow constructor
    } catch (...) {
        QString errorMsg = "Unknown exception in initializeSimulationEngine";
        VLOG_ERROR("UI", errorMsg);
        QMessageBox::critical(this, "Error", errorMsg);
        throw; // Re-throw to be caught in MainWindow constructor
    }
}

void MainWindow::initializeUi()
{
    // Initialize UI components
    ui->pauseButton->setEnabled(false);
    ui->stopButton->setEnabled(false);
    ui->stepButton->setEnabled(false);
    ui->actionPause->setEnabled(false);
    ui->actionStop->setEnabled(false);
    ui->actionStep->setEnabled(false);

    // Set initial button text
    ui->stopButton->setText("Reset");
    ui->actionStop->setText("Reset");

    // Set initial speed slider value
    ui->speedSlider->setValue(5);

    // Create tab widget for left panel
    auto* leftTabWidget = new QTabWidget(ui->leftPanel); // NOLINT(cppcoreguidelines-owning-memory) - Qt parent-child ownership
    ui->verticalLayout_2->addWidget(leftTabWidget);

    // Create Project Tree widget
    m_projectTreeWidget = new Vizion3D::UI::ProjectTreeWidget(this); // NOLINT(cppcoreguidelines-owning-memory) - Qt parent-child ownership
    leftTabWidget->addTab(m_projectTreeWidget, "Project Tree");

    // Create G-code editor
    m_gCodeEditor = new Vizion3D::UI::GCodeEditor(this); // NOLINT(cppcoreguidelines-owning-memory) - Qt parent-child ownership
    m_gCodeEditor->setPlaceholderText("Enter G-code here or open a file...");
    leftTabWidget->addTab(m_gCodeEditor, "G-code Editor");

    // Add some sample G-code
    m_gCodeEditor->setPlainText(
        "G90 ; Absolute positioning\n"
        "G21 ; Set units to mm\n"
        "G0 Z5 ; Raise tool\n"
        "G0 X0 Y0 ; Move to origin\n"
        "G1 Z-1 F100 ; Plunge to cutting depth\n"
        "G1 X50 Y0 F200 ; Cut line\n"
        "G1 X50 Y50 ; Cut line\n"
        "G1 X0 Y50 ; Cut line\n"
        "G1 X0 Y0 ; Cut line\n"
        "G0 Z5 ; Raise tool\n"
        "M30 ; End program"
    );

    // Remove the original G-code editor and placeholder labels
    ui->gCodeEditor->setVisible(false);
    ui->label->setVisible(false);

    // Hide the 3D viewport placeholder label
    ui->label_2->setVisible(false);
}

void MainWindow::connectSignalsAndSlots()
{
    // Connect edit menu actions to the G-code editor
    connect(ui->actionUndo, &QAction::triggered, m_gCodeEditor, &QPlainTextEdit::undo);
    connect(ui->actionRedo, &QAction::triggered, m_gCodeEditor, &QPlainTextEdit::redo);
    connect(ui->actionCut, &QAction::triggered, m_gCodeEditor, &QPlainTextEdit::cut);
    connect(ui->actionCopy, &QAction::triggered, m_gCodeEditor, &QPlainTextEdit::copy);
    connect(ui->actionPaste, &QAction::triggered, m_gCodeEditor, &QPlainTextEdit::paste);

    // Connect Project Tree signals
    connect(m_projectTreeWidget, &Vizion3D::UI::ProjectTreeWidget::ncProgramSelected,
            this, [this](const QString& content, const QString& name) {
                m_gCodeEditor->setPlainText(content);
                statusBar()->showMessage(QString("Loaded NC program: %1").arg(name), 2000);
                // Update UI to enable/disable buttons based on new G-code content
                updateUi();
            });

    // Connect G-code editor text changes to update UI
    connect(m_gCodeEditor, &QPlainTextEdit::textChanged,
            this, [this]() {
                // Update UI when G-code content changes (typed, pasted, etc.)
                updateUi();
            });

    // Connect parsing progress signals
    connect(m_simulationEngine, &Vizion3D::Engine::Interfaces::ISimulationEngine::parsingProgress,
            this, [this](int lineNumber, int totalLines) {
                double progress = static_cast<double>(lineNumber) / totalLines * 100.0;
                statusBar()->showMessage(QString("Parsing G-code: %1%").arg(progress, 0, 'f', 1));
            });

    // Connect simulation progress signals
    connect(m_simulationEngine, &Vizion3D::Engine::Interfaces::ISimulationEngine::simulationProgress,
            this, [this](double progress) {
                statusBar()->showMessage(QString("Simulation progress: %1%").arg(progress * 100.0, 0, 'f', 1));
            });

    // Connect simulation step signal to highlight current line in G-code editor
    connect(m_simulationEngine, &Vizion3D::Engine::Interfaces::ISimulationEngine::simulationStep,
            this, [this](const Vizion3D::Engine::Interfaces::ToolpathPoint& point) {
                // Get the line number from the toolpath point
                int lineNumber = point.lineNumber() + 1; // Convert to 1-based line number
                if (lineNumber > 0) {
                    m_gCodeEditor->setCurrentLine(lineNumber);
                }
            });
}

void MainWindow::updateUi()
{
    // Update UI based on simulation state
    bool isRunning = m_simulationEngine->isSimulationRunning();
    bool isPaused = m_simulationEngine->isSimulationPaused();
    bool isSteppingMode = m_simulationEngine->isSteppingMode();
    bool hasGCode = !m_gCodeEditor->toPlainText().isEmpty();

    // Start button: enabled when not running OR when paused (to resume) OR when in stepping mode
    ui->startButton->setEnabled(!isRunning || isPaused || isSteppingMode);
    ui->actionStart->setEnabled(!isRunning || isPaused || isSteppingMode);

    // Pause button: enabled when running and not paused and NOT in stepping mode (automatic simulation only)
    ui->pauseButton->setEnabled(isRunning && !isPaused && !isSteppingMode);
    ui->actionPause->setEnabled(isRunning && !isPaused && !isSteppingMode);

    // Reset button (was Stop): always enabled when there's G-code
    ui->stopButton->setEnabled(hasGCode);
    ui->actionStop->setEnabled(hasGCode);

    // Step button: enabled when there's G-code AND (simulation is paused OR not running OR in stepping mode)
    bool canStep = hasGCode && (isPaused || !isRunning || isSteppingMode);
    ui->stepButton->setEnabled(canStep);
    ui->actionStep->setEnabled(canStep);

    // Update button text to reflect new functionality
    if (isPaused) {
        ui->startButton->setText("Resume");
        ui->actionStart->setText("Resume");
    } else if (isSteppingMode) {
        ui->startButton->setText("Auto Run");
        ui->actionStart->setText("Auto Run");
    } else {
        ui->startButton->setText("Start");
        ui->actionStart->setText("Start");
    }

    // Change Stop button to Reset
    ui->stopButton->setText("Reset");
    ui->actionStop->setText("Reset");

    // Update status bar
    if (isRunning) {
        if (isPaused) {
            statusBar()->showMessage("Simulation paused - Click Resume to continue or Step to advance one line");
        } else if (isSteppingMode) {
            statusBar()->showMessage("Stepping mode - Click Step to advance one line or Auto Run for continuous simulation");
        } else {
            statusBar()->showMessage("Simulation running");
        }
    } else {
        if (hasGCode) {
            statusBar()->showMessage("Ready - Click Start to begin simulation or Step to debug line by line");
        } else {
            statusBar()->showMessage("Load G-code to begin simulation");
        }
    }
}

void MainWindow::loadSettings()
{
    QSettings settings("Vizion3D", "CNCSimulation");

    // Load window geometry
    restoreGeometry(settings.value("geometry").toByteArray());
    restoreState(settings.value("windowState").toByteArray());

    // Load recent files
    // TODO: Implement recent files list

    // Load other settings
    ui->speedSlider->setValue(settings.value("simulationSpeed", 5).toInt());
}

void MainWindow::saveSettings()
{
    QSettings settings("Vizion3D", "CNCSimulation");

    // Save window geometry
    settings.setValue("geometry", saveGeometry());
    settings.setValue("windowState", saveState());

    // Save other settings
    settings.setValue("simulationSpeed", ui->speedSlider->value());
}

// File menu actions
void MainWindow::on_actionNew_triggered()
{
    // Check if there are unsaved changes
    if (m_gCodeEditor->document()->isModified() && !m_gCodeEditor->toPlainText().isEmpty()) {
        QMessageBox::StandardButton reply = QMessageBox::question(
            this, "Unsaved Changes",
            "You have unsaved changes. Do you want to save them before creating a new file?",
            QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel
        );

        if (reply == QMessageBox::Yes) {
            on_actionSave_triggered();
            if (m_gCodeEditor->document()->isModified()) {
                // User canceled save operation
                return;
            }
        } else if (reply == QMessageBox::Cancel) {
            return;
        }
    }

    // Clear the editor and reset the current file path
    m_gCodeEditor->clear();
    currentFilePath.clear();
    m_gCodeEditor->document()->setModified(false);
    statusBar()->showMessage("New file created", 2000);

    // Update UI to disable buttons since G-code is now empty
    updateUi();
}

void MainWindow::on_actionOpen_triggered()
{
    // Check if there are unsaved changes in the current editor buffer
    if (m_gCodeEditor->document()->isModified() && !m_gCodeEditor->toPlainText().isEmpty()) {
        QMessageBox::StandardButton reply = QMessageBox::question(
            this, "Unsaved Changes",
            "You have unsaved changes. Do you want to save them before opening a new project?",
            QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel
        );

        if (reply == QMessageBox::Yes) {
            on_actionSave_triggered();
            if (m_gCodeEditor->document()->isModified()) {
                // User cancelled the save operation
                return;
            }
        } else if (reply == QMessageBox::Cancel) {
            return;
        }
    }

    // Let the user pick a directory that represents the project root
    QString projectDirPath = QFileDialog::getExistingDirectory(this, "Open Project Folder", "");
    if (projectDirPath.isEmpty()) {
        return;
    }

    // Clear the editor since a new project is being loaded
    m_gCodeEditor->clear();
    currentFilePath.clear();
    m_gCodeEditor->document()->setModified(false);

    // Delegate loading of NC programs to the project tree widget
    m_projectTreeWidget->loadProject(projectDirPath);

    // Update the window title and status bar
    setWindowTitle(QString("Vizion3D - CNC Simulation v1.0.0 - %1").arg(QFileInfo(projectDirPath).fileName()));
    statusBar()->showMessage(QString("Opened project folder: %1").arg(projectDirPath), 2000);

    // Update UI state since editor is empty until a program is selected
    updateUi();
}

void MainWindow::on_actionSave_triggered()
{
    if (currentFilePath.isEmpty()) {
        on_actionSaveAs_triggered();
        return;
    }

    QFile file(currentFilePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::critical(this, "Error", "Could not save file: " + file.errorString());
        return;
    }

    QTextStream out(&file);
    out << m_gCodeEditor->toPlainText();
    file.close();

    m_gCodeEditor->document()->setModified(false);
    statusBar()->showMessage("Saved: " + currentFilePath);

    // Update window title to include file name
    setWindowTitle(QString("Vizion3D - CNC Simulation v1.0.0 - %1").arg(QFileInfo(currentFilePath).fileName()));
}

void MainWindow::on_actionSaveAs_triggered()
{
    QString filePath = QFileDialog::getSaveFileName(this, "Save G-code File", "", "G-code Files (*.nc *.gcode *.ngc);;All Files (*)");
    if (filePath.isEmpty()) {
        return;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::critical(this, "Error", "Could not save file: " + file.errorString());
        return;
    }

    QTextStream out(&file);
    out << m_gCodeEditor->toPlainText();
    file.close();

    currentFilePath = filePath;
    m_gCodeEditor->document()->setModified(false);
    statusBar()->showMessage("Saved: " + currentFilePath);

    // Update window title to include file name
    setWindowTitle(QString("Vizion3D - CNC Simulation v1.0.0 - %1").arg(QFileInfo(currentFilePath).fileName()));
}

void MainWindow::on_actionExit_triggered()
{
    close();
}

// Edit menu actions
void MainWindow::on_actionUndo_triggered()
{
    ui->gCodeEditor->undo();
}

void MainWindow::on_actionRedo_triggered()
{
    ui->gCodeEditor->redo();
}

void MainWindow::on_actionCut_triggered()
{
    ui->gCodeEditor->cut();
}

void MainWindow::on_actionCopy_triggered()
{
    ui->gCodeEditor->copy();
}

void MainWindow::on_actionPaste_triggered()
{
    ui->gCodeEditor->paste();
}

// Simulation menu actions
void MainWindow::on_actionStart_triggered()
{
    // Check if simulation is paused - if so, just resume it
    if (m_simulationEngine->isSimulationRunning() && m_simulationEngine->isSimulationPaused()) {
        VLOG_DEBUG("UI", "Resuming paused simulation");
        m_simulationEngine->resumeSimulation();
        updateUi();
        return;
    }

    // Check if we're transitioning from stepping mode to automatic mode
    if (m_simulationEngine->isSimulationRunning() && m_simulationEngine->isSteppingMode()) {
        VLOG_DEBUG("UI", "Transitioning from stepping mode to automatic simulation");
        // Don't reset anything, just start automatic simulation from current position
        double timeStep = 0.1; // Default time step
        auto startResult = m_simulationEngine->startSimulation(timeStep);
        if (!startResult.isSuccess()) {
            const auto& error = startResult.error();
            VLOG_ERROR("UI", QString("Failed to transition to automatic simulation: %1").arg(error.message));
            QMessageBox::critical(this, "Error", QString("Failed to start automatic simulation: %1").arg(error.message));
            return;
        }
        updateUi();
        return;
    }

    // Parse G-code for fresh start (only when not transitioning from stepping mode)
    QString gcode = m_gCodeEditor->toPlainText();
    if (gcode.isEmpty()) {
        QMessageBox::warning(this, "Warning", "No G-code to simulate");
        return;
    }

    // Reset current line in G-code editor (only for fresh start)
    m_gCodeEditor->setCurrentLine(1);

    // Parse G-code and generate toolpath
    auto parseResult = m_simulationEngine->parseGCode(gcode);
    if (!parseResult.isSuccess()) {
        const auto& error = parseResult.error();
        VLOG_ERROR("UI", QString("Failed to parse G-code: %1").arg(error.message));
        QMessageBox::critical(this, "Error", QString("Failed to parse G-code: %1").arg(error.message));
        return;
    }

    // Check if toolpath was generated
    auto toolpathResult = m_simulationEngine->getToolpath();
    if (!toolpathResult.isSuccess()) {
        const auto& error = toolpathResult.error();
        VLOG_ERROR("UI", QString("Failed to get toolpath: %1").arg(error.message));
        QMessageBox::critical(this, "Error", QString("Failed to get toolpath: %1").arg(error.message));
        return;
    }

    const QVector<Vizion3D::Engine::Interfaces::ToolpathPoint>& toolpath = toolpathResult.value();
    if (toolpath.isEmpty()) {
        VLOG_WARNING("UI", "Toolpath is empty after parsing G-code");
        QMessageBox::critical(this, "Error", "No toolpath was generated from the G-code");
        return;
    }

    VLOG_DEBUG("UI", QString("Generated toolpath with %1 points").arg(toolpath.size()));

    // Start simulation
    double timeStep = 0.1; // Default time step
    auto startResult = m_simulationEngine->startSimulation(timeStep);
    if (!startResult.isSuccess()) {
        const auto& error = startResult.error();
        VLOG_ERROR("UI", QString("Failed to start simulation: %1").arg(error.message));
        QMessageBox::critical(this, "Error", QString("Failed to start simulation: %1").arg(error.message));
        return;
    }

    updateUi();
}

void MainWindow::on_actionPause_triggered()
{
    m_simulationEngine->pauseSimulation();
    updateUi();
}

void MainWindow::on_actionStop_triggered()
{
    VLOG_DEBUG("UI", "Reset button clicked - resetting simulation to beginning");
    m_simulationEngine->resetSimulation();

    // Reset current line in G-code editor to line 1
    m_gCodeEditor->setCurrentLine(1);

    updateUi();
}

void MainWindow::on_actionStep_triggered()
{
    // Check if we have G-code to step through
    QString gcode = m_gCodeEditor->toPlainText();
    if (gcode.isEmpty()) {
        QMessageBox::warning(this, "Warning", "No G-code to step through");
        return;
    }

    // If simulation is not running and not paused, we need to parse G-code first
    if (!m_simulationEngine->isSimulationRunning() && !m_simulationEngine->isSimulationPaused()) {
        VLOG_DEBUG("UI", "Parsing G-code for stepping from beginning");

        // Parse G-code and generate toolpath (same as Start button)
        auto parseResult = m_simulationEngine->parseGCode(gcode);
        if (!parseResult.isSuccess()) {
            const auto& error = parseResult.error();
            VLOG_ERROR("UI", QString("Failed to parse G-code for stepping: %1").arg(error.message));
            QMessageBox::critical(this, "Error", QString("Failed to parse G-code: %1").arg(error.message));
            return;
        }

        // Check if toolpath was generated (same as Start button)
        auto toolpathResult = m_simulationEngine->getToolpath();
        if (!toolpathResult.isSuccess()) {
            const auto& error = toolpathResult.error();
            VLOG_ERROR("UI", QString("Failed to get toolpath for stepping: %1").arg(error.message));
            QMessageBox::critical(this, "Error", QString("Failed to get toolpath: %1").arg(error.message));
            return;
        }

        const QVector<Vizion3D::Engine::Interfaces::ToolpathPoint>& toolpath = toolpathResult.value();
        if (toolpath.isEmpty()) {
            VLOG_WARNING("UI", "Toolpath is empty after parsing G-code for stepping");
            QMessageBox::critical(this, "Error", "No toolpath was generated from the G-code");
            return;
        }

        VLOG_DEBUG("UI", QString("Generated toolpath with %1 points for stepping").arg(toolpath.size()));

        // Reset current line in G-code editor to line 1 (same as Start button)
        m_gCodeEditor->setCurrentLine(1);

        VLOG_DEBUG("UI", "G-code parsed successfully for stepping - ready to step");
    }

    // Now perform the step (this will handle initialization if needed)
    VLOG_DEBUG("UI", "Performing simulation step");
    m_simulationEngine->stepSimulation();
    updateUi();
}

// View menu actions
void MainWindow::on_actionTopView_triggered()
{
    if (m_simulationView) {
        m_simulationView->setView("top");
        statusBar()->showMessage("Top view", 2000);
    }
}

void MainWindow::on_actionFrontView_triggered()
{
    if (m_simulationView) {
        m_simulationView->setView("front");
        statusBar()->showMessage("Front view", 2000);
    }
}

void MainWindow::on_actionSideView_triggered()
{
    if (m_simulationView) {
        m_simulationView->setView("side");
        statusBar()->showMessage("Side view", 2000);
    }
}

void MainWindow::on_actionIsometricView_triggered()
{
    if (m_simulationView) {
        m_simulationView->setView("isometric");
        statusBar()->showMessage("Isometric view", 2000);
    }
}

void MainWindow::on_actionZoomIn_triggered()
{
    // Simulate a wheel event with positive delta
    if (m_simulationView) {
        QWheelEvent wheelEvent(
            QPointF(m_simulationView->width() / 2.0, m_simulationView->height() / 2.0),
            m_simulationView->mapToGlobal(QPoint(m_simulationView->width() / 2, m_simulationView->height() / 2)),
            QPoint(0, 0),
            QPoint(0, 120), // Positive delta for zoom in
            Qt::NoButton,
            Qt::NoModifier,
            Qt::ScrollBegin,
            false
        );
        QApplication::sendEvent(m_simulationView, &wheelEvent);
        statusBar()->showMessage("Zoomed in", 2000);
    }
}

void MainWindow::on_actionZoomOut_triggered()
{
    // Simulate a wheel event with negative delta
    if (m_simulationView) {
        QWheelEvent wheelEvent(
            QPointF(m_simulationView->width() / 2.0, m_simulationView->height() / 2.0),
            m_simulationView->mapToGlobal(QPoint(m_simulationView->width() / 2, m_simulationView->height() / 2)),
            QPoint(0, 0),
            QPoint(0, -120), // Negative delta for zoom out
            Qt::NoButton,
            Qt::NoModifier,
            Qt::ScrollBegin,
            false
        );
        QApplication::sendEvent(m_simulationView, &wheelEvent);
        statusBar()->showMessage("Zoomed out", 2000);
    }
}

void MainWindow::on_actionZoomFit_triggered()
{
    if (m_simulationView) {
        m_simulationView->fitView();
        statusBar()->showMessage("View fit to content", 2000);
    }
}

// Help menu actions
void MainWindow::on_actionAbout_triggered()
{
    QMessageBox::about(this, "About Vizion3D",
        "Vizion3D - CNC Simulation\n"
        "Version 1.0.0\n\n"
        "A high-performance CNC simulation application.\n\n"
        "© 2025 Vizion3D"
    );
}

void MainWindow::on_actionDocumentation_triggered()
{
    // TODO: Implement documentation viewer
    QMessageBox::information(this, "Documentation", "Documentation will be available in a future version.");
}

// Settings menu actions
void MainWindow::on_actionSettings_triggered()
{
    // TODO: Implement settings dialog
    QMessageBox::information(this, "Settings", "Settings dialog will be available in a future version.");
}

void MainWindow::on_actionLoggingSettings_triggered()
{
    VLOG_DEBUG("UI", "Opening logging settings dialog");

    // Create the dialog if it doesn't exist
    if (!m_logSettingsDialog) {
        m_logSettingsDialog = new Vizion3D::UI::LogSettingsDialog(this); // NOLINT(cppcoreguidelines-owning-memory) - Qt parent-child ownership
    }

    // Show the dialog
    m_logSettingsDialog->exec();
}

void MainWindow::on_actionDebugConsole_triggered()
{
    VLOG_DEBUG("UI", "Opening debug console");

    // Create the debug console widget if it doesn't exist
    if (!m_debugConsoleWidget) {
        m_debugConsoleWidget = new Vizion3D::UI::DebugConsoleWidget(this); // NOLINT(cppcoreguidelines-owning-memory) - Qt parent-child ownership
        VLOG_INFO("UI", "Debug console widget created");
    }

    // Create a dock widget for the debug console if it's not already in a dock
    if (!m_debugConsoleWidget->parent() || m_debugConsoleWidget->parent() == this) {
        auto* dockWidget = new QDockWidget("Debug Console", this); // NOLINT(cppcoreguidelines-owning-memory) - Qt parent-child ownership
        dockWidget->setWidget(m_debugConsoleWidget);
        dockWidget->setAllowedAreas(Qt::AllDockWidgetAreas);
        addDockWidget(Qt::BottomDockWidgetArea, dockWidget);

        // Log that the debug console was opened
        VLOG_INFO("UI", "Debug console opened in dock widget");
    } else {
        // If the debug console is already in a dock, just make sure it's visible
        auto* dockWidget = qobject_cast<QDockWidget*>(m_debugConsoleWidget->parent());
        if (dockWidget) {
            dockWidget->show();
            dockWidget->raise();
            VLOG_INFO("UI", "Existing debug console dock widget shown");
        }
    }
}

// Button click handlers
void MainWindow::on_startButton_clicked()
{
    on_actionStart_triggered();
}

void MainWindow::on_pauseButton_clicked()
{
    on_actionPause_triggered();
}

void MainWindow::on_stopButton_clicked()
{
    on_actionStop_triggered();
}

void MainWindow::on_stepButton_clicked()
{
    on_actionStep_triggered();
}

void MainWindow::on_speedSlider_valueChanged(int value)
{
    // Convert slider value (1-10) to simulation speed (0.1-2.0)
    double speed = value / 5.0;
    m_simulationEngine->setSimulationSpeed(speed);
    VLOG_DEBUG("UI", QString("Simulation speed changed to: %1").arg(speed));
}

// Signal handlers for simulation engine
void MainWindow::onSimulationStarted()
{
    VLOG_DEBUG("UI", "Simulation started");
    updateUi();
}

void MainWindow::onSimulationPaused()
{
    VLOG_DEBUG("UI", "Simulation paused");
    updateUi();
}

void MainWindow::onSimulationResumed()
{
    VLOG_DEBUG("UI", "Simulation resumed");
    updateUi();
}

void MainWindow::onSimulationStopped()
{
    VLOG_DEBUG("UI", "Simulation stopped");
    updateUi();
}

void MainWindow::onSimulationCompleted()
{
    VLOG_DEBUG("UI", "Simulation completed");
    updateUi();
    QMessageBox::information(this, "Simulation", "Simulation completed successfully");
}

void MainWindow::onSimulationError(const Vizion3D::Utils::Error& error)
{
    VLOG_ERROR("UI", QString("Simulation error: %1").arg(error.message));
    VLOG_DEBUG("UI", QString("Simulation error: %1").arg(error.message));
    updateUi();
    QMessageBox::critical(this, "Simulation Error", error.message);
}

void MainWindow::onParsingCompleted()
{
    VLOG_DEBUG("UI", "G-code parsing completed");
    statusBar()->showMessage("G-code parsing completed");
}

void MainWindow::onParsingError(const Vizion3D::Utils::Error& error)
{
    VLOG_ERROR("UI", QString("G-code parsing error: %1").arg(error.message));
    VLOG_DEBUG("UI", QString("G-code parsing error: %1").arg(error.message));

    // Create user-friendly error message
    QString displayMessage = error.message;
    if (!error.context.isEmpty()) {
        displayMessage = QString("%1\nContext: %2").arg(error.message, error.context);
    }

    statusBar()->showMessage(QString("G-code parsing error: %1").arg(error.message));
    QMessageBox::critical(this, "G-code Error", displayMessage);
}

void MainWindow::closeEvent(QCloseEvent* event)
{
    // Check if there are unsaved changes
    if (m_gCodeEditor->document()->isModified() && !m_gCodeEditor->toPlainText().isEmpty()) {
        QMessageBox::StandardButton reply = QMessageBox::question(
            this, "Unsaved Changes",
            "You have unsaved changes. Do you want to save them before exiting?",
            QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel
        );

        if (reply == QMessageBox::Yes) {
            on_actionSave_triggered();
            if (m_gCodeEditor->document()->isModified()) {
                // User canceled save operation
                event->ignore();
                return;
            }
        } else if (reply == QMessageBox::Cancel) {
            event->ignore();
            return;
        }
    }

    // Save settings before closing
    saveSettings();
    event->accept();
}
