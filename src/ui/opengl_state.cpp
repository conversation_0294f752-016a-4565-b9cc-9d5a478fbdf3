#include "ui/opengl_state.h"
#include "utils/logger.h"
#include <QDebug>

namespace Vizion3D {
namespace UI {

// Initialize static member
OpenGLState* OpenGLState::s_instance = nullptr;

OpenGLState::OpenGLState()
    : m_depthTestEnabled(false)
    , m_cullFaceEnabled(false)
    , m_blendingEnabled(false)
    , m_lineWidth(1.0f)
    , m_pointSize(1.0f)
    , m_color(1.0f, 1.0f, 1.0f, 1.0f)
{
    initializeOpenGLFunctions();
}

OpenGLState& OpenGLState::instance()
{
    if (!s_instance) {
        s_instance = new OpenGLState();
    }
    return *s_instance;
}

void OpenGLState::initialize()
{
    // Set default state
    glClearColor(0.15f, 0.15f, 0.15f, 1.0f);

    enableDepthTest();
    enableCullFace();
    enableBlending();

    setLineWidth(1.0f);
    setPointSize(1.0f);

    // Note: glColor4f is removed as it's not available in Core Profile
    // Color setting should be done through shaders
    m_color = QVector4D(1.0f, 1.0f, 1.0f, 1.0f);

    // Note: GL_POINT_SMOOTH is deprecated in Core Profile and removed
    // Point smoothing should be done through shaders

    VLOG_DEBUG("OpenGL", "OpenGLState initialized");
}

void OpenGLState::enableDepthTest(GLenum depthFunc)
{
    if (!m_depthTestEnabled) {
        glEnable(GL_DEPTH_TEST);
        m_depthTestEnabled = true;
    }

    glDepthFunc(depthFunc);
}

void OpenGLState::disableDepthTest()
{
    if (m_depthTestEnabled) {
        glDisable(GL_DEPTH_TEST);
        m_depthTestEnabled = false;
    }
}

void OpenGLState::enableCullFace(GLenum mode)
{
    if (!m_cullFaceEnabled) {
        glEnable(GL_CULL_FACE);
        m_cullFaceEnabled = true;
    }

    glCullFace(mode);
}

void OpenGLState::disableCullFace()
{
    if (m_cullFaceEnabled) {
        glDisable(GL_CULL_FACE);
        m_cullFaceEnabled = false;
    }
}

void OpenGLState::enableBlending(GLenum sfactor, GLenum dfactor)
{
    if (!m_blendingEnabled) {
        glEnable(GL_BLEND);
        m_blendingEnabled = true;
    }

    glBlendFunc(sfactor, dfactor);
}

void OpenGLState::disableBlending()
{
    if (m_blendingEnabled) {
        glDisable(GL_BLEND);
        m_blendingEnabled = false;
    }
}

void OpenGLState::setLineWidth(float width)
{
    if (m_lineWidth != width) {
        glLineWidth(width);
        m_lineWidth = width;
    }
}

void OpenGLState::setPointSize(float size)
{
    if (m_pointSize != size) {
        glPointSize(size);
        m_pointSize = size;
    }
}

void OpenGLState::setColor(const QVector3D& color)
{
    setColor(color.x(), color.y(), color.z());
}

void OpenGLState::setColor(const QVector4D& color)
{
    setColor(color.x(), color.y(), color.z(), color.w());
}

void OpenGLState::setColor(float r, float g, float b, float a)
{
    // In Core Profile, we don't use glColor4f as it's part of the fixed-function pipeline
    // Instead, we just store the color value for use in shaders
    if (m_color.x() != r || m_color.y() != g || m_color.z() != b || m_color.w() != a) {
        m_color = QVector4D(r, g, b, a);
    }
}

void OpenGLState::setProjectionMatrix(const QMatrix4x4& matrix)
{
    // In Core Profile, we don't use the fixed-function matrix stack
    // Instead, we just store the matrix for use in shaders
    if (m_projectionMatrix != matrix) {
        m_projectionMatrix = matrix;
    }
}

void OpenGLState::setModelViewMatrix(const QMatrix4x4& matrix)
{
    // In Core Profile, we don't use the fixed-function matrix stack
    // Instead, we just store the matrix for use in shaders
    if (m_modelViewMatrix != matrix) {
        m_modelViewMatrix = matrix;
    }
}

void OpenGLState::clear(const QVector4D& color)
{
    glClearColor(color.x(), color.y(), color.z(), color.w());
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
}

} // namespace UI
} // namespace Vizion3D
