#include "ui/renderer.h"
#include "utils/logger.h"
#include <QDebug>
#include <QFile>
#include <QFileInfo>
#include <QOpenGLContext>

namespace Vizion3D {
namespace UI {

Renderer::Renderer()
{
    initializeOpenGLFunctions();
}

Renderer::~Renderer()
{
    // Base class destructor - subclasses should handle their own cleanup
    if (m_vao.isCreated()) {
        m_vao.destroy();
    }
}

bool Renderer::createAndBindVAO(QOpenGLVertexArrayObject& vao)
{
    // Create and bind a Vertex Array Object (VAO)
    // This is required in OpenGL Core Profile before setting up vertex attributes

    // Check if we have a valid OpenGL context
    QOpenGLContext* context = QOpenGLContext::currentContext();
    if (!context) {
        VLOG_ERROR("OpenGL", "No current OpenGL context when creating VAO");
        return false;
    }

    // Clear any existing OpenGL errors before proceeding
    while (glGetError() != GL_NO_ERROR) {
        // Clear error queue
    }

    // Check if VAO is already created
    if (vao.isCreated()) {
        VLOG_DEBUG("OpenGL", "VAO is already created, binding existing VAO");
        vao.bind();
        return true;
    }

    // Create a new VAO
    if (!vao.create()) {
        VLOG_ERROR("OpenGL", "Failed to create Vertex Array Object");
        return false;
    }

    // Bind the VAO
    vao.bind();

    VLOG_DEBUG("OpenGL", "Successfully created and bound Vertex Array Object");
    return true;
}

QOpenGLShaderProgram* Renderer::createShaderProgram(const QString& vertexShaderSource, const QString& fragmentShaderSource)
{
    QOpenGLShaderProgram* program = new QOpenGLShaderProgram();

    // Add vertex shader
    if (!program->addShaderFromSourceCode(QOpenGLShader::Vertex, vertexShaderSource)) {
        VLOG_ERROR("OpenGL", QString("Failed to compile vertex shader: %1").arg(program->log()));
        delete program;
        return nullptr;
    }

    // Add fragment shader
    if (!program->addShaderFromSourceCode(QOpenGLShader::Fragment, fragmentShaderSource)) {
        VLOG_ERROR("OpenGL", QString("Failed to compile fragment shader: %1").arg(program->log()));
        delete program;
        return nullptr;
    }

    // Link shader program
    if (!program->link()) {
        VLOG_ERROR("OpenGL", QString("Failed to link shader program: %1").arg(program->log()));
        delete program;
        return nullptr;
    }

    return program;
}

QString Renderer::loadShaderSource(const QString& filePath)
{
    // Try loading from Qt resource system first
    if (!filePath.startsWith(":/")) {
        // If the path doesn't start with ":/", try with it
        QString resourcePath = ":/" + filePath;
        QFile resourceFile(resourcePath);
        if (resourceFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
            QString source = resourceFile.readAll();
            resourceFile.close();
            VLOG_DEBUG("OpenGL", QString("Successfully loaded shader from Qt resource: %1").arg(resourcePath));
            return source;
        }
    }

    // Try loading directly from the provided path
    QFile file(filePath);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QString source = file.readAll();
        file.close();
        VLOG_DEBUG("OpenGL", QString("Successfully loaded shader from file system: %1").arg(filePath));
        return source;
    }

    VLOG_DEBUG("OpenGL", QString("Failed to open shader file: %1").arg(filePath));

    // Try alternative paths
    QStringList alternativePaths;

    // If path starts with ":/shaders/", try without the prefix
    if (filePath.startsWith(":/shaders/")) {
        alternativePaths << filePath.mid(10); // Remove ":/shaders/"
    }

    // Try with resources/shaders/ prefix
    if (!filePath.startsWith("resources/shaders/")) {
        QString fileName = QFileInfo(filePath).fileName();
        alternativePaths << "resources/shaders/" + fileName;
    }

    // Try with just shaders/ prefix
    if (!filePath.startsWith("shaders/")) {
        QString fileName = QFileInfo(filePath).fileName();
        alternativePaths << "shaders/" + fileName;
    }

    // Try with just the filename
    QString fileName = QFileInfo(filePath).fileName();
    alternativePaths << fileName;

    // Try each alternative path
    for (const QString& altPath : alternativePaths) {
        QFile altFile(altPath);
        if (altFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
            QString source = altFile.readAll();
            altFile.close();
            VLOG_DEBUG("OpenGL", QString("Successfully loaded shader from alternative path: %1").arg(altPath));
            return source;
        }
        VLOG_DEBUG("OpenGL", QString("Failed to open shader file at alternative path: %1").arg(altPath));
    }

    return QString();
}

void Renderer::deleteShaderProgram(QOpenGLShaderProgram* program)
{
    if (program) {
        program->release();
        delete program;
    }
}

} // namespace UI
} // namespace Vizion3D
