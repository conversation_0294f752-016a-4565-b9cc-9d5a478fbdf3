#include "ui/simulation_view.h"
#include "ui/opengl_state.h"
#include "engine/interfaces/simulation_engine_interface.h"
#include "engine/interfaces/toolpath_point.h"
#include "utils/logger.h"

#include <QMouseEvent>
#include <QWheelEvent>
#include <QOpenGLShaderProgram>
#include <QOpenGLBuffer>
#include <QOpenGLVertexArrayObject>
#include <QDebug>
#include <QCoreApplication>
#include <QTime>
#include <QMessageBox>
#include <QElapsedTimer>
#include <QDateTime>
#include <cmath>

namespace Vizion3D {
namespace UI {

/**
 * @brief Shows a diagnostic message box with a timestamp and step number
 *
 * @param step The step number in the initialization sequence
 * @param message The message to display
 * @param elapsedTimer Timer to track elapsed time since SimulationView construction started
 */
void logSimulationViewDiagnosticMessage(int step, const QString& message, const QElapsedTimer& elapsedTimer) {
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    QString elapsedMs = QString::number(elapsedTimer.elapsed());
    QString fullMessage = QString("[%1] SimulationView Step %2 (%3 ms): %4")
                              .arg(timestamp)
                              .arg(step)
                              .arg(elapsedMs)
                              .arg(message);

    VLOG_DEBUG("UI", QString("SIMULATIONVIEW DIAGNOSTIC: %1").arg(fullMessage));
}

SimulationView::SimulationView(QWidget* parent)
    : QOpenGLWidget(parent)
    , m_lastMousePos(0, 0)
    , m_mousePressed(false)
    , m_showGrid(true)
    , m_showAxes(true)
    , m_showToolpath(true)
    , m_simulationActive(false)
    , m_currentPosition(0, 0, 0)
{
    // Start a timer to track elapsed time
    QElapsedTimer viewTimer;
    viewTimer.start();

    logSimulationViewDiagnosticMessage(1, "SimulationView constructor started", viewTimer);

    // Set focus policy to enable keyboard input
    VLOG_DEBUG("UI", "Setting focus policy...");
    setFocusPolicy(Qt::StrongFocus);

    // Enable mouse tracking for better interaction
    VLOG_DEBUG("UI", "Enabling mouse tracking...");
    setMouseTracking(true);

    VLOG_DEBUG("UI", QString("SimulationView created with camera position: %1 %2 %3 and target: %4 %5 %6 and up vector: %7 %8 %9")
               .arg(m_camera.position().x())
               .arg(m_camera.position().y())
               .arg(m_camera.position().z())
               .arg(m_camera.target().x())
               .arg(m_camera.target().y())
               .arg(m_camera.target().z())
               .arg(m_camera.up().x())
               .arg(m_camera.up().y())
               .arg(m_camera.up().z()));

    logSimulationViewDiagnosticMessage(2, "SimulationView constructor completed", viewTimer);
}

void SimulationView::connectToEngine(Engine::Interfaces::ISimulationEngine* engine)
{
    VLOG_DEBUG("UI", "==== SimulationView::connectToEngine CALLED ====");

    // Disconnect any existing connections
    for (const QMetaObject::Connection& connection : m_connections) {
        disconnect(connection);
    }

    m_connections.clear();

    // Connect to engine signals
    if (engine) {
        VLOG_DEBUG("UI", "Connecting to engine signals");

        m_connections << connect(engine, &Engine::Interfaces::ISimulationEngine::simulationStep,
                                this, &SimulationView::onSimulationStep);
        m_connections << connect(engine, &Engine::Interfaces::ISimulationEngine::simulationStarted,
                                this, &SimulationView::onSimulationStarted);
        m_connections << connect(engine, &Engine::Interfaces::ISimulationEngine::simulationStopped,
                                this, &SimulationView::onSimulationStopped);
        m_connections << connect(engine, &Engine::Interfaces::ISimulationEngine::simulationCompleted,
                                this, &SimulationView::onSimulationCompleted);

        VLOG_DEBUG("UI", "Connected to engine signals successfully");

        // Clear any existing toolpath data
        m_toolpathPoints.clear();
        m_toolpathColors.clear();

        // Reset current position to origin
        m_currentPosition = QVector3D(0, 0, 0);

        VLOG_DEBUG("UI", "Cleared toolpath data and reset position to origin");

        // Update view
        update();
    } else {
        VLOG_DEBUG("UI", "No engine provided, not connecting signals");
    }

    VLOG_DEBUG("UI", "==== SimulationView::connectToEngine COMPLETED ====");
}

SimulationView::~SimulationView()
{
    // Disconnect all signal connections
    for (const QMetaObject::Connection& connection : m_connections) {
        disconnect(connection);
    }
    m_connections.clear();

    // Clean up OpenGL resources
    makeCurrent();

    // Release OpenGL resources here

    doneCurrent();
}

void SimulationView::initializeGL()
{
    // Start a timer to track elapsed time
    QElapsedTimer glTimer;
    glTimer.start();

    VLOG_DEBUG("UI", "==== SimulationView::initializeGL CALLED ====");
    VLOG_DEBUG("UI", QString("Widget size: %1x%2").arg(width()).arg(height()));

    logSimulationViewDiagnosticMessage(201, "initializeGL started", glTimer);

    try {
        // Initialize OpenGL functions
        VLOG_DEBUG("OpenGL", "Initializing OpenGL functions...");
        logSimulationViewDiagnosticMessage(202, "About to initialize OpenGL functions", glTimer);
        if (!initializeOpenGLFunctions()) {
            QString errorMsg = "Failed to initialize OpenGL functions! This may indicate that OpenGL 4.1 Core Profile is not available.";
            VLOG_ERROR("OpenGL", errorMsg);
            QMessageBox::critical(nullptr, "OpenGL Error", errorMsg);
            return;
        }
        VLOG_DEBUG("OpenGL", "OpenGL functions initialized successfully");
        logSimulationViewDiagnosticMessage(203, "OpenGL functions initialized successfully", glTimer);

        // Get OpenGL version information
        logSimulationViewDiagnosticMessage(204, "About to get OpenGL version information", glTimer);
        QString glVersion = reinterpret_cast<const char*>(glGetString(GL_VERSION)); // NOLINT(cppcoreguidelines-pro-type-reinterpret-cast) - OpenGL API requirement
        QString glRenderer = reinterpret_cast<const char*>(glGetString(GL_RENDERER)); // NOLINT(cppcoreguidelines-pro-type-reinterpret-cast) - OpenGL API requirement
        QString glVendor = reinterpret_cast<const char*>(glGetString(GL_VENDOR)); // NOLINT(cppcoreguidelines-pro-type-reinterpret-cast) - OpenGL API requirement
        QString glShadingLanguageVersion = reinterpret_cast<const char*>(glGetString(GL_SHADING_LANGUAGE_VERSION)); // NOLINT(cppcoreguidelines-pro-type-reinterpret-cast) - OpenGL API requirement

        VLOG_DEBUG("OpenGL", QString("OpenGL Version: %1").arg(glVersion));
        VLOG_DEBUG("OpenGL", QString("OpenGL Renderer: %1").arg(glRenderer));
        VLOG_DEBUG("OpenGL", QString("OpenGL Vendor: %1").arg(glVendor));
        VLOG_DEBUG("OpenGL", QString("GLSL Version: %1").arg(glShadingLanguageVersion));
        logSimulationViewDiagnosticMessage(205, "Got OpenGL version: " + glVersion, glTimer);

        // Check if we have a valid OpenGL context
        if (glVersion.isEmpty() || glRenderer.isEmpty() || glVendor.isEmpty()) {
            QString errorMsg = "Invalid OpenGL context. Could not retrieve OpenGL version information.";
            VLOG_ERROR("OpenGL", errorMsg);
            QMessageBox::critical(nullptr, "OpenGL Error", errorMsg);
            return;
        }

        // Set the viewport to cover the entire widget
        logSimulationViewDiagnosticMessage(206, "About to set viewport", glTimer);
        int devicePixelRatio = static_cast<int>(this->devicePixelRatio());
        glViewport(0, 0, width() * devicePixelRatio, height() * devicePixelRatio);

        VLOG_DEBUG("OpenGL", QString("Device pixel ratio: %1").arg(devicePixelRatio));
        VLOG_DEBUG("OpenGL", QString("Setting viewport to: %1x%2").arg(width() * devicePixelRatio).arg(height() * devicePixelRatio));
        logSimulationViewDiagnosticMessage(207, "Viewport set", glTimer);

        // Set clear color to dark gray for better contrast
        logSimulationViewDiagnosticMessage(208, "About to set clear color and enable GL features", glTimer);
        glClearColor(0.15f, 0.15f, 0.15f, 1.0f);

        // Enable depth testing
        VLOG_DEBUG("OpenGL", "Enabling depth testing...");
        glEnable(GL_DEPTH_TEST);
        glDepthFunc(GL_LESS);

        // Enable backface culling
        VLOG_DEBUG("OpenGL", "Enabling backface culling...");
        glEnable(GL_CULL_FACE);
        glCullFace(GL_BACK);

        // Enable blending
        VLOG_DEBUG("OpenGL", "Enabling blending...");
        glEnable(GL_BLEND);
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
        logSimulationViewDiagnosticMessage(209, "GL features enabled", glTimer);

        // Initialize renderers
        logSimulationViewDiagnosticMessage(210, "About to initialize renderers", glTimer);
        try {
            VLOG_DEBUG("OpenGL", "Initializing grid renderer...");
            m_gridRenderer.initialize();
            logSimulationViewDiagnosticMessage(211, "Grid renderer initialized", glTimer);
        } catch (const std::exception& e) {
            VLOG_ERROR("OpenGL", QString("Exception initializing grid renderer: %1").arg(e.what()));
            QMessageBox::warning(nullptr, "Renderer Error",
                                QString("Failed to initialize grid renderer: %1").arg(e.what()));
            // Continue with other renderers
        }

        try {
            VLOG_DEBUG("OpenGL", "Initializing axes renderer...");
            m_axesRenderer.initialize();
            logSimulationViewDiagnosticMessage(212, "Axes renderer initialized", glTimer);
        } catch (const std::exception& e) {
            VLOG_ERROR("OpenGL", QString("Exception initializing axes renderer: %1").arg(e.what()));
            QMessageBox::warning(nullptr, "Renderer Error",
                                QString("Failed to initialize axes renderer: %1").arg(e.what()));
            // Continue with other renderers
        }

        try {
            VLOG_DEBUG("OpenGL", "Initializing toolpath renderer...");
            m_toolpathRenderer.initialize();
            logSimulationViewDiagnosticMessage(213, "Toolpath renderer initialized", glTimer);
        } catch (const std::exception& e) {
            VLOG_ERROR("OpenGL", QString("Exception initializing toolpath renderer: %1").arg(e.what()));
            QMessageBox::warning(nullptr, "Renderer Error",
                                QString("Failed to initialize toolpath renderer: %1").arg(e.what()));
            // Continue with other renderers
        }

        try {
            VLOG_DEBUG("OpenGL", "Initializing position renderer...");
            m_positionRenderer.initialize();
            logSimulationViewDiagnosticMessage(214, "Position renderer initialized", glTimer);
        } catch (const std::exception& e) {
            VLOG_ERROR("OpenGL", QString("Exception initializing position renderer: %1").arg(e.what()));
            QMessageBox::warning(nullptr, "Renderer Error",
                                QString("Failed to initialize position renderer: %1").arg(e.what()));
            // Continue with other renderers
        }

        // Call fitView to set up the camera properly
        VLOG_DEBUG("OpenGL", "Setting up camera view...");
        logSimulationViewDiagnosticMessage(215, "About to set up camera view", glTimer);
        fitView();
        logSimulationViewDiagnosticMessage(216, "Camera view set up", glTimer);

        VLOG_DEBUG("OpenGL", QString("OpenGL initialized with viewport size: %1x%2 and aspect ratio: %3 and camera position: %4 %5 %6 and target: %7 %8 %9")
                   .arg(width())
                   .arg(height())
                   .arg(static_cast<float>(width()) / static_cast<float>(height()))
                   .arg(m_camera.position().x())
                   .arg(m_camera.position().y())
                   .arg(m_camera.position().z())
                   .arg(m_camera.target().x())
                   .arg(m_camera.target().y())
                   .arg(m_camera.target().z()));

        VLOG_DEBUG("OpenGL", "==== SimulationView::initializeGL COMPLETED SUCCESSFULLY ====");
        logSimulationViewDiagnosticMessage(217, "initializeGL completed successfully", glTimer);
    } catch (const std::exception& e) {
        QString errorMsg = QString("Exception during OpenGL initialization: %1").arg(e.what());
        VLOG_ERROR("OpenGL", errorMsg);
        QMessageBox::critical(nullptr, "OpenGL Error", errorMsg);
        logSimulationViewDiagnosticMessage(999, "ERROR: " + errorMsg, glTimer);
    } catch (...) {
        QString errorMsg = "Unknown exception during OpenGL initialization!";
        VLOG_ERROR("OpenGL", errorMsg);
        QMessageBox::critical(nullptr, "OpenGL Error", errorMsg);
        logSimulationViewDiagnosticMessage(999, "ERROR: " + errorMsg, glTimer);
    }
}

void SimulationView::paintGL()
{
    VLOG_DEBUG("OpenGL", "==== SimulationView::paintGL CALLED ====");

    try {
        // Log detailed information about the current state
        VLOG_DEBUG("OpenGL", QString("Widget size: %1x%2").arg(width()).arg(height()));
        VLOG_DEBUG("OpenGL", QString("Camera position: %1 %2 %3").arg(m_camera.position().x()).arg(m_camera.position().y()).arg(m_camera.position().z()));
        VLOG_DEBUG("OpenGL", QString("Camera target: %1 %2 %3").arg(m_camera.target().x()).arg(m_camera.target().y()).arg(m_camera.target().z()));
        VLOG_DEBUG("OpenGL", QString("Current position: %1 %2 %3").arg(m_currentPosition.x()).arg(m_currentPosition.y()).arg(m_currentPosition.z()));
        VLOG_DEBUG("OpenGL", QString("Toolpath points count: %1").arg(m_toolpathPoints.size()));
        VLOG_DEBUG("OpenGL", QString("Show grid: %1, Show axes: %2, Show toolpath: %3").arg(m_showGrid).arg(m_showAxes).arg(m_showToolpath));
        VLOG_DEBUG("OpenGL", QString("Simulation active: %1").arg(m_simulationActive));

        // Set the viewport to cover the entire widget
        int devicePixelRatio = static_cast<int>(this->devicePixelRatio());
        glViewport(0, 0, width() * devicePixelRatio, height() * devicePixelRatio);

        VLOG_DEBUG("UI", QString("Device pixel ratio: %1").arg(devicePixelRatio));
        VLOG_DEBUG("UI", QString("Setting viewport to: %1x%2").arg(width() * devicePixelRatio).arg(height() * devicePixelRatio));

        // Clear the screen
        VLOG_DEBUG("UI", "Clearing screen...");
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // Calculate matrices
        float aspect = static_cast<float>(width()) / static_cast<float>(height());
        QMatrix4x4 viewMatrix = m_camera.viewMatrix();
        QMatrix4x4 projectionMatrix = m_camera.projectionMatrix(aspect);

        VLOG_DEBUG("UI", QString("Viewport set to: %1x%2 with aspect ratio: %3 and FOV: %4")
                .arg(width()).arg(height()).arg(aspect).arg(m_camera.fov()));

        // Draw in order from background to foreground
        // Use OpenGLState singleton for centralized state management
        OpenGLState& glState = OpenGLState::instance();

        // First draw the grid (background)
        if (m_showGrid) {
            try {
                VLOG_DEBUG("UI", "Drawing grid");
                m_gridRenderer.render(viewMatrix, projectionMatrix);
            } catch (const std::exception& e) {
                VLOG_ERROR("OpenGL", QString("Exception rendering grid: %1").arg(e.what()));
                // Continue with other renderers
            }
        }

        // Then draw the axes
        if (m_showAxes) {
            try {
                VLOG_DEBUG("UI", "Drawing axes");
                m_axesRenderer.render(viewMatrix, projectionMatrix);
            } catch (const std::exception& e) {
                VLOG_ERROR("OpenGL", QString("Exception rendering axes: %1").arg(e.what()));
                // Continue with other renderers
            }
        }

        // Then draw the toolpath (only when simulation is active)
        if (m_showToolpath && m_simulationActive && !m_toolpathPoints.isEmpty()) {
            try {
                VLOG_DEBUG("UI", QString("Drawing toolpath with %1 points").arg(m_toolpathPoints.size()));

                // Set line width and point size for toolpath using clamped values
                float clampedLineWidth = m_toolpathRenderer.getClampedLineWidth();
                float clampedPointSize = m_toolpathRenderer.getClampedPointSize();
                glState.setLineWidth(clampedLineWidth);
                glState.setPointSize(clampedPointSize);

                m_toolpathRenderer.setPoints(m_toolpathPoints);
                m_toolpathRenderer.setColors(m_toolpathColors);
                m_toolpathRenderer.render(viewMatrix, projectionMatrix);
            } catch (const std::exception& e) {
                VLOG_ERROR("OpenGL", QString("Exception rendering toolpath: %1").arg(e.what()));
                // Continue with other renderers
            }
        } else if (m_showToolpath && !m_simulationActive && !m_toolpathPoints.isEmpty()) {
            VLOG_DEBUG("UI", "Skipping toolpath rendering: simulation not active");
        }

        // Finally draw the current position (foreground)
        try {
            VLOG_DEBUG("UI", "Drawing current position");

            // Position renderer doesn't need specific OpenGL state changes
            // It uses default line width and point size
            glState.setLineWidth(1.0f);
            glState.setPointSize(1.0f);

            m_positionRenderer.setPosition(m_currentPosition);
            m_positionRenderer.render(viewMatrix, projectionMatrix);
        } catch (const std::exception& e) {
            VLOG_ERROR("OpenGL", QString("Exception rendering position: %1").arg(e.what()));
            // Continue with other renderers
        }

        // Reset OpenGL state to defaults for next frame
        glState.setLineWidth(1.0f);
        glState.setPointSize(1.0f);

        // Force a flush of the OpenGL commands
        VLOG_DEBUG("UI", "Flushing OpenGL commands...");
        glFlush();

        VLOG_DEBUG("UI", "==== SimulationView::paintGL COMPLETED SUCCESSFULLY ====");
    } catch (const std::exception& e) {
        QString errorMsg = QString("Exception during OpenGL rendering: %1").arg(e.what());
        VLOG_ERROR("OpenGL", errorMsg);
        // Don't show a message box here as it would cause an infinite loop
        // Just log the error
    } catch (...) {
        QString errorMsg = "Unknown exception during OpenGL rendering!";
        VLOG_ERROR("OpenGL", errorMsg);
        // Don't show a message box here as it would cause an infinite loop
        // Just log the error
    }
}

void SimulationView::resizeGL(int width, int height)
{
    VLOG_DEBUG("UI", "==== SimulationView::resizeGL CALLED ====");
    VLOG_DEBUG("UI", QString("Resizing to: %1x%2").arg(width).arg(height));

    try {
        // Set the viewport to cover the entire widget
        int devicePixelRatio = static_cast<int>(this->devicePixelRatio());
        glViewport(0, 0, width * devicePixelRatio, height * devicePixelRatio);

        VLOG_DEBUG("UI", QString("Device pixel ratio: %1").arg(devicePixelRatio));
        VLOG_DEBUG("UI", QString("Setting viewport to: %1x%2").arg(width * devicePixelRatio).arg(height * devicePixelRatio));

        VLOG_DEBUG("UI", QString("Viewport resized to: %1x%2 with aspect ratio: %3 and FOV: %4 and camera position: %5 and target: %6")
                .arg(width * devicePixelRatio).arg(height * devicePixelRatio)
                .arg(static_cast<float>(width) / static_cast<float>(height))
                .arg(m_camera.fov()).arg(m_camera.position().x()).arg(m_camera.target().x()));
        VLOG_DEBUG("UI", "==== SimulationView::resizeGL COMPLETED SUCCESSFULLY ====");

        // No need to call fitView here as it would reset the camera position
        // Only call it if you want to reset the view
    } catch (const std::exception& e) {
        VLOG_ERROR("OpenGL", QString("Exception during OpenGL resize: %1").arg(e.what()));
    } catch (...) {
        VLOG_ERROR("OpenGL", "Unknown exception during OpenGL resize!");
    }
}

void SimulationView::mousePressEvent(QMouseEvent* event)
{
    m_lastMousePos = event->pos();
    m_mousePressed = true;
}

void SimulationView::mouseMoveEvent(QMouseEvent* event)
{
    if (!m_mousePressed) {
        return;
    }

    QPoint delta = event->pos() - m_lastMousePos;
    m_lastMousePos = event->pos();

    // Rotate camera
    if (event->buttons() & Qt::LeftButton) {
        // Calculate rotation angles
        float angleX = static_cast<float>(delta.y()) * 0.5f;
        float angleY = static_cast<float>(delta.x()) * 0.5f;

        // Delegate to camera class
        m_camera.orbit(angleY, angleX);

        update();
    }

    // Pan camera
    if (event->buttons() & Qt::MiddleButton) {
        // Calculate pan distances
        float panX = static_cast<float>(-delta.x()) * 0.01f;
        float panY = static_cast<float>(delta.y()) * 0.01f;

        // Delegate to camera class
        m_camera.pan(panX, panY);

        update();
    }
}

void SimulationView::mouseReleaseEvent(QMouseEvent* event)
{
    m_mousePressed = false;
}

void SimulationView::wheelEvent(QWheelEvent* event)
{
    // Zoom camera
    float zoomFactor = event->angleDelta().y() > 0 ? 0.9f : 1.1f;

    // Delegate to camera class
    m_camera.zoom(zoomFactor);

    update();
}

void SimulationView::onSimulationStep(const Engine::Interfaces::ToolpathPoint& position)
{
    VLOG_DEBUG("UI", QString("onSimulationStep: Position: %1 Move type: %2")
             .arg(position.position().x()).arg(static_cast<int>(position.moveType())));

    // If simulation is not active but we're receiving step signals, activate it
    // This handles the case where user steps without starting the simulation first
    if (!m_simulationActive) {
        VLOG_DEBUG("UI", "SimulationView: Activating simulation rendering for stepping mode");
        m_simulationActive = true;

        // Clear any existing toolpath points for fresh start
        m_toolpathPoints.clear();
        m_toolpathColors.clear();

        // Reset current position to origin
        m_currentPosition = QVector3D(0, 0, 0);
    }

    // Update current position with detailed logging
    QVector3D oldPosition = m_currentPosition;
    m_currentPosition = position.position();

    VLOG_DEBUG("UI", QString("Tool position updated from %1 to %2").arg(oldPosition.x()).arg(m_currentPosition.x()));

    // Check if this is a new point with a small tolerance
    // This is more reliable than exact comparison for floating point values
    const float tolerance = 0.001f;
    bool pointExists = false;

    for (int i = 0; i < m_toolpathPoints.size(); ++i) {
        const QVector3D& existingPoint = m_toolpathPoints[i];
        float distance = (existingPoint - position.position()).length();

        if (distance < tolerance) {
            pointExists = true;
            break;
        }
    }

    if (!pointExists) {
        // Add the new point to the toolpath
        m_toolpathPoints.append(position.position());

        // Set color based on move type
        QVector3D color;
        switch (position.moveType()) {
            case Engine::Interfaces::MoveType::Rapid:
                color = QVector3D(1.0f, 0.0f, 0.0f); // Red for rapid moves
                break;
            case Engine::Interfaces::MoveType::Linear:
                color = QVector3D(0.0f, 1.0f, 0.0f); // Green for linear moves
                break;
            case Engine::Interfaces::MoveType::ArcCW:
            case Engine::Interfaces::MoveType::ArcCCW:
                color = QVector3D(0.0f, 0.0f, 1.0f); // Blue for arc moves
                break;
            default:
                color = QVector3D(1.0f, 1.0f, 0.0f); // Yellow for unknown moves
                break;
        }
        m_toolpathColors.append(color);

        VLOG_DEBUG("UI", QString("Added new point to toolpath, total points: %1").arg(m_toolpathPoints.size()));
    }

    // Force a redraw of the OpenGL view
    update();

    // Ensure the update is processed immediately
    QCoreApplication::processEvents();
}

void SimulationView::onSimulationStarted()
{
    VLOG_DEBUG("UI", "SimulationView: Simulation started - activating simulation rendering");

    // Set simulation as active
    m_simulationActive = true;

    // Clear any existing toolpath points for fresh start
    m_toolpathPoints.clear();
    m_toolpathColors.clear();

    // Reset current position to origin
    m_currentPosition = QVector3D(0, 0, 0);

    // Update view
    update();
}

void SimulationView::onSimulationStopped()
{
    VLOG_DEBUG("UI", "SimulationView: Simulation stopped - deactivating simulation rendering and clearing toolpath");

    // Set simulation as inactive
    m_simulationActive = false;

    // Clear toolpath data to remove visualization
    m_toolpathPoints.clear();
    m_toolpathColors.clear();

    // Reset current position to origin
    m_currentPosition = QVector3D(0, 0, 0);

    // Update view
    update();
}

void SimulationView::onSimulationCompleted()
{
    VLOG_DEBUG("UI", "SimulationView: Simulation completed - keeping simulation rendering active to show final result");

    // Keep simulation active so user can see the final toolpath result
    // m_simulationActive remains true

    // Update view
    update();
}

void SimulationView::setView(const QString& view)
{
    VLOG_DEBUG("UI", QString("Setting view to: %1").arg(view));

    // Set the camera view using the Camera class
    if (view == "top") {
        m_camera.setTopView();
    } else if (view == "front") {
        m_camera.setFrontView();
    } else if (view == "side") {
        m_camera.setSideView();
    } else if (view == "isometric") {
        m_camera.setIsometricView();
    }

    VLOG_DEBUG("UI", QString("View set to: %1 with camera position: %2 and target: %3")
             .arg(view).arg(m_camera.position().x()).arg(m_camera.target().x()));

    update();
}

void SimulationView::resetCamera()
{
    VLOG_DEBUG("UI", "Resetting camera");

    // Reset the camera using the Camera class
    m_camera.reset();

    VLOG_DEBUG("UI", QString("Camera reset to position: %1 and target: %2 and up vector: %3 with FOV: %4")
             .arg(m_camera.position().x()).arg(m_camera.target().x()).arg(m_camera.up().x()).arg(m_camera.fov()));

    update();
}

void SimulationView::fitView()
{
    VLOG_DEBUG("UI", "Fitting view to content");

    // Calculate the center of our toolpath square (0,0,0 to 25,25,0)
    QVector3D center(12.5f, 0.0f, 12.5f);

    // Set the camera to isometric view centered on the toolpath
    m_camera.setIsometricView(center);

    VLOG_DEBUG("UI", QString("View fitted with camera position: %1 and target: %2 and FOV: %3")
             .arg(m_camera.position().x()).arg(m_camera.target().x()).arg(m_camera.fov()));

    update();
}

} // namespace UI
} // namespace Vizion3D
