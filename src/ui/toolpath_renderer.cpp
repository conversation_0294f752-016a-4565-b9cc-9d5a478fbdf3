#include "ui/toolpath_renderer.h"
#include "utils/logger.h"
#include <QDebug>
#include <QFile>
#include <QOpenGLContext>

namespace Vizion3D {
namespace UI {

ToolpathRenderer::ToolpathRenderer()
    : m_lineWidth(2.0f)
    , m_pointSize(5.0f)
    , m_showPoints(true)
    , m_shaderProgram(nullptr)
    , m_initialized(false)
    , m_needsUpdate(true)
    , m_lineVertexVBO(QOpenGLBuffer::VertexBuffer)
    , m_lineColorVBO(QOpenGLBuffer::VertexBuffer)
    , m_pointVertexVBO(QOpenGLBuffer::VertexBuffer)
    , m_pointColorVBO(QOpenGLBuffer::VertexBuffer)
    , m_minLineWidth(1.0f)
    , m_maxLineWidth(1.0f)
    , m_minPointSize(1.0f)
    , m_maxPointSize(1.0f)
    , m_rangesCached(false)
{
    // Don't create OpenGL resources in constructor
    // They will be created in initialize() when we have a valid OpenGL context
}

ToolpathRenderer::~ToolpathRenderer()
{
    // Clean up OpenGL resources
    if (m_initialized) {
        // Clean up line buffers
        m_lineVAO.destroy();
        m_lineVertexVBO.destroy();
        m_lineColorVBO.destroy();

        // Clean up point buffers
        m_pointVAO.destroy();
        m_pointVertexVBO.destroy();
        m_pointColorVBO.destroy();

        // Delete shader program
        deleteShaderProgram(m_shaderProgram);
    }
}

void ToolpathRenderer::initialize()
{
    VLOG_DEBUG("OpenGL", "ToolpathRenderer::initialize called");

    // Check if already initialized to prevent duplicate initialization
    if (m_initialized) {
        VLOG_DEBUG("OpenGL", "ToolpathRenderer already initialized, skipping initialization");
        return;
    }

    // Ensure we have a valid OpenGL context
    QOpenGLContext* context = QOpenGLContext::currentContext();
    if (!context) {
        VLOG_ERROR("OpenGL", "No current OpenGL context during ToolpathRenderer initialization");
        return;
    }

    // Initialize OpenGL functions if not already done
    if (!initializeOpenGLFunctions()) {
        VLOG_ERROR("OpenGL", "Failed to initialize OpenGL functions in ToolpathRenderer");
        return;
    }

    try {
        // Create OpenGL buffers
        if (!m_lineVertexVBO.isCreated() && !m_lineVertexVBO.create()) {
            VLOG_ERROR("OpenGL", "Failed to create line vertex VBO");
            return;
        }

        if (!m_lineColorVBO.isCreated() && !m_lineColorVBO.create()) {
            VLOG_ERROR("OpenGL", "Failed to create line color VBO");
            return;
        }

        if (!m_pointVertexVBO.isCreated() && !m_pointVertexVBO.create()) {
            VLOG_ERROR("OpenGL", "Failed to create point vertex VBO");
            return;
        }

        if (!m_pointColorVBO.isCreated() && !m_pointColorVBO.create()) {
            VLOG_ERROR("OpenGL", "Failed to create point color VBO");
            return;
        }

        // Create shader program
        createShaderProgram();

        if (!m_shaderProgram) {
            VLOG_ERROR("OpenGL", "Failed to create shader program");
            return;
        }

        // Don't setup buffers during initialization - wait until we have actual points
        // This prevents OpenGL errors when no toolpath data is available
        // Buffers will be set up on first render when points are available

        // Cache OpenGL capability ranges
        cacheOpenGLRanges();

        m_initialized = true;
        m_needsUpdate = true; // Ensure buffers are set up when points are first provided

        VLOG_DEBUG("OpenGL", "ToolpathRenderer initialized successfully");
    } catch (const std::exception& e) {
        VLOG_ERROR("OpenGL", QString("Exception during ToolpathRenderer initialization: %1").arg(e.what()));
    } catch (...) {
        VLOG_ERROR("OpenGL", "Unknown exception during ToolpathRenderer initialization");
    }
}

void ToolpathRenderer::render(const QMatrix4x4& viewMatrix, const QMatrix4x4& projectionMatrix)
{
    // Skip rendering if there are no points - this prevents OpenGL errors
    if (m_points.isEmpty()) {
        // Don't log this as it's normal during startup
        return;
    }

    try {
        // Initialize if not already initialized
        if (!m_initialized) {
            VLOG_DEBUG("OpenGL", "ToolpathRenderer not initialized, initializing now");
            initialize();

            // If initialization failed, don't try to render
            if (!m_initialized) {
                VLOG_WARNING("OpenGL", "ToolpathRenderer initialization failed, cannot render");
                return;
            }
        }

        // Update buffers if needed
        if (m_needsUpdate) {
            VLOG_DEBUG("OpenGL", "ToolpathRenderer buffers need update, updating now");
            setupToolpathBuffers();
            m_needsUpdate = false;
        }

        // Double-check we still have points after buffer update
        if (m_points.isEmpty()) {
            return;
        }

        VLOG_DEBUG("OpenGL", QString("ToolpathRenderer::render called with %1 points").arg(m_points.size()));

        // Check if shader program is valid
        if (!m_shaderProgram) {
            VLOG_WARNING("OpenGL", "Cannot render toolpath: shader program is null");
            return;
        }

        // Validate that we have valid VAOs before proceeding
        if (!m_lineVAO.isCreated() && !m_pointVAO.isCreated()) {
            VLOG_WARNING("OpenGL", "Cannot render toolpath: no valid VAOs created");
            return;
        }

        // Depth testing is already enabled globally in SimulationView::initializeGL()
        // No need to enable it again here

        // Calculate model-view-projection matrix
        QMatrix4x4 modelMatrix;
        modelMatrix.setToIdentity();
        QMatrix4x4 mvp = projectionMatrix * viewMatrix * modelMatrix;

        // Validate shader program before binding
        if (!m_shaderProgram->isLinked()) {
            VLOG_ERROR("OpenGL", "Cannot bind shader program: not linked properly");
            VLOG_ERROR("OpenGL", QString("Shader program log: %1").arg(m_shaderProgram->log()));
            return;
        }

        // Clear any existing OpenGL errors before binding
        while (glGetError() != GL_NO_ERROR) {
            // Clear error queue
        }

        // Bind shader program
        m_shaderProgram->bind();
        checkOpenGLError("after binding shader program");

        // Set uniform values
        m_shaderProgram->setUniformValue("modelViewProjection", mvp);
        checkOpenGLError("after setting uniform values");

        // Draw toolpath lines if VAO is valid and we have points
        // Note: Line width is now set by SimulationView before calling render()
        if (m_lineVAO.isCreated() && !m_points.empty()) {
            m_lineVAO.bind();
            checkOpenGLError("after binding line VAO");

            glDrawArrays(GL_LINE_STRIP, 0, m_points.size());
            checkOpenGLError("after drawing line arrays");
            m_lineVAO.release();
            checkOpenGLError("after releasing line VAO");
        } else if (m_lineVAO.isCreated()) {
            VLOG_WARNING("OpenGL", "Cannot render toolpath lines: no points available");
        } else {
            VLOG_WARNING("OpenGL", "Cannot render toolpath lines: VAO not created");
        }

        // Draw points if requested and VAO is valid and we have points
        // Note: Point size is now set by SimulationView before calling render()
        if (m_showPoints && m_pointVAO.isCreated() && !m_points.empty()) {
            m_pointVAO.bind();
            checkOpenGLError("after binding point VAO");

            glDrawArrays(GL_POINTS, 0, m_points.size());
            checkOpenGLError("after drawing point arrays");
            m_pointVAO.release();
            checkOpenGLError("after releasing point VAO");
        } else if (m_showPoints && m_pointVAO.isCreated()) {
            VLOG_WARNING("OpenGL", "Cannot render toolpath points: no points available");
        } else if (m_showPoints && !m_pointVAO.isCreated()) {
            VLOG_WARNING("OpenGL", "Cannot render toolpath points: VAO not created");
        }

        // Release shader program
        // Note: OpenGL state reset is now handled by SimulationView
        m_shaderProgram->release();
        checkOpenGLError("after releasing shader program");

        // Check for OpenGL errors with detailed reporting
        checkOpenGLError("after toolpath rendering");

        VLOG_DEBUG("OpenGL", "ToolpathRenderer::render completed");
    } catch (const std::exception& e) {
        VLOG_ERROR("OpenGL", QString("Exception during toolpath rendering: %1").arg(e.what()));
    } catch (...) {
        VLOG_ERROR("OpenGL", "Unknown exception during toolpath rendering");
    }
}

void ToolpathRenderer::createShaderProgram()
{
    VLOG_DEBUG("OpenGL", "ToolpathRenderer::createShaderProgram called");

    // Load shader sources
    QString vertexShaderSource = loadShaderSource("shaders/toolpath.vert");
    QString fragmentShaderSource = loadShaderSource("shaders/toolpath.frag");

    if (vertexShaderSource.isEmpty() || fragmentShaderSource.isEmpty()) {
        VLOG_WARNING("OpenGL", "Failed to load shader sources, using default shaders");

        // Use default shaders if loading failed
        vertexShaderSource =
            "#version 410 core\n"
            "layout(location = 0) in vec3 position;\n"
            "layout(location = 1) in vec3 color;\n"
            "uniform mat4 modelViewProjection;\n"
            "out vec3 fragColor;\n"
            "void main() {\n"
            "    gl_Position = modelViewProjection * vec4(position, 1.0);\n"
            "    fragColor = color;\n"
            "}\n";

        fragmentShaderSource =
            "#version 410 core\n"
            "in vec3 fragColor;\n"
            "out vec4 outColor;\n"
            "void main() {\n"
            "    outColor = vec4(fragColor, 1.0);\n"
            "}\n";
    }

    // Clear any existing OpenGL errors before creating shader program
    while (glGetError() != GL_NO_ERROR) {
        // Clear error queue
    }

    // Create shader program
    m_shaderProgram = new QOpenGLShaderProgram();

    // Add vertex shader
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Vertex, vertexShaderSource)) {
        VLOG_ERROR("OpenGL", QString("Failed to compile vertex shader: %1").arg(m_shaderProgram->log()));
        VLOG_ERROR("OpenGL", QString("Vertex shader source: %1").arg(vertexShaderSource));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    // Add fragment shader
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Fragment, fragmentShaderSource)) {
        VLOG_ERROR("OpenGL", QString("Failed to compile fragment shader: %1").arg(m_shaderProgram->log()));
        VLOG_ERROR("OpenGL", QString("Fragment shader source: %1").arg(fragmentShaderSource));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    // Link shader program
    if (!m_shaderProgram->link()) {
        VLOG_ERROR("OpenGL", QString("Failed to link shader program: %1").arg(m_shaderProgram->log()));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    // Validate the linked program
    if (!m_shaderProgram->isLinked()) {
        VLOG_ERROR("OpenGL", "Shader program is not properly linked after link() call");
        VLOG_ERROR("OpenGL", QString("Shader program log: %1").arg(m_shaderProgram->log()));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    VLOG_DEBUG("OpenGL", "Shader program created and linked successfully");
    VLOG_DEBUG("OpenGL", QString("Shader program ID: %1").arg(m_shaderProgram->programId()));
}

void ToolpathRenderer::setupToolpathBuffers()
{
    VLOG_DEBUG("OpenGL", "ToolpathRenderer::setupToolpathBuffers called");

    // If there are no points, don't try to set up buffers
    if (m_points.isEmpty()) {
        VLOG_DEBUG("OpenGL", "No points to set up buffers for");
        return;
    }

    try {
        // Prepare point colors if needed
        QVector<QVector3D> pointColors;
        for (int i = 0; i < m_points.size(); ++i) {
            if (i == 0) {
                // First point in bright green
                pointColors.append(QVector3D(0.0f, 1.0f, 0.0f));
            } else if (i == m_points.size() - 1) {
                // Last point in bright red
                pointColors.append(QVector3D(1.0f, 0.0f, 0.0f));
            } else if (i < m_colors.size()) {
                // Use the provided color
                pointColors.append(m_colors[i]);
            } else {
                // Default to bright blue
                pointColors.append(QVector3D(0.0f, 0.5f, 1.0f));
            }
        }

        // Prepare line colors if needed
        QVector<QVector3D> lineColors = m_colors;
        while (lineColors.size() < m_points.size()) {
            lineColors.append(QVector3D(1.0f, 1.0f, 1.0f));
        }

        // Setup line VAO and VBOs using the base class method
        if (!createAndBindVAO(m_lineVAO)) {
            VLOG_ERROR("OpenGL", "Failed to create and bind VAO for toolpath lines");
            return;
        }
        checkOpenGLError("after creating and binding line VAO");

        // Upload vertex data
        m_lineVertexVBO.bind();
        checkOpenGLError("after binding line vertex VBO");
        m_lineVertexVBO.allocate(m_points.constData(), m_points.size() * sizeof(QVector3D));
        checkOpenGLError("after allocating line vertex data");

        // Set vertex attribute pointers
        glEnableVertexAttribArray(0);
        checkOpenGLError("after enabling vertex attribute 0");
        glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);
        checkOpenGLError("after setting vertex attribute pointer 0");

        // Upload color data
        m_lineColorVBO.bind();
        checkOpenGLError("after binding line color VBO");
        m_lineColorVBO.allocate(lineColors.constData(), lineColors.size() * sizeof(QVector3D));
        checkOpenGLError("after allocating line color data");

        // Set color attribute pointers
        glEnableVertexAttribArray(1);
        checkOpenGLError("after enabling vertex attribute 1");
        glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);
        checkOpenGLError("after setting vertex attribute pointer 1");

        // Release line VAO
        m_lineVAO.release();
        checkOpenGLError("after releasing line VAO");

        // Setup point VAO and VBOs using the base class method
        if (!createAndBindVAO(m_pointVAO)) {
            VLOG_ERROR("OpenGL", "Failed to create and bind VAO for toolpath points");
            return;
        }
        checkOpenGLError("after creating and binding point VAO");

        // Upload vertex data
        m_pointVertexVBO.bind();
        checkOpenGLError("after binding point vertex VBO");
        m_pointVertexVBO.allocate(m_points.constData(), m_points.size() * sizeof(QVector3D));
        checkOpenGLError("after allocating point vertex data");

        // Set vertex attribute pointers
        glEnableVertexAttribArray(0);
        checkOpenGLError("after enabling point vertex attribute 0");
        glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);
        checkOpenGLError("after setting point vertex attribute pointer 0");

        // Upload color data
        m_pointColorVBO.bind();
        checkOpenGLError("after binding point color VBO");
        m_pointColorVBO.allocate(pointColors.constData(), pointColors.size() * sizeof(QVector3D));
        checkOpenGLError("after allocating point color data");

        // Set color attribute pointers
        glEnableVertexAttribArray(1);
        checkOpenGLError("after enabling point vertex attribute 1");
        glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);
        checkOpenGLError("after setting point vertex attribute pointer 1");

        // Release point VAO
        m_pointVAO.release();
        checkOpenGLError("after releasing point VAO");

        VLOG_DEBUG("OpenGL", QString("Toolpath buffers set up with %1 points").arg(m_points.size()));
    } catch (const std::exception& e) {
        VLOG_ERROR("OpenGL", QString("Exception during toolpath buffer setup: %1").arg(e.what()));
    } catch (...) {
        VLOG_ERROR("OpenGL", "Unknown exception during toolpath buffer setup");
    }
}

void ToolpathRenderer::setPoints(const QVector<QVector3D>& points)
{
    if (m_points != points) {
        m_points = points;
        m_needsUpdate = true;
    }
}

void ToolpathRenderer::setColors(const QVector<QVector3D>& colors)
{
    if (m_colors != colors) {
        m_colors = colors;
        m_needsUpdate = true;
    }
}

void ToolpathRenderer::setLineWidth(float width)
{
    m_lineWidth = width;
}

void ToolpathRenderer::setPointSize(float size)
{
    m_pointSize = size;
}

void ToolpathRenderer::setShowPoints(bool show)
{
    m_showPoints = show;
}

float ToolpathRenderer::getClampedLineWidth() const
{
    // Ensure ranges are cached before clamping
    if (!m_rangesCached) {
        // Cast away const to cache ranges - this is a lazy initialization pattern
        const_cast<ToolpathRenderer*>(this)->cacheOpenGLRanges();
    }

    // Return clamped line width
    return qBound(m_minLineWidth, m_lineWidth, m_maxLineWidth);
}

float ToolpathRenderer::getClampedPointSize() const
{
    // Ensure ranges are cached before clamping
    if (!m_rangesCached) {
        // Cast away const to cache ranges - this is a lazy initialization pattern
        const_cast<ToolpathRenderer*>(this)->cacheOpenGLRanges();
    }

    // Return clamped point size
    return qBound(m_minPointSize, m_pointSize, m_maxPointSize);
}

void ToolpathRenderer::clear()
{
    if (!m_points.isEmpty() || !m_colors.isEmpty()) {
        m_points.clear();
        m_colors.clear();
        m_needsUpdate = true;
    }
}

void ToolpathRenderer::cacheOpenGLRanges()
{
    if (m_rangesCached) {
        return; // Already cached
    }

    // Use conservative defaults for OpenGL Core Profile
    m_minLineWidth = 1.0f;
    m_maxLineWidth = 1.0f;  // Core Profile typically only supports 1.0
    m_minPointSize = 1.0f;
    m_maxPointSize = 64.0f; // Conservative upper bound

    try {
        // Clear any existing errors
        while (glGetError() != GL_NO_ERROR) {
            // Clear error queue
        }

        // Try to query supported line width range
        GLfloat lineWidthRange[2] = {1.0f, 1.0f};
        glGetFloatv(GL_LINE_WIDTH_RANGE, lineWidthRange);

        // Check for errors after line width query
        GLenum err = glGetError();
        if (err == GL_NO_ERROR && lineWidthRange[0] > 0.0f && lineWidthRange[1] >= lineWidthRange[0]) {
            m_minLineWidth = lineWidthRange[0];
            m_maxLineWidth = lineWidthRange[1];
        }

        // Try to query supported point size range
        GLfloat pointSizeRange[2] = {1.0f, 64.0f};
        glGetFloatv(GL_POINT_SIZE_RANGE, pointSizeRange);

        // Check for errors after point size query
        err = glGetError();
        if (err == GL_NO_ERROR && pointSizeRange[0] > 0.0f && pointSizeRange[1] >= pointSizeRange[0]) {
            m_minPointSize = pointSizeRange[0];
            m_maxPointSize = pointSizeRange[1];
        }

        m_rangesCached = true;

        VLOG_DEBUG("OpenGL", QString("OpenGL ranges cached - Line width: %1-%2, Point size: %3-%4")
                   .arg(m_minLineWidth).arg(m_maxLineWidth).arg(m_minPointSize).arg(m_maxPointSize));

    } catch (const std::exception& e) {
        VLOG_ERROR("OpenGL", QString("Exception during OpenGL range caching: %1").arg(e.what()));
        // Keep safe defaults
        m_rangesCached = true;
    }
}

void ToolpathRenderer::checkOpenGLError(const QString& operation)
{
    GLenum err;
    while ((err = glGetError()) != GL_NO_ERROR) {
        QString errorString;
        switch (err) {
            case GL_INVALID_ENUM:
                errorString = "GL_INVALID_ENUM";
                break;
            case GL_INVALID_VALUE:
                errorString = "GL_INVALID_VALUE";
                break;
            case GL_INVALID_OPERATION:
                errorString = "GL_INVALID_OPERATION";
                break;
            case GL_OUT_OF_MEMORY:
                errorString = "GL_OUT_OF_MEMORY";
                break;
            case GL_INVALID_FRAMEBUFFER_OPERATION:
                errorString = "GL_INVALID_FRAMEBUFFER_OPERATION";
                break;
            default:
                errorString = QString("Unknown error %1").arg(err);
                break;
        }
        VLOG_WARNING("OpenGL", QString("OpenGL error %1 (%2) during: %3").arg(errorString).arg(err).arg(operation));
    }
}

} // namespace UI
} // namespace Vizion3D
