#include "ui/position_renderer.h"
#include "utils/logger.h"
#include <QDebug>
#include <QFile>
#include <cmath>

namespace Vizion3D {
namespace UI {

PositionRenderer::PositionRenderer()
    : m_position(0.0f, 0.0f, 0.0f)
    , m_radius(5.0f)
    , m_color(1.0f, 0.0f, 1.0f, 1.0f)
    , m_slices(32)
    , m_stacks(32)
    , m_showCross(true)
    , m_crossSize(10.0f)
    , m_shaderProgram(nullptr)
    , m_initialized(false)
    , m_needsUpdate(true)
{
    // Initialize OpenGL buffers
    m_sphereVertexVBO.create();
    m_sphereNormalVBO.create();
    m_sphereIndexVBO = QOpenGLBuffer(QOpenGLBuffer::IndexBuffer);
    m_sphereIndexVBO.create();
    m_crossVertexVBO.create();
    m_crossColorVBO.create();
}

PositionRenderer::~PositionRenderer()
{
    // Clean up OpenGL resources
    if (m_initialized) {
        // Clean up sphere buffers
        m_sphereVAO.destroy();
        m_sphereVertexVBO.destroy();
        m_sphereNormalVBO.destroy();
        m_sphereIndexVBO.destroy();

        // Clean up cross buffers
        m_crossVAO.destroy();
        m_crossVertexVBO.destroy();
        m_crossColorVBO.destroy();

        // Delete shader program
        deleteShaderProgram(m_shaderProgram);
    }
}

void PositionRenderer::initialize()
{
    VLOG_DEBUG("OpenGL", "PositionRenderer::initialize called");

    // Create shader program
    createShaderProgram();

    // Generate vertices
    generateSphereVertices();
    generateCrossVertices();

    // Setup buffers
    setupSphereBuffers();
    setupCrossBuffers();

    m_initialized = true;
    m_needsUpdate = false;

    VLOG_DEBUG("OpenGL", "PositionRenderer initialized");
}

void PositionRenderer::render(const QMatrix4x4& viewMatrix, const QMatrix4x4& projectionMatrix)
{
    if (!m_initialized) {
        initialize();
    }

    if (m_needsUpdate) {
        // Regenerate vertices and update buffers
        generateSphereVertices();
        generateCrossVertices();

        setupSphereBuffers();
        setupCrossBuffers();

        m_needsUpdate = false;
    }

    VLOG_DEBUG("OpenGL", QString("PositionRenderer::render called at position %1 %2 %3").arg(m_position.x()).arg(m_position.y()).arg(m_position.z()));

    // Check if shader program is valid
    if (!m_shaderProgram) {
        VLOG_WARNING("OpenGL", "Cannot render position: shader program is null");
        return;
    }

    // Depth testing is already enabled globally in SimulationView::initializeGL()

    // Calculate model-view-projection matrix
    QMatrix4x4 modelMatrix;
    modelMatrix.setToIdentity();
    modelMatrix.translate(m_position);

    QMatrix4x4 modelView = viewMatrix * modelMatrix;
    QMatrix4x4 mvp = projectionMatrix * modelView;

    // Bind shader program
    m_shaderProgram->bind();

    // Set uniform values for sphere
    m_shaderProgram->setUniformValue("modelViewProjection", mvp);
    m_shaderProgram->setUniformValue("modelView", modelView);
    m_shaderProgram->setUniformValue("color", m_color);
    m_shaderProgram->setUniformValue("lightPosition", QVector3D(100.0f, 100.0f, 100.0f));

    // Draw sphere
    m_sphereVAO.bind();
    glDrawElements(GL_TRIANGLES, m_sphereIndices.size(), GL_UNSIGNED_INT, nullptr);
    m_sphereVAO.release();

    // Draw cross if requested
    if (m_showCross) {
        // Use a simpler shader for the cross
        // For simplicity, we'll reuse the same shader but with a different model matrix

        // Reset model matrix to draw cross at the exact position
        modelMatrix.setToIdentity();

        // Calculate new matrices
        modelView = viewMatrix * modelMatrix;
        mvp = projectionMatrix * modelView;

        // Set uniform values for cross
        m_shaderProgram->setUniformValue("modelViewProjection", mvp);
        m_shaderProgram->setUniformValue("modelView", modelView);

        // Draw cross
        m_crossVAO.bind();
        glLineWidth(3.0f);
        glDrawArrays(GL_LINES, 0, m_crossVertices.size());
        m_crossVAO.release();

        // Reset line width
        glLineWidth(1.0f);
    }

    // Release shader program
    m_shaderProgram->release();

    VLOG_DEBUG("OpenGL", "PositionRenderer::render completed");
}

void PositionRenderer::createShaderProgram()
{
    VLOG_DEBUG("OpenGL", "PositionRenderer::createShaderProgram called");

    // Load shader sources
    QString vertexShaderSource = loadShaderSource("shaders/position.vert");
    QString fragmentShaderSource = loadShaderSource("shaders/position.frag");

    if (vertexShaderSource.isEmpty() || fragmentShaderSource.isEmpty()) {
        VLOG_WARNING("OpenGL", "Failed to load shader sources, using default shaders");

        // Use default shaders if loading failed
        vertexShaderSource =
            "#version 410 core\n"
            "layout(location = 0) in vec3 position;\n"
            "layout(location = 1) in vec3 normal;\n"
            "uniform mat4 modelViewProjection;\n"
            "uniform mat4 modelView;\n"
            "uniform vec4 color;\n"
            "out vec3 fragNormal;\n"
            "out vec3 fragPosition;\n"
            "out vec4 fragColor;\n"
            "void main() {\n"
            "    gl_Position = modelViewProjection * vec4(position, 1.0);\n"
            "    fragNormal = mat3(modelView) * normal;\n"
            "    fragPosition = vec3(modelView * vec4(position, 1.0));\n"
            "    fragColor = color;\n"
            "}\n";

        fragmentShaderSource =
            "#version 410 core\n"
            "in vec3 fragNormal;\n"
            "in vec3 fragPosition;\n"
            "in vec4 fragColor;\n"
            "out vec4 outColor;\n"
            "uniform vec3 lightPosition;\n"
            "void main() {\n"
            "    float ambientStrength = 0.3;\n"
            "    vec3 ambient = ambientStrength * vec3(1.0, 1.0, 1.0);\n"
            "    vec3 norm = normalize(fragNormal);\n"
            "    vec3 lightDir = normalize(lightPosition - fragPosition);\n"
            "    float diff = max(dot(norm, lightDir), 0.0);\n"
            "    vec3 diffuse = diff * vec3(1.0, 1.0, 1.0);\n"
            "    vec3 result = (ambient + diffuse) * fragColor.rgb;\n"
            "    outColor = vec4(result, fragColor.a);\n"
            "}\n";
    }

    // Create shader program
    m_shaderProgram = new QOpenGLShaderProgram();

    // Add vertex shader
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Vertex, vertexShaderSource)) {
        VLOG_ERROR("OpenGL", QString("Failed to compile vertex shader: %1").arg(m_shaderProgram->log()));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    // Add fragment shader
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Fragment, fragmentShaderSource)) {
        VLOG_ERROR("OpenGL", QString("Failed to compile fragment shader: %1").arg(m_shaderProgram->log()));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    // Link shader program
    if (!m_shaderProgram->link()) {
        VLOG_ERROR("OpenGL", QString("Failed to link shader program: %1").arg(m_shaderProgram->log()));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    VLOG_DEBUG("OpenGL", "Shader program created successfully");
}

void PositionRenderer::generateSphereVertices()
{
    VLOG_DEBUG("OpenGL", "PositionRenderer::generateSphereVertices called");

    m_sphereVertices.clear();
    m_sphereNormals.clear();
    m_sphereIndices.clear();

    // Generate sphere vertices
    for (int i = 0; i <= m_stacks; ++i) {
        float phi = M_PI * i / m_stacks;
        float sinPhi = sin(phi);
        float cosPhi = cos(phi);

        for (int j = 0; j <= m_slices; ++j) {
            float theta = 2.0f * M_PI * j / m_slices;
            float sinTheta = sin(theta);
            float cosTheta = cos(theta);

            // Position
            float x = m_radius * sinPhi * cosTheta;
            float y = m_radius * cosPhi;
            float z = m_radius * sinPhi * sinTheta;

            // Normal
            float nx = sinPhi * cosTheta;
            float ny = cosPhi;
            float nz = sinPhi * sinTheta;

            m_sphereVertices.append(QVector3D(x, y, z));
            m_sphereNormals.append(QVector3D(nx, ny, nz));
        }
    }

    // Generate indices
    for (int i = 0; i < m_stacks; ++i) {
        for (int j = 0; j < m_slices; ++j) {
            int first = (i * (m_slices + 1)) + j;
            int second = first + m_slices + 1;

            // First triangle
            m_sphereIndices.append(first);
            m_sphereIndices.append(second);
            m_sphereIndices.append(first + 1);

            // Second triangle
            m_sphereIndices.append(second);
            m_sphereIndices.append(second + 1);
            m_sphereIndices.append(first + 1);
        }
    }

    VLOG_DEBUG("OpenGL", QString("Generated %1 sphere vertices and %2 indices").arg(m_sphereVertices.size()).arg(m_sphereIndices.size()));
}

void PositionRenderer::generateCrossVertices()
{
    VLOG_DEBUG("OpenGL", "PositionRenderer::generateCrossVertices called");

    m_crossVertices.clear();
    m_crossColors.clear();

    if (m_showCross) {
        // X axis (red)
        m_crossVertices.append(QVector3D(m_position.x() - m_crossSize, m_position.y(), m_position.z()));
        m_crossVertices.append(QVector3D(m_position.x() + m_crossSize, m_position.y(), m_position.z()));
        m_crossColors.append(QVector3D(1.0f, 0.0f, 0.0f));
        m_crossColors.append(QVector3D(1.0f, 0.0f, 0.0f));

        // Y axis (green)
        m_crossVertices.append(QVector3D(m_position.x(), m_position.y() - m_crossSize, m_position.z()));
        m_crossVertices.append(QVector3D(m_position.x(), m_position.y() + m_crossSize, m_position.z()));
        m_crossColors.append(QVector3D(0.0f, 1.0f, 0.0f));
        m_crossColors.append(QVector3D(0.0f, 1.0f, 0.0f));

        // Z axis (blue)
        m_crossVertices.append(QVector3D(m_position.x(), m_position.y(), m_position.z() - m_crossSize));
        m_crossVertices.append(QVector3D(m_position.x(), m_position.y(), m_position.z() + m_crossSize));
        m_crossColors.append(QVector3D(0.0f, 0.0f, 1.0f));
        m_crossColors.append(QVector3D(0.0f, 0.0f, 1.0f));
    }

    VLOG_DEBUG("OpenGL", QString("Generated %1 cross vertices").arg(m_crossVertices.size()));
}

void PositionRenderer::setupSphereBuffers()
{
    VLOG_DEBUG("OpenGL", "PositionRenderer::setupSphereBuffers called");

    // Create and bind VAO using the base class method
    if (!createAndBindVAO(m_sphereVAO)) {
        VLOG_ERROR("OpenGL", "Failed to create and bind VAO for sphere");
        return;
    }

    // Upload vertex data
    m_sphereVertexVBO.bind();
    m_sphereVertexVBO.allocate(m_sphereVertices.constData(), m_sphereVertices.size() * sizeof(QVector3D));

    // Set vertex attribute pointers
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);

    // Upload normal data
    m_sphereNormalVBO.bind();
    m_sphereNormalVBO.allocate(m_sphereNormals.constData(), m_sphereNormals.size() * sizeof(QVector3D));

    // Set normal attribute pointers
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);

    // Upload index data
    m_sphereIndexVBO.bind();
    m_sphereIndexVBO.allocate(m_sphereIndices.constData(), m_sphereIndices.size() * sizeof(unsigned int));

    // Release VAO
    m_sphereVAO.release();

    VLOG_DEBUG("OpenGL", "Sphere buffers set up");
}

void PositionRenderer::setupCrossBuffers()
{
    VLOG_DEBUG("OpenGL", "PositionRenderer::setupCrossBuffers called");

    if (m_crossVertices.isEmpty()) {
        return;
    }

    // Create and bind VAO using the base class method
    if (!createAndBindVAO(m_crossVAO)) {
        VLOG_ERROR("OpenGL", "Failed to create and bind VAO for cross");
        return;
    }

    // Upload vertex data
    m_crossVertexVBO.bind();
    m_crossVertexVBO.allocate(m_crossVertices.constData(), m_crossVertices.size() * sizeof(QVector3D));

    // Set vertex attribute pointers
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);

    // Upload color data
    m_crossColorVBO.bind();
    m_crossColorVBO.allocate(m_crossColors.constData(), m_crossColors.size() * sizeof(QVector3D));

    // Set color attribute pointers
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);

    // Release VAO
    m_crossVAO.release();

    VLOG_DEBUG("OpenGL", "Cross buffers set up");
}

void PositionRenderer::setPosition(const QVector3D& position)
{
    if (m_position != position) {
        m_position = position;
        m_needsUpdate = true;
    }
}

void PositionRenderer::setRadius(float radius)
{
    if (m_radius != radius) {
        m_radius = radius;
        m_needsUpdate = true;
    }
}

void PositionRenderer::setColor(const QVector4D& color)
{
    m_color = color;
}

void PositionRenderer::setResolution(int slices, int stacks)
{
    if (m_slices != slices || m_stacks != stacks) {
        m_slices = slices;
        m_stacks = stacks;
        m_needsUpdate = true;
    }
}

void PositionRenderer::setShowCross(bool show)
{
    if (m_showCross != show) {
        m_showCross = show;
        m_needsUpdate = true;
    }
}

void PositionRenderer::setCrossSize(float size)
{
    if (m_crossSize != size) {
        m_crossSize = size;
        m_needsUpdate = true;
    }
}

} // namespace UI
} // namespace Vizion3D
