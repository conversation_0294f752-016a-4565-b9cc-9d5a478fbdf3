#include "ui/grid_renderer.h"
#include "ui/opengl_state.h"
#include <QDebug>
#include <QFile>
#include <QVector>
#include "utils/result.h"

namespace Vizion3D {
namespace UI {

GridRenderer::GridRenderer()
    : m_size(200.0f)
    , m_step(10.0f)
    , m_color(0.3f, 0.3f, 0.3f)
    , m_xAxisColor(1.0f, 0.0f, 0.0f)
    , m_yAxisColor(0.0f, 1.0f, 0.0f)
    , m_zAxisColor(0.0f, 0.0f, 1.0f)
    , m_highlightToolpathArea(true)
    , m_toolpathAreaMin(0.0f, 0.0f, 0.0f)
    , m_toolpathAreaMax(25.0f, 0.0f, 25.0f)
    , m_shaderProgram(nullptr)
    , m_initialized(false)
    , m_needsUpdate(true)
{
    // Initialize OpenGL buffers
    m_gridVertexVBO.create();
    m_gridColorVBO.create();
    m_axesVertexVBO.create();
    m_axesColorVBO.create();
    m_toolpathAreaVertexVBO.create();
    m_toolpathAreaColorVBO.create();
}

GridRenderer::~GridRenderer()
{
    // Clean up OpenGL resources
    if (m_initialized) {
        // Clean up grid buffers
        m_gridVAO.destroy();
        m_gridVertexVBO.destroy();
        m_gridColorVBO.destroy();

        // Clean up axes buffers
        m_axesVAO.destroy();
        m_axesVertexVBO.destroy();
        m_axesColorVBO.destroy();

        // Clean up toolpath area buffers
        m_toolpathAreaVAO.destroy();
        m_toolpathAreaVertexVBO.destroy();
        m_toolpathAreaColorVBO.destroy();

        // Delete shader program
        deleteShaderProgram(m_shaderProgram);
    }
}

void GridRenderer::initialize()
{
    VLOG_DEBUG("UI", "GridRenderer::initialize called");

    // Create shader program
    createShaderProgram();

    // Generate vertices
    generateGridVertices();
    generateMainAxesVertices();
    generateToolpathAreaVertices();

    // Setup buffers
    setupGridBuffers();
    setupMainAxesBuffers();
    setupToolpathAreaBuffers();

    m_initialized = true;
    m_needsUpdate = false;

    VLOG_DEBUG("UI", "GridRenderer initialized");
}

void GridRenderer::render(const QMatrix4x4& viewMatrix, const QMatrix4x4& projectionMatrix)
{
    if (!m_initialized) {
        initialize();
    }

    if (m_needsUpdate) {
        // Regenerate vertices and update buffers
        generateGridVertices();
        generateMainAxesVertices();
        generateToolpathAreaVertices();

        setupGridBuffers();
        setupMainAxesBuffers();
        setupToolpathAreaBuffers();

        m_needsUpdate = false;
    }

    VLOG_DEBUG("UI", "GridRenderer::render called");

    // Check if shader program is valid
    if (!m_shaderProgram) {
        VLOG_DEBUG("UI", "Cannot render grid: shader program is null");
        return;
    }

    // Depth testing is already enabled globally in SimulationView::initializeGL()

    // Calculate model-view-projection matrix
    QMatrix4x4 modelMatrix;
    modelMatrix.setToIdentity();
    QMatrix4x4 mvp = projectionMatrix * viewMatrix * modelMatrix;

    // Bind shader program
    m_shaderProgram->bind();

    // Set uniform values
    m_shaderProgram->setUniformValue("modelViewProjection", mvp);

    // Get OpenGL state manager for centralized state management
    OpenGLState& glState = OpenGLState::instance();

    // Draw grid
    glState.setLineWidth(1.0f);
    m_gridVAO.bind();
    glDrawArrays(GL_LINES, 0, m_gridVertices.size());
    m_gridVAO.release();

    // Draw main axes
    glState.setLineWidth(3.0f);
    m_axesVAO.bind();
    glDrawArrays(GL_LINES, 0, m_axesVertices.size());
    m_axesVAO.release();

    // Draw toolpath area if requested
    if (m_highlightToolpathArea) {
        glState.setLineWidth(2.0f);
        m_toolpathAreaVAO.bind();
        glDrawArrays(GL_LINE_LOOP, 0, m_toolpathAreaVertices.size());
        m_toolpathAreaVAO.release();
    }

    // Release shader program
    m_shaderProgram->release();

    VLOG_DEBUG("UI", "GridRenderer::render completed");
}

void GridRenderer::createShaderProgram()
{
    VLOG_DEBUG("UI", "GridRenderer::createShaderProgram called");

    // Load shader sources
    QString vertexShaderSource = loadShaderSource("shaders/grid.vert");
    QString fragmentShaderSource = loadShaderSource("shaders/grid.frag");

    if (vertexShaderSource.isEmpty() || fragmentShaderSource.isEmpty()) {
        VLOG_DEBUG("UI", "Failed to load shader sources, using default shaders");

        // Use default shaders if loading failed
        vertexShaderSource =
            "#version 410 core\n"
            "layout(location = 0) in vec3 position;\n"
            "layout(location = 1) in vec3 color;\n"
            "uniform mat4 modelViewProjection;\n"
            "out vec3 fragColor;\n"
            "void main() {\n"
            "    gl_Position = modelViewProjection * vec4(position, 1.0);\n"
            "    fragColor = color;\n"
            "}\n";

        fragmentShaderSource =
            "#version 410 core\n"
            "in vec3 fragColor;\n"
            "out vec4 outColor;\n"
            "void main() {\n"
            "    outColor = vec4(fragColor, 1.0);\n"
            "}\n";
    }

    // Create shader program
    m_shaderProgram = new QOpenGLShaderProgram();

    // Add vertex shader
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Vertex, vertexShaderSource)) {
        VLOG_DEBUG("UI", QString("Failed to compile vertex shader:%1").arg(m_shaderProgram->log()));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    // Add fragment shader
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Fragment, fragmentShaderSource)) {
        VLOG_DEBUG("UI", QString("Failed to compile fragment shader:%1").arg(m_shaderProgram->log()));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    // Link shader program
    if (!m_shaderProgram->link()) {
        VLOG_DEBUG("UI", QString("Failed to link shader program:%1").arg(m_shaderProgram->log()));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    VLOG_DEBUG("UI", "Shader program created successfully");
}

void GridRenderer::generateGridVertices()
{
    VLOG_DEBUG("UI", "GridRenderer::generateGridVertices called");

    m_gridVertices.clear();
    m_gridColors.clear();

    // Calculate grid parameters
    const int gridLines = (static_cast<int>(m_size / m_step) * 2) + 1;

    // Generate grid lines
    for (int i = 0; i < gridLines; ++i) {
        float x = -m_size + (i * m_step);

        // X lines (along X axis, in XZ plane)
        m_gridVertices.append(QVector3D(x, 0.0f, -m_size));
        m_gridVertices.append(QVector3D(x, 0.0f, m_size));
        m_gridColors.append(m_color);
        m_gridColors.append(m_color);

        float z = -m_size + (i * m_step);

        // Z lines (along Z axis, in XZ plane)
        m_gridVertices.append(QVector3D(-m_size, 0.0f, z));
        m_gridVertices.append(QVector3D(m_size, 0.0f, z));
        m_gridColors.append(m_color);
        m_gridColors.append(m_color);
    }

    VLOG_DEBUG("UI", QString("Generated %1 grid vertices").arg(m_gridVertices.size()));
}

void GridRenderer::generateMainAxesVertices()
{
    VLOG_DEBUG("UI", "GridRenderer::generateMainAxesVertices called");

    m_axesVertices.clear();
    m_axesColors.clear();

    // X axis (red)
    m_axesVertices.append(QVector3D(0.0f, 0.0f, 0.0f));
    m_axesVertices.append(QVector3D(m_size, 0.0f, 0.0f));
    m_axesColors.append(m_xAxisColor);
    m_axesColors.append(m_xAxisColor);

    // Y axis (green)
    m_axesVertices.append(QVector3D(0.0f, 0.0f, 0.0f));
    m_axesVertices.append(QVector3D(0.0f, m_size, 0.0f));
    m_axesColors.append(m_yAxisColor);
    m_axesColors.append(m_yAxisColor);

    // Z axis (blue)
    m_axesVertices.append(QVector3D(0.0f, 0.0f, 0.0f));
    m_axesVertices.append(QVector3D(0.0f, 0.0f, m_size));
    m_axesColors.append(m_zAxisColor);
    m_axesColors.append(m_zAxisColor);

    // Add ticks at 10-unit intervals
    for (int i = 1; i <= 10; i++) {
        float pos = i * 10.0f;

        // X axis tick
        m_axesVertices.append(QVector3D(pos, 0.0f, -1.0f));
        m_axesVertices.append(QVector3D(pos, 0.0f, 1.0f));
        m_axesColors.append(m_xAxisColor);
        m_axesColors.append(m_xAxisColor);

        // Z axis tick
        m_axesVertices.append(QVector3D(-1.0f, 0.0f, pos));
        m_axesVertices.append(QVector3D(1.0f, 0.0f, pos));
        m_axesColors.append(m_zAxisColor);
        m_axesColors.append(m_zAxisColor);
    }

    VLOG_DEBUG("UI", QString("Generated %1 axes vertices").arg(m_axesVertices.size()));
}

void GridRenderer::generateToolpathAreaVertices()
{
    VLOG_DEBUG("UI", "GridRenderer::generateToolpathAreaVertices called");

    m_toolpathAreaVertices.clear();
    m_toolpathAreaColors.clear();

    // Toolpath area (yellow)
    QVector3D color(1.0f, 1.0f, 0.0f);

    m_toolpathAreaVertices.append(QVector3D(m_toolpathAreaMin.x(), m_toolpathAreaMin.y(), m_toolpathAreaMin.z()));
    m_toolpathAreaVertices.append(QVector3D(m_toolpathAreaMax.x(), m_toolpathAreaMin.y(), m_toolpathAreaMin.z()));
    m_toolpathAreaVertices.append(QVector3D(m_toolpathAreaMax.x(), m_toolpathAreaMin.y(), m_toolpathAreaMax.z()));
    m_toolpathAreaVertices.append(QVector3D(m_toolpathAreaMin.x(), m_toolpathAreaMin.y(), m_toolpathAreaMax.z()));

    m_toolpathAreaColors.append(color);
    m_toolpathAreaColors.append(color);
    m_toolpathAreaColors.append(color);
    m_toolpathAreaColors.append(color);

    VLOG_DEBUG("UI", QString("Generated %1 toolpath area vertices").arg(m_toolpathAreaVertices.size()));
}

void GridRenderer::setupGridBuffers()
{
    VLOG_DEBUG("UI", "GridRenderer::setupGridBuffers called");

    // Safety check - don't proceed if there are no vertices
    if (m_gridVertices.isEmpty()) {
        VLOG_DEBUG("UI", "No grid vertices to set up buffers for");
        return;
    }

    // Make sure we have matching colors
    if (m_gridColors.size() != m_gridVertices.size()) {
        VLOG_DEBUG("UI", QString("Warning: Grid colors size (%1) doesn't match vertices size (%2)").arg(m_gridColors.size()).arg(m_gridVertices.size()));
        // Fill with default color if needed
        while (m_gridColors.size() < m_gridVertices.size()) {
            m_gridColors.append(m_color);
        }
    }

    // Create and bind VAO using the base class method
    if (!createAndBindVAO(m_gridVAO)) {
        VLOG_ERROR("UI", "Failed to create and bind VAO for grid");
        return;
    }

    // Upload vertex data
    m_gridVertexVBO.bind();
    m_gridVertexVBO.allocate(m_gridVertices.constData(), m_gridVertices.size() * sizeof(QVector3D));

    // Set vertex attribute pointers
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);

    // Upload color data
    m_gridColorVBO.bind();
    m_gridColorVBO.allocate(m_gridColors.constData(), m_gridColors.size() * sizeof(QVector3D));

    // Set color attribute pointers
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);

    // Release VAO
    m_gridVAO.release();

    VLOG_DEBUG("UI", QString("Grid buffers set up with %1 vertices").arg(m_gridVertices.size()));
}

void GridRenderer::setupMainAxesBuffers()
{
    VLOG_DEBUG("UI", "GridRenderer::setupMainAxesBuffers called");

    // Safety check - don't proceed if there are no vertices
    if (m_axesVertices.isEmpty()) {
        VLOG_DEBUG("UI", "No axes vertices to set up buffers for");
        return;
    }

    // Make sure we have matching colors
    if (m_axesColors.size() != m_axesVertices.size()) {
        VLOG_DEBUG("UI", QString("Warning: Axes colors size (%1) doesn't match vertices size (%2)").arg(m_axesColors.size()).arg(m_axesVertices.size()));
        // Fill with default color if needed
        while (m_axesColors.size() < m_axesVertices.size()) {
            m_axesColors.append(m_xAxisColor); // Default to X axis color
        }
    }

    // Create and bind VAO using the base class method
    if (!createAndBindVAO(m_axesVAO)) {
        VLOG_ERROR("UI", "Failed to create and bind VAO for axes");
        return;
    }

    // Upload vertex data
    m_axesVertexVBO.bind();
    m_axesVertexVBO.allocate(m_axesVertices.constData(), m_axesVertices.size() * sizeof(QVector3D));

    // Set vertex attribute pointers
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);

    // Upload color data
    m_axesColorVBO.bind();
    m_axesColorVBO.allocate(m_axesColors.constData(), m_axesColors.size() * sizeof(QVector3D));

    // Set color attribute pointers
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);

    // Release VAO
    m_axesVAO.release();

    VLOG_DEBUG("UI", QString("Main axes buffers set up with %1 vertices").arg(m_axesVertices.size()));
}

void GridRenderer::setupToolpathAreaBuffers()
{
    VLOG_DEBUG("UI", "GridRenderer::setupToolpathAreaBuffers called");

    // Safety check - don't proceed if there are no vertices
    if (m_toolpathAreaVertices.isEmpty()) {
        VLOG_DEBUG("UI", "No toolpath area vertices to set up buffers for");
        return;
    }

    // Make sure we have matching colors
    if (m_toolpathAreaColors.size() != m_toolpathAreaVertices.size()) {
        VLOG_DEBUG("UI", QString("Warning: Toolpath area colors size (%1) doesn't match vertices size (%2)").arg(m_toolpathAreaColors.size()).arg(m_toolpathAreaVertices.size()));
        // Fill with default color if needed
        QVector3D defaultColor(1.0f, 1.0f, 0.0f); // Yellow
        while (m_toolpathAreaColors.size() < m_toolpathAreaVertices.size()) {
            m_toolpathAreaColors.append(defaultColor);
        }
    }

    // Create and bind VAO using the base class method
    if (!createAndBindVAO(m_toolpathAreaVAO)) {
        VLOG_ERROR("UI", "Failed to create and bind VAO for toolpath area");
        return;
    }

    // Upload vertex data
    m_toolpathAreaVertexVBO.bind();
    m_toolpathAreaVertexVBO.allocate(m_toolpathAreaVertices.constData(), m_toolpathAreaVertices.size() * sizeof(QVector3D));

    // Set vertex attribute pointers
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);

    // Upload color data
    m_toolpathAreaColorVBO.bind();
    m_toolpathAreaColorVBO.allocate(m_toolpathAreaColors.constData(), m_toolpathAreaColors.size() * sizeof(QVector3D));

    // Set color attribute pointers
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);

    // Release VAO
    m_toolpathAreaVAO.release();

    VLOG_DEBUG("UI", QString("Toolpath area buffers set up with %1 vertices").arg(m_toolpathAreaVertices.size()));
}

void GridRenderer::setSize(float size)
{
    if (m_size != size) {
        m_size = size;
        m_needsUpdate = true;
    }
}

void GridRenderer::setStep(float step)
{
    if (m_step != step) {
        m_step = step;
        m_needsUpdate = true;
    }
}

void GridRenderer::setColor(const QVector3D& color)
{
    if (m_color != color) {
        m_color = color;
        m_needsUpdate = true;
    }
}

void GridRenderer::setMainAxesColor(const QVector3D& xColor, const QVector3D& yColor, const QVector3D& zColor)
{
    if (m_xAxisColor != xColor || m_yAxisColor != yColor || m_zAxisColor != zColor) {
        m_xAxisColor = xColor;
        m_yAxisColor = yColor;
        m_zAxisColor = zColor;
        m_needsUpdate = true;
    }
}

void GridRenderer::setHighlightToolpathArea(bool highlight)
{
    m_highlightToolpathArea = highlight;
}

void GridRenderer::setToolpathArea(const QVector3D& min, const QVector3D& max)
{
    if (m_toolpathAreaMin != min || m_toolpathAreaMax != max) {
        m_toolpathAreaMin = min;
        m_toolpathAreaMax = max;
        m_needsUpdate = true;
    }
}

} // namespace UI
} // namespace Vizion3D
