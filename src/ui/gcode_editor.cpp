#include "ui/gcode_editor.h"

namespace Vizion3D {
namespace UI {

// GCodeSyntaxHighlighter implementation
GCodeSyntaxHighlighter::GCodeSyntaxHighlighter(QTextDocument* parent)
    : QSyntaxHighlighter(parent)
{
    // G-code format (G0, G1, etc.)
    m_gCodeFormat.setForeground(Qt::blue);
    m_gCodeFormat.setFontWeight(QFont::Bold);
    m_highlightingRules.append({QRegularExpression("\\bG\\d+\\b"), m_gCodeFormat});

    // M-code format (M3, M5, etc.)
    m_mCodeFormat.setForeground(Qt::darkBlue);
    m_mCodeFormat.setFontWeight(QFont::Bold);
    m_highlightingRules.append({QRegularExpression("\\bM\\d+\\b"), m_mCodeFormat});

    // Coordinate format (X, Y, Z, etc.)
    m_coordinateFormat.setForeground(Qt::darkGreen);
    m_highlightingRules.append({QRegularExpression("\\b[XYZ][-+]?\\d*\\.?\\d+\\b"), m_coordinateFormat});

    // Feed rate format (F)
    m_highlightingRules.append({QRegularExpression("\\bF[-+]?\\d*\\.?\\d+\\b"), m_coordinateFormat});

    // Comment format
    m_commentFormat.setForeground(Qt::darkGray);
    m_highlightingRules.append({QRegularExpression(";.*"), m_commentFormat});
    m_highlightingRules.append({QRegularExpression("\\(.*\\)"), m_commentFormat});

    // Number format
    m_numberFormat.setForeground(Qt::darkMagenta);
    m_highlightingRules.append({QRegularExpression("\\b\\d+\\.?\\d*\\b"), m_numberFormat});
}

void GCodeSyntaxHighlighter::highlightBlock(const QString& text)
{
    for (const HighlightingRule& rule : m_highlightingRules) {
        QRegularExpressionMatchIterator matchIterator = rule.pattern.globalMatch(text);
        while (matchIterator.hasNext()) {
            QRegularExpressionMatch match = matchIterator.next();
            setFormat(match.capturedStart(), match.capturedLength(), rule.format);
        }
    }
}

// LineNumberArea implementation
LineNumberArea::LineNumberArea(QPlainTextEdit* editor)
    : QWidget(editor)
    , m_codeEditor(editor)
{
}

QSize LineNumberArea::sizeHint() const
{
    return QSize(dynamic_cast<GCodeEditor*>(m_codeEditor)->lineNumberAreaWidth(), 0);
}

void LineNumberArea::paintEvent(QPaintEvent* event)
{
    QPainter painter(this);
    painter.fillRect(event->rect(), QColor(240, 240, 240));

    GCodeEditor* editor = dynamic_cast<GCodeEditor*>(m_codeEditor);
    if (!editor) {
        return;
    }

    QTextBlock block = m_codeEditor->document()->findBlockByLineNumber(0);
    int blockNumber = block.blockNumber();
    int top = static_cast<int>(editor->blockBoundingGeometryPublic(block).translated(editor->contentOffsetPublic()).top());
    int bottom = top + static_cast<int>(editor->blockBoundingRectPublic(block).height());
    int currentLine = editor->currentLine();

    while (block.isValid() && top <= event->rect().bottom()) {
        if (block.isVisible() && bottom >= event->rect().top()) {
            QString number = QString::number(blockNumber + 1);

            // Highlight the current line number
            if (blockNumber + 1 == currentLine) {
                painter.fillRect(0, top, width(), m_codeEditor->fontMetrics().height(), QColor(255, 255, 200));
                painter.setPen(Qt::black);
                painter.drawText(0, top, width() - 5, m_codeEditor->fontMetrics().height(), Qt::AlignRight, "▶ " + number);
            } else {
                painter.setPen(Qt::gray);
                painter.drawText(0, top, width() - 5, m_codeEditor->fontMetrics().height(), Qt::AlignRight, number);
            }
        }

        block = block.next();
        top = bottom;
        bottom = top + static_cast<int>(editor->blockBoundingRectPublic(block).height());
        ++blockNumber;
    }
}

// GCodeEditor implementation
GCodeEditor::GCodeEditor(QWidget* parent)
    : QPlainTextEdit(parent)
    , m_currentLineNumber(0)
{
    m_lineNumberArea = new LineNumberArea(this);
    m_highlighter = new GCodeSyntaxHighlighter(document());

    connect(this, &QPlainTextEdit::blockCountChanged, this, &GCodeEditor::updateLineNumberAreaWidth);
    connect(this, &QPlainTextEdit::updateRequest, this, &GCodeEditor::updateLineNumberArea);
    connect(this, &QPlainTextEdit::cursorPositionChanged, this, &GCodeEditor::highlightCurrentLine);

    updateLineNumberAreaWidth(0);
    highlightCurrentLine();

    // Set font to monospace
    QFont font("Courier New", 10);
    font.setStyleHint(QFont::Monospace);
    setFont(font);

    // Set tab width to 4 spaces
    QFontMetrics metrics(font);
    setTabStopDistance(4 * metrics.horizontalAdvance(' '));

    // Disable line wrapping to prevent synchronization issues
    // This ensures each logical G-code line corresponds to exactly one visual line
    setLineWrapMode(QPlainTextEdit::NoWrap);

    // Enable horizontal scrollbar when content exceeds visible width
    setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
}

QRectF GCodeEditor::blockBoundingGeometryPublic(const QTextBlock& block) const
{
    return blockBoundingGeometry(block);
}

QRectF GCodeEditor::blockBoundingRectPublic(const QTextBlock& block) const
{
    return blockBoundingRect(block);
}

QPointF GCodeEditor::contentOffsetPublic() const
{
    return contentOffset();
}

int GCodeEditor::lineNumberAreaWidth() const
{
    int digits = 1;
    int max = qMax(1, blockCount());
    while (max >= 10) {
        max /= 10;
        ++digits;
    }

    // Add space for the arrow indicator
    int space = 3 + (fontMetrics().horizontalAdvance(QLatin1Char('9')) * (digits + 2));
    return space;
}

void GCodeEditor::updateLineNumberAreaWidth(int /* newBlockCount */)
{
    setViewportMargins(lineNumberAreaWidth(), 0, 0, 0);
}

void GCodeEditor::updateLineNumberArea(const QRect& rect, int dy)
{
    if (dy) {
        m_lineNumberArea->scroll(0, dy);
    } else {
        m_lineNumberArea->update(0, rect.y(), m_lineNumberArea->width(), rect.height());
    }

    if (rect.contains(viewport()->rect())) {
        updateLineNumberAreaWidth(0);
    }
}

void GCodeEditor::resizeEvent(QResizeEvent* e)
{
    QPlainTextEdit::resizeEvent(e);

    QRect cr = contentsRect();
    m_lineNumberArea->setGeometry(QRect(cr.left(), cr.top(), lineNumberAreaWidth(), cr.height()));
}

void GCodeEditor::highlightCurrentLine()
{
    QList<QTextEdit::ExtraSelection> extraSelections;

    if (!isReadOnly()) {
        QTextEdit::ExtraSelection selection;

        QColor lineColor = QColor(Qt::yellow).lighter(180);

        selection.format.setBackground(lineColor);
        selection.format.setProperty(QTextFormat::FullWidthSelection, true);
        selection.cursor = textCursor();
        selection.cursor.clearSelection();
        extraSelections.append(selection);
    }

    setExtraSelections(extraSelections);
}

void GCodeEditor::setCurrentLine(int lineNumber)
{
    if (lineNumber <= 0 || lineNumber > blockCount()) {
        return;
    }

    m_currentLineNumber = lineNumber;

    // Move cursor to the specified line
    QTextCursor cursor(document()->findBlockByLineNumber(lineNumber - 1));
    setTextCursor(cursor);

    // Ensure the line is visible
    centerCursor();

    // Update the line number area to show the arrow
    m_lineNumberArea->update();
}

int GCodeEditor::currentLine() const
{
    return m_currentLineNumber;
}

} // namespace UI
} // namespace Vizion3D
