#include "ui/axes_renderer.h"
#include "ui/opengl_state.h"
#include "utils/logger.h"
#include <QDebug>
#include <QFile>

namespace Vizion3D {
namespace UI {

AxesRenderer::AxesRenderer()
    : m_axisLength(50.0f)
    , m_arrowSize(5.0f)
    , m_xAxisColor(1.0f, 0.0f, 0.0f)
    , m_yAxisColor(0.0f, 1.0f, 0.0f)
    , m_zAxisColor(0.0f, 0.0f, 1.0f)
    , m_showLabels(false)
    , m_shaderProgram(nullptr)
    , m_initialized(false)
    , m_needsUpdate(true)
{
    // Initialize OpenGL buffers
    m_axesVertexVBO.create();
    m_axesColorVBO.create();
}

AxesRenderer::~AxesRenderer()
{
    // Clean up OpenGL resources
    if (m_initialized) {
        // Clean up axes buffers
        m_axesVAO.destroy();
        m_axesVertexVBO.destroy();
        m_axesColorVBO.destroy();

        // Delete shader program
        deleteShaderProgram(m_shaderProgram);
    }
}

void AxesRenderer::initialize()
{
    VLOG_DEBUG("OpenGL", "AxesRenderer::initialize called");

    // Create shader program
    createShaderProgram();

    // Generate vertices
    generateAxesVertices();

    // Setup buffers
    setupAxesBuffers();

    m_initialized = true;
    m_needsUpdate = false;

    VLOG_DEBUG("OpenGL", "AxesRenderer initialized");
}

void AxesRenderer::render(const QMatrix4x4& viewMatrix, const QMatrix4x4& projectionMatrix)
{
    if (!m_initialized) {
        initialize();
    }

    if (m_needsUpdate) {
        // Regenerate vertices and update buffers
        generateAxesVertices();
        setupAxesBuffers();

        m_needsUpdate = false;
    }

    VLOG_DEBUG("OpenGL", "AxesRenderer::render called");

    // Check if shader program is valid
    if (!m_shaderProgram) {
        VLOG_WARNING("OpenGL", "Cannot render axes: shader program is null");
        return;
    }

    // Depth testing is already enabled globally in SimulationView::initializeGL()

    // Calculate model-view-projection matrix
    QMatrix4x4 modelMatrix;
    modelMatrix.setToIdentity();
    QMatrix4x4 mvp = projectionMatrix * viewMatrix * modelMatrix;

    // Bind shader program
    m_shaderProgram->bind();

    // Set uniform values
    m_shaderProgram->setUniformValue("modelViewProjection", mvp);

    // Get OpenGL state manager for centralized state management
    OpenGLState& glState = OpenGLState::instance();

    // Draw axes
    glState.setLineWidth(2.0f);
    m_axesVAO.bind();
    glDrawArrays(GL_LINES, 0, m_axesVertices.size());

    // Draw labels if requested
    if (m_showLabels) {
        glState.setPointSize(5.0f);
        // Draw the last 3 vertices as points (the end points of the axes)
        glDrawArrays(GL_POINTS, m_axesVertices.size() - 3, 3);
    }

    m_axesVAO.release();

    // Release shader program
    m_shaderProgram->release();

    VLOG_DEBUG("OpenGL", "AxesRenderer::render completed");
}

void AxesRenderer::createShaderProgram()
{
    VLOG_DEBUG("OpenGL", "AxesRenderer::createShaderProgram called");

    // Load shader sources
    QString vertexShaderSource = loadShaderSource("shaders/axes.vert");
    QString fragmentShaderSource = loadShaderSource("shaders/axes.frag");

    if (vertexShaderSource.isEmpty() || fragmentShaderSource.isEmpty()) {
        VLOG_WARNING("OpenGL", "Failed to load shader sources, using default shaders");

        // Use default shaders if loading failed
        vertexShaderSource =
            "#version 410 core\n"
            "layout(location = 0) in vec3 position;\n"
            "layout(location = 1) in vec3 color;\n"
            "uniform mat4 modelViewProjection;\n"
            "out vec3 fragColor;\n"
            "void main() {\n"
            "    gl_Position = modelViewProjection * vec4(position, 1.0);\n"
            "    fragColor = color;\n"
            "}\n";

        fragmentShaderSource =
            "#version 410 core\n"
            "in vec3 fragColor;\n"
            "out vec4 outColor;\n"
            "void main() {\n"
            "    outColor = vec4(fragColor, 1.0);\n"
            "}\n";
    }

    // Create shader program
    m_shaderProgram = new QOpenGLShaderProgram();

    // Add vertex shader
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Vertex, vertexShaderSource)) {
        VLOG_ERROR("OpenGL", QString("Failed to compile vertex shader: %1").arg(m_shaderProgram->log()));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    // Add fragment shader
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Fragment, fragmentShaderSource)) {
        VLOG_ERROR("OpenGL", QString("Failed to compile fragment shader: %1").arg(m_shaderProgram->log()));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    // Link shader program
    if (!m_shaderProgram->link()) {
        VLOG_ERROR("OpenGL", QString("Failed to link shader program: %1").arg(m_shaderProgram->log()));
        delete m_shaderProgram;
        m_shaderProgram = nullptr;
        return;
    }

    VLOG_DEBUG("OpenGL", "Shader program created successfully");
}

void AxesRenderer::generateAxesVertices()
{
    VLOG_DEBUG("OpenGL", "AxesRenderer::generateAxesVertices called");

    m_axesVertices.clear();
    m_axesColors.clear();

    // X axis (red)
    m_axesVertices.append(QVector3D(0.0f, 0.0f, 0.0f));
    m_axesVertices.append(QVector3D(m_axisLength, 0.0f, 0.0f));
    m_axesColors.append(m_xAxisColor);
    m_axesColors.append(m_xAxisColor);

    // Y axis (green)
    m_axesVertices.append(QVector3D(0.0f, 0.0f, 0.0f));
    m_axesVertices.append(QVector3D(0.0f, m_axisLength, 0.0f));
    m_axesColors.append(m_yAxisColor);
    m_axesColors.append(m_yAxisColor);

    // Z axis (blue)
    m_axesVertices.append(QVector3D(0.0f, 0.0f, 0.0f));
    m_axesVertices.append(QVector3D(0.0f, 0.0f, m_axisLength));
    m_axesColors.append(m_zAxisColor);
    m_axesColors.append(m_zAxisColor);

    // Add label points at the end of each axis
    if (m_showLabels) {
        // X axis label
        m_axesVertices.append(QVector3D(m_axisLength, 0.0f, 0.0f));
        m_axesColors.append(m_xAxisColor);

        // Y axis label
        m_axesVertices.append(QVector3D(0.0f, m_axisLength, 0.0f));
        m_axesColors.append(m_yAxisColor);

        // Z axis label
        m_axesVertices.append(QVector3D(0.0f, 0.0f, m_axisLength));
        m_axesColors.append(m_zAxisColor);
    }

    VLOG_DEBUG("OpenGL", QString("Generated %1 axes vertices").arg(m_axesVertices.size()));
}

void AxesRenderer::setupAxesBuffers()
{
    VLOG_DEBUG("OpenGL", "AxesRenderer::setupAxesBuffers called");

    // Safety check - don't proceed if there are no vertices
    if (m_axesVertices.isEmpty()) {
        VLOG_DEBUG("OpenGL", "No axes vertices to set up buffers for");
        return;
    }

    // Make sure we have matching colors
    if (m_axesColors.size() != m_axesVertices.size()) {
        VLOG_WARNING("OpenGL", QString("Warning: Axes colors size (%1) doesn't match vertices size (%2)").arg(m_axesColors.size()).arg(m_axesVertices.size()));
        // Fill with default color if needed
        while (m_axesColors.size() < m_axesVertices.size()) {
            m_axesColors.append(m_xAxisColor); // Default to X axis color
        }
    }

    // Create and bind VAO using the base class method
    if (!createAndBindVAO(m_axesVAO)) {
        VLOG_ERROR("OpenGL", "Failed to create and bind VAO for axes");
        return;
    }

    // Upload vertex data
    m_axesVertexVBO.bind();
    m_axesVertexVBO.allocate(m_axesVertices.constData(), m_axesVertices.size() * sizeof(QVector3D));

    // Set vertex attribute pointers
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);

    // Upload color data
    m_axesColorVBO.bind();
    m_axesColorVBO.allocate(m_axesColors.constData(), m_axesColors.size() * sizeof(QVector3D));

    // Set color attribute pointers
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, sizeof(QVector3D), nullptr);

    // Release VAO
    m_axesVAO.release();

    VLOG_DEBUG("OpenGL", QString("Axes buffers set up with %1 vertices").arg(m_axesVertices.size()));
}

void AxesRenderer::setAxisLength(float length)
{
    if (m_axisLength != length) {
        m_axisLength = length;
        m_needsUpdate = true;
    }
}

void AxesRenderer::setArrowSize(float size)
{
    if (m_arrowSize != size) {
        m_arrowSize = size;
        m_needsUpdate = true;
    }
}

void AxesRenderer::setAxisColors(const QVector3D& xColor, const QVector3D& yColor, const QVector3D& zColor)
{
    if (m_xAxisColor != xColor || m_yAxisColor != yColor || m_zAxisColor != zColor) {
        m_xAxisColor = xColor;
        m_yAxisColor = yColor;
        m_zAxisColor = zColor;
        m_needsUpdate = true;
    }
}

void AxesRenderer::setShowLabels(bool show)
{
    if (m_showLabels != show) {
        m_showLabels = show;
        m_needsUpdate = true;
    }
}

} // namespace UI
} // namespace Vizion3D
