#include "ui/debug_console_widget.h"

// Qt Core includes
#include <QtCore/QDateTime>
#include <QtCore/QDir>
#include <QtCore/QFile>
#include <QtCore/QMutex>
#include <QtCore/QMutexLocker>
#include <QtCore/QStandardPaths>
#include <QtCore/QTextStream>
#include <QtCore/QTimer>

// Qt Widgets includes
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QFileDialog>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QPlainTextEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QScrollBar>
#include <QtWidgets/QVBoxLayout>

// Qt Gui includes
#include <QtGui/QColor>
#include <QtGui/QTextCharFormat>
#include <QtGui/QTextCursor>
#include <QtGui/QTextDocument>

namespace Vizion3D {
namespace UI {

// DebugConsoleLogSink implementation
DebugConsoleLogSink::DebugConsoleLogSink(QPlainTextEdit* textEdit)
    : m_textEdit(textEdit)
    , m_maxLines(10000)
    , m_showTimestamps(true)
    , m_showCategories(true)
    , m_showFileInfo(false)
    , m_showFunctions(false)
    , m_showThreadIds(false)
    , m_minLogLevel(Utils::LogLevel::Debug) {

    // Use new-style connect syntax
    connect(&m_updateTimer, &QTimer::timeout, this, &DebugConsoleLogSink::processPendingEntries);
    m_updateTimer.start(100); // Update every 100ms to avoid UI freezing
}




void DebugConsoleLogSink::write(const QDateTime& timestamp,
                               Utils::LogLevel level,
                               const QString& category,
                               const QString& message,
                               const QString& file,
                               int line,
                               const QString& function,
                               Qt::HANDLE threadId) {
    // Check if the message should be logged based on the level and category filter
    if (level < m_minLogLevel) {
        return;
    }

    if (!m_categoryFilter.isEmpty() && !category.contains(m_categoryFilter, Qt::CaseInsensitive)) {
        return;
    }

    QMutexLocker locker(&m_mutex);

    // Add the log entry to the pending queue
    LogEntry entry;
    entry.timestamp = timestamp;
    entry.level = level;
    entry.category = category;
    entry.message = message;
    entry.file = file;
    entry.line = line;
    entry.function = function;
    entry.threadId = threadId;

    m_pendingEntries.enqueue(entry);
}

void DebugConsoleLogSink::flush() {
    processPendingEntries();
}

void DebugConsoleLogSink::setMaxLines(int maxLines) {
    QMutexLocker locker(&m_mutex);
    m_maxLines = maxLines;
}

int DebugConsoleLogSink::maxLines() const {
    // Create a non-const copy of the mutex for QMutexLocker
    QMutex& mutex = const_cast<QMutex&>(m_mutex);
    QMutexLocker locker(&mutex);
    return m_maxLines;
}

void DebugConsoleLogSink::setShowTimestamps(bool show) {
    QMutexLocker locker(&m_mutex);
    m_showTimestamps = show;
}

bool DebugConsoleLogSink::showTimestamps() const {
    // Create a non-const copy of the mutex for QMutexLocker
    QMutex& mutex = const_cast<QMutex&>(m_mutex);
    QMutexLocker locker(&mutex);
    return m_showTimestamps;
}

void DebugConsoleLogSink::setShowCategories(bool show) {
    QMutexLocker locker(&m_mutex);
    m_showCategories = show;
}

bool DebugConsoleLogSink::showCategories() const {
    // Create a non-const copy of the mutex for QMutexLocker
    QMutex& mutex = const_cast<QMutex&>(m_mutex);
    QMutexLocker locker(&mutex);
    return m_showCategories;
}
void DebugConsoleLogSink::setShowFileInfo(bool show) {
    QMutexLocker locker(&m_mutex);
    m_showFileInfo = show;
}

bool DebugConsoleLogSink::showFileInfo() const {
    // Create a non-const copy of the mutex for QMutexLocker
    QMutex& mutex = const_cast<QMutex&>(m_mutex);
    QMutexLocker locker(&mutex);
    return m_showFileInfo;
}

void DebugConsoleLogSink::setShowFunctions(bool show) {
    QMutexLocker locker(&m_mutex);
    m_showFunctions = show;
}

bool DebugConsoleLogSink::showFunctions() const {
    // Create a non-const copy of the mutex for QMutexLocker
    QMutex& mutex = const_cast<QMutex&>(m_mutex);
    QMutexLocker locker(&mutex);
    return m_showFunctions;
}

void DebugConsoleLogSink::setShowThreadIds(bool show) {
    QMutexLocker locker(&m_mutex);
    m_showThreadIds = show;
}

bool DebugConsoleLogSink::showThreadIds() const {
    // Create a non-const copy of the mutex for QMutexLocker
    QMutex& mutex = const_cast<QMutex&>(m_mutex);
    QMutexLocker locker(&mutex);
    return m_showThreadIds;
}
void DebugConsoleLogSink::setMinLogLevel(Utils::LogLevel level) {
    QMutexLocker locker(&m_mutex);
    m_minLogLevel = level;
}

Utils::LogLevel DebugConsoleLogSink::minLogLevel() const {
    // Create a non-const copy of the mutex for QMutexLocker
    QMutex& mutex = const_cast<QMutex&>(m_mutex);
    QMutexLocker locker(&mutex);
    return m_minLogLevel;
}

void DebugConsoleLogSink::setCategoryFilter(const QString& filter) {
    QMutexLocker locker(&m_mutex);
    m_categoryFilter = filter;
}

QString DebugConsoleLogSink::categoryFilter() const {
    // Create a non-const copy of the mutex for QMutexLocker
    QMutex& mutex = const_cast<QMutex&>(m_mutex);
    QMutexLocker locker(&mutex);
    return m_categoryFilter;
}

void DebugConsoleLogSink::clear() {
    QMutexLocker locker(&m_mutex);
    m_pendingEntries.clear();

    // Safely access m_textEdit from the UI thread to avoid freezing
    if (m_textEdit) {
        // Use QMetaObject::invokeMethod to safely call clear() from the UI thread
        QMetaObject::invokeMethod(m_textEdit, "clear", Qt::QueuedConnection);
    }
}
void DebugConsoleLogSink::processPendingEntries() {
    QQueue<LogEntry> entriesToProcess;

    {
        QMutexLocker locker(&m_mutex);

        if (m_pendingEntries.isEmpty()) {
            return;
        }

        // Move all pending entries to a local queue to process them without holding the mutex
        entriesToProcess = m_pendingEntries;
        m_pendingEntries.clear();
    }

    // Process all entries without holding the mutex
    // This prevents blocking other threads from adding new entries while we process existing ones
    for (const LogEntry& entry : entriesToProcess) {
        processLogEntry(entry);
    }
}

void DebugConsoleLogSink::processLogEntry(const LogEntry& entry) {
    if (!m_textEdit) {
        return;
    }

    QString formattedEntry = formatLogEntry(entry);
    QColor textColor = levelToColor(entry.level);

    // Use QMetaObject::invokeMethod to safely update the UI from any thread
    QMetaObject::invokeMethod(m_textEdit, [this, formattedEntry, textColor]() {
        // This lambda will be executed in the UI thread
        if (!m_textEdit) {
            return;
        }

        // Add the entry to the text edit
        QTextCursor cursor(m_textEdit->document());
        cursor.movePosition(QTextCursor::End);

        QTextCharFormat format;
        format.setForeground(textColor);

        cursor.insertText(formattedEntry + "\n", format);

        // Limit the number of lines
        QTextDocument* doc = m_textEdit->document();
        while (doc->blockCount() > m_maxLines) {
            cursor = QTextCursor(doc);
            cursor.movePosition(QTextCursor::Start);
            cursor.select(QTextCursor::BlockUnderCursor);
            cursor.removeSelectedText();
            cursor.deleteChar(); // Delete the newline
        }

        // Scroll to the bottom if the scroll bar was at the bottom before adding the new text
        QScrollBar* scrollBar = m_textEdit->verticalScrollBar();
        if (scrollBar) {
            bool wasAtBottom = scrollBar->value() == scrollBar->maximum();
            if (wasAtBottom) {
                // Use a single-shot timer to delay the scrolling slightly
                // This helps prevent UI freezing when rapidly updating the text
                QTimer::singleShot(0, [scrollBar]() {
                    scrollBar->setValue(scrollBar->maximum());
                });
            }
        }
    }, Qt::QueuedConnection);
}

QString DebugConsoleLogSink::formatLogEntry(const LogEntry& entry) const {
    QString result;

    // Add timestamp if enabled
    if (m_showTimestamps) {
        result += entry.timestamp.toString("yyyy-MM-dd hh:mm:ss.zzz") + " ";
    }

    // Add log level
    switch (entry.level) {
        case Utils::LogLevel::Trace:   result += "[TRACE] "; break;
        case Utils::LogLevel::Debug:   result += "[DEBUG] "; break;
        case Utils::LogLevel::Info:    result += "[INFO] "; break;
        case Utils::LogLevel::Warning: result += "[WARNING] "; break;
        case Utils::LogLevel::Error:   result += "[ERROR] "; break;
        case Utils::LogLevel::Fatal:   result += "[FATAL] "; break;
        default:                       result += "[UNKNOWN] "; break;
    }

    // Add category if enabled
    if (m_showCategories && !entry.category.isEmpty()) {
        result += "[" + entry.category + "] ";
    }

    // Add file info if enabled
    if (m_showFileInfo && !entry.file.isEmpty()) {
        result += entry.file;
        if (entry.line > 0) {
            result += ":" + QString::number(entry.line);
        }
        result += " ";
    }

    // Add function if enabled
    if (m_showFunctions && !entry.function.isEmpty()) {
        result += entry.function + "() ";
    }

    // Add thread ID if enabled
    if (m_showThreadIds) {
        result += "[Thread " + QString::number(reinterpret_cast<quintptr>(entry.threadId)) + "] ";
    }

    result += entry.message;

    return result;
}

QColor DebugConsoleLogSink::levelToColor(Utils::LogLevel level) const {
    switch (level) {
        case Utils::LogLevel::Trace:   return QColor(128, 128, 128); // Gray
        case Utils::LogLevel::Debug:   return QColor(0, 128, 0);     // Green
        case Utils::LogLevel::Info:    return QColor(0, 0, 0);       // Black
        case Utils::LogLevel::Warning: return QColor(255, 165, 0);   // Orange
        case Utils::LogLevel::Error:   return QColor(255, 0, 0);     // Red
        case Utils::LogLevel::Fatal:   return QColor(128, 0, 128);   // Purple
        default:                       return QColor(0, 0, 0);       // Black
    }
}

// DebugConsoleWidget implementation
DebugConsoleWidget::DebugConsoleWidget(QWidget* parent)
    : QWidget(parent) {
    initializeUi();
}

DebugConsoleWidget::~DebugConsoleWidget() {
    // No need to delete Qt objects with parent-child relationship
}
void DebugConsoleWidget::initializeUi() {
    // Create the text edit
    m_textEdit = new QPlainTextEdit(this);
    m_textEdit->setReadOnly(true);
    m_textEdit->setLineWrapMode(QPlainTextEdit::NoWrap);
    m_textEdit->setMaximumBlockCount(10000); // Limit the number of lines for performance

    // Create the log sink
    m_logSink = std::make_shared<DebugConsoleLogSink>(m_textEdit);
    Utils::Logger::instance().addSink(m_logSink);

    // Create the controls
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    QHBoxLayout* controlsLayout = new QHBoxLayout();

    // Log level combo box
    controlsLayout->addWidget(new QLabel(tr("Log Level:")));
    m_logLevelCombo = new QComboBox();
    m_logLevelCombo->addItem(tr("Trace"), static_cast<int>(Utils::LogLevel::Trace));
    m_logLevelCombo->addItem(tr("Debug"), static_cast<int>(Utils::LogLevel::Debug));
    m_logLevelCombo->addItem(tr("Info"), static_cast<int>(Utils::LogLevel::Info));
    m_logLevelCombo->addItem(tr("Warning"), static_cast<int>(Utils::LogLevel::Warning));
    m_logLevelCombo->addItem(tr("Error"), static_cast<int>(Utils::LogLevel::Error));
    m_logLevelCombo->addItem(tr("Fatal"), static_cast<int>(Utils::LogLevel::Fatal));
    m_logLevelCombo->setCurrentIndex(1); // Debug
    controlsLayout->addWidget(m_logLevelCombo);
    // Category filter
    controlsLayout->addWidget(new QLabel(tr("Category Filter:")));
    m_categoryFilterEdit = new QLineEdit();
    controlsLayout->addWidget(m_categoryFilterEdit);

    // Display options
    m_showTimestampsCheck = new QCheckBox(tr("Timestamps"));
    m_showTimestampsCheck->setChecked(true);
    controlsLayout->addWidget(m_showTimestampsCheck);

    m_showCategoriesCheck = new QCheckBox(tr("Categories"));
    m_showCategoriesCheck->setChecked(true);
    controlsLayout->addWidget(m_showCategoriesCheck);

    m_showFileInfoCheck = new QCheckBox(tr("File Info"));
    controlsLayout->addWidget(m_showFileInfoCheck);

    m_showFunctionsCheck = new QCheckBox(tr("Functions"));
    controlsLayout->addWidget(m_showFunctionsCheck);

    m_showThreadIdsCheck = new QCheckBox(tr("Thread IDs"));
    controlsLayout->addWidget(m_showThreadIdsCheck);

    // Buttons
    controlsLayout->addStretch();

    m_clearButton = new QPushButton(tr("Clear"));
    controlsLayout->addWidget(m_clearButton);

    m_saveButton = new QPushButton(tr("Save"));
    controlsLayout->addWidget(m_saveButton);

    // Add the controls and text edit to the main layout
    mainLayout->addLayout(controlsLayout);
    mainLayout->addWidget(m_textEdit);

    // Connect signals and slots using Qt6 syntax
    connect(m_clearButton, &QPushButton::clicked, this, &DebugConsoleWidget::onClearClicked);
    connect(m_saveButton, &QPushButton::clicked, this, &DebugConsoleWidget::onSaveClicked);

    // Use the new Qt6 connect syntax with QOverload
    connect(m_logLevelCombo,
            QOverload<int>::of(&QComboBox::currentIndexChanged),
            this,
            &DebugConsoleWidget::onLogLevelChanged);

    connect(m_categoryFilterEdit, &QLineEdit::textChanged,
            this, &DebugConsoleWidget::onCategoryFilterChanged);

    connect(m_showTimestampsCheck, &QCheckBox::toggled,
            this, &DebugConsoleWidget::onShowTimestampsChanged);

    connect(m_showCategoriesCheck, &QCheckBox::toggled,
            this, &DebugConsoleWidget::onShowCategoriesChanged);

    connect(m_showFileInfoCheck, &QCheckBox::toggled,
            this, &DebugConsoleWidget::onShowFileInfoChanged);

    connect(m_showFunctionsCheck, &QCheckBox::toggled,
            this, &DebugConsoleWidget::onShowFunctionsChanged);

    connect(m_showThreadIdsCheck, &QCheckBox::toggled,
            this, &DebugConsoleWidget::onShowThreadIdsChanged);
}



// These methods are already implemented above, removing duplicates

// This method is already implemented above, removing duplicate

// This method is already implemented above, removing duplicate

// These methods are already implemented above, removing duplicates

// These methods are already implemented above, removing duplicates

void DebugConsoleWidget::onClearClicked() {
    m_logSink->clear();
}

void DebugConsoleWidget::onSaveClicked() {
    QString defaultDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    QString defaultFileName = "Vizion3D_Debug_Log_" + QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss") + ".log";
    QString filePath = QFileDialog::getSaveFileName(this, tr("Save Debug Log"),
                                                  QDir(defaultDir).filePath(defaultFileName),
                                                  tr("Log Files (*.log);;Text Files (*.txt);;All Files (*)"));
    if (filePath.isEmpty()) {
        return;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::critical(this, tr("Error"), tr("Could not open file for writing: %1").arg(file.errorString()));
        return;
    }

    QTextStream out(&file);
    out << m_textEdit->toPlainText();
    file.close();

    QMessageBox::information(this, tr("Success"), tr("Debug log saved to %1").arg(filePath));
}

void DebugConsoleWidget::onLogLevelChanged(int index) {
    Utils::LogLevel level = static_cast<Utils::LogLevel>(m_logLevelCombo->itemData(index).toInt());
    m_logSink->setMinLogLevel(level);
}

void DebugConsoleWidget::onCategoryFilterChanged() {
    m_logSink->setCategoryFilter(m_categoryFilterEdit->text());
}

void DebugConsoleWidget::onShowTimestampsChanged(bool checked) {
    m_logSink->setShowTimestamps(checked);
}

void DebugConsoleWidget::onShowCategoriesChanged(bool checked) {
    m_logSink->setShowCategories(checked);
}

void DebugConsoleWidget::onShowFileInfoChanged(bool checked) {
    m_logSink->setShowFileInfo(checked);
}

void DebugConsoleWidget::onShowFunctionsChanged(bool checked) {
    m_logSink->setShowFunctions(checked);
}

void DebugConsoleWidget::onShowThreadIdsChanged(bool checked) {
    m_logSink->setShowThreadIds(checked);
}

} // namespace UI
} // namespace Vizion3D
