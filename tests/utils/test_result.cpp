#include <QtTest/QtTest>
#include <QString>
#include <QObject>
#include "utils/result.h"

using namespace Vizion3D::Utils;

/**
 * @brief Comprehensive test suite for the Result<T> template system
 *
 * This test suite covers all aspects of the Result<T> template including:
 * - Success and error creation
 * - Value and error access
 * - Template specializations
 * - Helper function correctness
 * - Integration with logging system
 * - Edge cases and error conditions
 */
class TestResult : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();

    // Result<T> template tests
    void testResultSuccessCreation();
    void testResultErrorCreation();
    void testResultValueAccess();
    void testResultErrorAccess();
    void testResultQueryMethods();
    void testResultConvenienceMethods();

    // Result<void> specialization tests
    void testResultVoidSuccessCreation();
    void testResultVoidErrorCreation();
    void testResultVoidQueryMethods();
    void testResultVoidErrorAccess();

    // Error struct tests
    void testErrorConstruction();
    void testErrorLogging();
    void testErrorCategoryAssignment();

    // Helper function tests
    void testErrorCodeToString();
    void testGetDefaultLogLevel();
    void testGetCategoryForErrorCode();

    // Edge cases and error conditions
    void testResultValueAccessOnError();
    void testResultErrorAccessOnSuccess();
    void testResultMapOperations();
    void testConvenienceMacros();

    // Integration tests
    void testLoggingIntegration();
    void testErrorPropagation();

private:
    void setupLogging();
};

void TestResult::initTestCase() {
    setupLogging();
}

void TestResult::cleanupTestCase() {
    // Cleanup if needed
}

void TestResult::setupLogging() {
    // Skip logging initialization for basic Result<T> tests
    // The Result<T> system will handle logging internally when needed
}

void TestResult::testResultSuccessCreation() {
    // Test creating successful results with different types
    auto intResult = Result<int>::success(42);
    QVERIFY(intResult.isSuccess());
    QVERIFY(!intResult.isError());
    QCOMPARE(intResult.value(), 42);

    auto stringResult = Result<QString>::success("test");
    QVERIFY(stringResult.isSuccess());
    QVERIFY(!stringResult.isError());
    QCOMPARE(stringResult.value(), QString("test"));
}

void TestResult::testResultErrorCreation() {
    // Test creating error results
    auto result = Result<int>::error(ErrorCode::InvalidArgument, "Test error message");
    QVERIFY(!result.isSuccess());
    QVERIFY(result.isError());
    QCOMPARE(result.error().code, ErrorCode::InvalidArgument);
    QCOMPARE(result.error().message, QString("Test error message"));
}

void TestResult::testResultValueAccess() {
    // Test value access on successful result
    auto result = Result<QString>::success("success value");
    QCOMPARE(result.value(), QString("success value"));

    // Test const value access
    const auto& constResult = result;
    QCOMPARE(constResult.value(), QString("success value"));
}

void TestResult::testResultErrorAccess() {
    // Test error access on failed result
    auto result = Result<int>::error(ErrorCode::FileNotFound, "File not found");
    const Error& error = result.error();
    QCOMPARE(error.code, ErrorCode::FileNotFound);
    QCOMPARE(error.message, QString("File not found"));
    QCOMPARE(error.category, QString("FileIO"));
}

void TestResult::testResultQueryMethods() {
    // Test query methods on success
    auto successResult = Result<int>::success(100);
    QVERIFY(successResult.isSuccess());
    QVERIFY(!successResult.isError());

    // Test query methods on error
    auto errorResult = Result<int>::error(ErrorCode::InvalidState, "Invalid state");
    QVERIFY(!errorResult.isSuccess());
    QVERIFY(errorResult.isError());
}

void TestResult::testResultConvenienceMethods() {
    // Test valueOr method
    auto successResult = Result<int>::success(42);
    QCOMPARE(successResult.valueOr(0), 42);

    auto errorResult = Result<int>::error(ErrorCode::InvalidArgument, "Error");
    QCOMPARE(errorResult.valueOr(99), 99);
}

void TestResult::testResultVoidSuccessCreation() {
    // Test creating successful void result
    auto result = Result<void>::success();
    QVERIFY(result.isSuccess());
    QVERIFY(!result.isError());
}

void TestResult::testResultVoidErrorCreation() {
    // Test creating error void result
    auto result = Result<void>::error(ErrorCode::OperationCancelled, "Operation was cancelled");
    QVERIFY(!result.isSuccess());
    QVERIFY(result.isError());
    QCOMPARE(result.error().code, ErrorCode::OperationCancelled);
    QCOMPARE(result.error().message, QString("Operation was cancelled"));
}

void TestResult::testResultVoidQueryMethods() {
    // Test query methods on void success
    auto successResult = Result<void>::success();
    QVERIFY(successResult.isSuccess());
    QVERIFY(!successResult.isError());

    // Test query methods on void error
    auto errorResult = Result<void>::error(ErrorCode::InternalError, "Internal error");
    QVERIFY(!errorResult.isSuccess());
    QVERIFY(errorResult.isError());
}

void TestResult::testResultVoidErrorAccess() {
    // Test error access on void result
    auto result = Result<void>::error(ErrorCode::ThreadTimeoutError, "Thread timeout");
    const Error& error = result.error();
    QCOMPARE(error.code, ErrorCode::ThreadTimeoutError);
    QCOMPARE(error.message, QString("Thread timeout"));
    QCOMPARE(error.category, QString("Threading"));
}

void TestResult::testErrorConstruction() {
    // Test Error construction with automatic category assignment
    Error error(ErrorCode::GCodeSyntaxError, "Syntax error in G-code");
    QCOMPARE(error.code, ErrorCode::GCodeSyntaxError);
    QCOMPARE(error.message, QString("Syntax error in G-code"));
    QCOMPARE(error.category, QString("GCode"));
    QVERIFY(!error.file.isEmpty());
    QVERIFY(error.line > 0);
    QVERIFY(!error.function.isEmpty());
}

void TestResult::testErrorLogging() {
    // Test that error logging method exists and returns correctly
    Error error(ErrorCode::OpenGLShaderCompileError, "Shader compilation failed");

    // The log method should not throw and should return a reference to the error
    // Note: We're not testing actual logging output in unit tests
    Error& loggedError = error.log(LogLevel::Error);
    QCOMPARE(&loggedError, &error);
}

void TestResult::testErrorCategoryAssignment() {
    // Test automatic category assignment for different error code ranges
    Error gcodeError(ErrorCode::GCodeParsingError, "G-code error");
    QCOMPARE(gcodeError.category, QString("GCode"));

    Error engineError(ErrorCode::SimulationNotInitialized, "Engine error");
    QCOMPARE(engineError.category, QString("Engine"));

    Error openglError(ErrorCode::OpenGLContextError, "OpenGL error");
    QCOMPARE(openglError.category, QString("OpenGL"));

    Error fileError(ErrorCode::FileNotFound, "File error");
    QCOMPARE(fileError.category, QString("FileIO"));
}

void TestResult::testErrorCodeToString() {
    // Test error code to string conversion
    QCOMPARE(errorCodeToString(ErrorCode::Success), QString("Success"));
    QCOMPARE(errorCodeToString(ErrorCode::GCodeSyntaxError), QString("GCodeSyntaxError"));
    QCOMPARE(errorCodeToString(ErrorCode::SimulationNotInitialized), QString("SimulationNotInitialized"));
    QCOMPARE(errorCodeToString(ErrorCode::OpenGLContextError), QString("OpenGLContextError"));
    QCOMPARE(errorCodeToString(ErrorCode::FileNotFound), QString("FileNotFound"));
    QCOMPARE(errorCodeToString(ErrorCode::OutOfMemory), QString("OutOfMemory"));
    QCOMPARE(errorCodeToString(ErrorCode::ThreadCreationFailed), QString("ThreadCreationFailed"));
    QCOMPARE(errorCodeToString(ErrorCode::InvalidArgument), QString("InvalidArgument"));
    QCOMPARE(errorCodeToString(ErrorCode::InternalError), QString("InternalError"));
}

void TestResult::testGetDefaultLogLevel() {
    // Test log level assignment for different error types
    QCOMPARE(getDefaultLogLevel(ErrorCode::OutOfMemory), LogLevel::Fatal);
    QCOMPARE(getDefaultLogLevel(ErrorCode::InternalError), LogLevel::Fatal);
    QCOMPARE(getDefaultLogLevel(ErrorCode::ThreadDeadlockDetected), LogLevel::Fatal);

    QCOMPARE(getDefaultLogLevel(ErrorCode::FileNotFound), LogLevel::Warning);
    QCOMPARE(getDefaultLogLevel(ErrorCode::NotImplemented), LogLevel::Warning);
    QCOMPARE(getDefaultLogLevel(ErrorCode::OperationCancelled), LogLevel::Warning);

    QCOMPARE(getDefaultLogLevel(ErrorCode::GCodeSyntaxError), LogLevel::Error);
    QCOMPARE(getDefaultLogLevel(ErrorCode::SimulationInvalidState), LogLevel::Error);
    QCOMPARE(getDefaultLogLevel(ErrorCode::OpenGLShaderCompileError), LogLevel::Error);
}

void TestResult::testGetCategoryForErrorCode() {
    // Test category assignment for different error code ranges
    QCOMPARE(getCategoryForErrorCode(ErrorCode::GCodeSyntaxError), QString("GCode"));
    QCOMPARE(getCategoryForErrorCode(ErrorCode::GCodeInvalidCommand), QString("GCode"));

    QCOMPARE(getCategoryForErrorCode(ErrorCode::SimulationNotInitialized), QString("Engine"));
    QCOMPARE(getCategoryForErrorCode(ErrorCode::SimulationAlreadyRunning), QString("Engine"));

    QCOMPARE(getCategoryForErrorCode(ErrorCode::OpenGLContextError), QString("OpenGL"));
    QCOMPARE(getCategoryForErrorCode(ErrorCode::OpenGLShaderCompileError), QString("OpenGL"));

    QCOMPARE(getCategoryForErrorCode(ErrorCode::FileNotFound), QString("FileIO"));
    QCOMPARE(getCategoryForErrorCode(ErrorCode::FilePermissionDenied), QString("FileIO"));

    QCOMPARE(getCategoryForErrorCode(ErrorCode::OutOfMemory), QString("System"));
    QCOMPARE(getCategoryForErrorCode(ErrorCode::ThreadCreationFailed), QString("Threading"));
    QCOMPARE(getCategoryForErrorCode(ErrorCode::InvalidArgument), QString("Validation"));
    QCOMPARE(getCategoryForErrorCode(ErrorCode::InternalError), QString("System"));
}

void TestResult::testResultValueAccessOnError() {
    // Test that accessing value on error result throws
    auto result = Result<int>::error(ErrorCode::InvalidState, "Error state");

    bool exceptionThrown = false;
    try {
        result.value();
    } catch (const std::runtime_error&) {
        exceptionThrown = true;
    }
    QVERIFY(exceptionThrown);
}

void TestResult::testResultErrorAccessOnSuccess() {
    // Test that accessing error on success result throws
    auto result = Result<int>::success(42);

    bool exceptionThrown = false;
    try {
        result.error();
    } catch (const std::runtime_error&) {
        exceptionThrown = true;
    }
    QVERIFY(exceptionThrown);
}

void TestResult::testResultMapOperations() {
    // Test map operation on success
    auto successResult = Result<int>::success(10);
    auto mappedResult = successResult.map([](int x) { return x * 2; });
    QVERIFY(mappedResult.isSuccess());
    QCOMPARE(mappedResult.value(), 20);

    // Test map operation on error
    auto errorResult = Result<int>::error(ErrorCode::InvalidArgument, "Error");
    auto mappedErrorResult = errorResult.map([](int x) { return x * 2; });
    QVERIFY(mappedErrorResult.isError());
    QCOMPARE(mappedErrorResult.error().code, ErrorCode::InvalidArgument);
}

void TestResult::testConvenienceMacros() {
    // Test convenience macros
    auto result1 = VIZION3D_ERROR(ErrorCode::FileNotFound, "File not found");
    QVERIFY(result1.isError());
    QCOMPARE(result1.error().code, ErrorCode::FileNotFound);
    QCOMPARE(result1.error().message, QString("File not found"));

    auto result2 = VIZION3D_ERROR_WITH_CONTEXT(ErrorCode::InvalidArgument, "Invalid parameter", "Function context");
    QVERIFY(result2.isError());
    QCOMPARE(result2.error().code, ErrorCode::InvalidArgument);
    QCOMPARE(result2.error().message, QString("Invalid parameter"));
    QCOMPARE(result2.error().context, QString("Function context"));
}

void TestResult::testLoggingIntegration() {
    // Test that creating an error result works correctly
    // Note: Actual logging integration is tested in integration tests
    auto result = Result<int>::error(ErrorCode::GCodeSyntaxError, "Test logging integration");
    QVERIFY(result.isError());

    // Verify that the error was created correctly
    QCOMPARE(result.error().code, ErrorCode::GCodeSyntaxError);
    QCOMPARE(result.error().message, QString("Test logging integration"));
    QCOMPARE(result.error().category, QString("GCode"));
}

void TestResult::testErrorPropagation() {
    // Test error propagation through multiple operations
    auto createError = []() -> Result<int> {
        return Result<int>::error(ErrorCode::FileReadError, "Cannot read file");
    };

    auto processResult = [](const Result<int>& input) -> Result<QString> {
        if (input.isError()) {
            return Result<QString>::error(input.error());
        }
        return Result<QString>::success(QString::number(input.value()));
    };

    auto errorResult = createError();
    auto propagatedResult = processResult(errorResult);

    QVERIFY(propagatedResult.isError());
    QCOMPARE(propagatedResult.error().code, ErrorCode::FileReadError);
    QCOMPARE(propagatedResult.error().message, QString("Cannot read file"));
}

QTEST_MAIN(TestResult)
#include "test_result.moc"
