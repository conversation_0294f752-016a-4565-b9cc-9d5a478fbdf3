#include <QtTest>
#include "engine/gcode_interpreter.h"
#include "utils/result.h"

using namespace Vizion3D::Engine;
using namespace Vizion3D::Utils;

class GCodeInterpreterTest : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void testBasicParsing();
    void testRapidMove();
    void testLinearMove();
    void testArcMove();
    void testAbsoluteMode();
    void testRelativeMode();
    void testSimulateExecution();

private:
    GCodeInterpreter* m_interpreter;
};

void GCodeInterpreterTest::initTestCase()
{
    m_interpreter = new GCodeInterpreter();
}

void GCodeInterpreterTest::cleanupTestCase()
{
    delete m_interpreter;
}

void GCodeInterpreterTest::testBasicParsing()
{
    // Test basic G-code parsing
    QString gcode = "G0 X0 Y0 Z0\nG1 X10 Y10 Z-1 F100";
    auto parseResult = m_interpreter->parseGCode(gcode);

    QVERIFY(parseResult.isSuccess());

    auto toolpathResult = m_interpreter->getToolpath();
    QVERIFY(toolpathResult.isSuccess());
    QVector<ToolpathPoint> toolpath = toolpathResult.value();
    QVERIFY(!toolpath.isEmpty());

    // Should have 3 points: initial position, G0 move, and G1 move
    QCOMPARE(toolpath.size(), 3);

    // Check the G1 move
    ToolpathPoint lastPoint = toolpath.last();
    QCOMPARE(lastPoint.position(), QVector3D(10, 10, -1));
    QCOMPARE(lastPoint.moveType(), MoveType::Linear);
    QCOMPARE(lastPoint.feedRate(), 100.0);
}

void GCodeInterpreterTest::testRapidMove()
{
    // Test rapid move (G0)
    QString gcode = "G0 X50 Y25 Z10";
    auto parseResult = m_interpreter->parseGCode(gcode);

    QVERIFY(parseResult.isSuccess());

    auto toolpathResult = m_interpreter->getToolpath();
    QVERIFY(toolpathResult.isSuccess());
    QVector<ToolpathPoint> toolpath = toolpathResult.value();
    QVERIFY(!toolpath.isEmpty());

    // Should have 2 points: initial position and G0 move
    QCOMPARE(toolpath.size(), 2);

    // Check the G0 move
    ToolpathPoint lastPoint = toolpath.last();
    QCOMPARE(lastPoint.position(), QVector3D(50, 25, 10));
    QCOMPARE(lastPoint.moveType(), MoveType::Rapid);
    QCOMPARE(lastPoint.feedRate(), 0.0);
}

void GCodeInterpreterTest::testLinearMove()
{
    // Test linear move (G1)
    QString gcode = "G1 X30 Y15 Z-5 F200";
    auto parseResult = m_interpreter->parseGCode(gcode);

    QVERIFY(parseResult.isSuccess());

    auto toolpathResult = m_interpreter->getToolpath();
    QVERIFY(toolpathResult.isSuccess());
    QVector<ToolpathPoint> toolpath = toolpathResult.value();
    QVERIFY(!toolpath.isEmpty());

    // Should have 2 points: initial position and G1 move
    QCOMPARE(toolpath.size(), 2);

    // Check the G1 move
    ToolpathPoint lastPoint = toolpath.last();
    QCOMPARE(lastPoint.position(), QVector3D(30, 15, -5));
    QCOMPARE(lastPoint.moveType(), MoveType::Linear);
    QCOMPARE(lastPoint.feedRate(), 200.0);
}

void GCodeInterpreterTest::testArcMove()
{
    // Test arc move (G2 - clockwise)
    QString gcode = "G0 X10 Y10 Z0\nG2 X20 Y10 I5 J0 F150";
    auto parseResult = m_interpreter->parseGCode(gcode);

    QVERIFY(parseResult.isSuccess());

    auto toolpathResult = m_interpreter->getToolpath();
    QVERIFY(toolpathResult.isSuccess());
    QVector<ToolpathPoint> toolpath = toolpathResult.value();
    QVERIFY(!toolpath.isEmpty());

    // Should have at least 3 points: initial position, G0 move, and multiple G2 arc segments
    QVERIFY(toolpath.size() >= 3);

    // Check the last point of the arc
    ToolpathPoint lastPoint = toolpath.last();
    QCOMPARE(lastPoint.position(), QVector3D(20, 10, 0));
    QCOMPARE(lastPoint.moveType(), MoveType::ArcCW);
    QCOMPARE(lastPoint.feedRate(), 150.0);

    // Test arc move (G3 - counter-clockwise)
    gcode = "G0 X10 Y10 Z0\nG3 X20 Y10 I5 J0 F150";
    parseResult = m_interpreter->parseGCode(gcode);

    QVERIFY(parseResult.isSuccess());

    toolpathResult = m_interpreter->getToolpath();
    QVERIFY(toolpathResult.isSuccess());
    toolpath = toolpathResult.value();
    QVERIFY(!toolpath.isEmpty());

    // Should have at least 3 points: initial position, G0 move, and multiple G3 arc segments
    QVERIFY(toolpath.size() >= 3);

    // Check the last point of the arc
    lastPoint = toolpath.last();
    QCOMPARE(lastPoint.position(), QVector3D(20, 10, 0));
    QCOMPARE(lastPoint.moveType(), MoveType::ArcCCW);
    QCOMPARE(lastPoint.feedRate(), 150.0);
}

void GCodeInterpreterTest::testAbsoluteMode()
{
    // Test absolute mode (G90)
    QString gcode = "G90\nG0 X10 Y10 Z0\nG1 X20 Y20 Z-1 F100";
    auto parseResult = m_interpreter->parseGCode(gcode);

    QVERIFY(parseResult.isSuccess());

    auto toolpathResult = m_interpreter->getToolpath();
    QVERIFY(toolpathResult.isSuccess());
    QVector<ToolpathPoint> toolpath = toolpathResult.value();
    QVERIFY(!toolpath.isEmpty());

    // Should have 3 points: initial position, G0 move, and G1 move
    QCOMPARE(toolpath.size(), 3);

    // Check the G1 move
    ToolpathPoint lastPoint = toolpath.last();
    QCOMPARE(lastPoint.position(), QVector3D(20, 20, -1));
}

void GCodeInterpreterTest::testRelativeMode()
{
    // Test relative mode (G91)
    QString gcode = "G0 X0 Y0 Z0\nG91\nG1 X10 Y10 Z-1 F100\nG1 X5 Y5 Z-1 F100";
    auto parseResult = m_interpreter->parseGCode(gcode);

    QVERIFY(parseResult.isSuccess());

    auto toolpathResult = m_interpreter->getToolpath();
    QVERIFY(toolpathResult.isSuccess());
    QVector<ToolpathPoint> toolpath = toolpathResult.value();
    QVERIFY(!toolpath.isEmpty());

    // Should have 4 points: initial position, G0 move, and two G1 moves
    QCOMPARE(toolpath.size(), 4);

    // Check the last G1 move (should be relative to the previous position)
    ToolpathPoint lastPoint = toolpath.last();
    QCOMPARE(lastPoint.position(), QVector3D(15, 15, -2));
}

void GCodeInterpreterTest::testSimulateExecution()
{
    // Test simulate execution
    QString gcode = "G0 X0 Y0 Z0\nG1 X100 Y0 Z0 F600";
    auto parseResult = m_interpreter->parseGCode(gcode);

    QVERIFY(parseResult.isSuccess());

    // Simulate with 0.1 second time step
    auto simulationResult = m_interpreter->simulateExecution(0.1);
    QVERIFY(simulationResult.isSuccess());
    QVector<ToolpathPoint> interpolatedPath = simulationResult.value();

    // Should have more points than the original toolpath due to interpolation
    QVERIFY(interpolatedPath.size() > 2);

    // Check the first and last points
    QCOMPARE(interpolatedPath.first().position(), QVector3D(0, 0, 0));
    QCOMPARE(interpolatedPath.last().position(), QVector3D(100, 0, 0));

    // Check that intermediate points are along the line
    for (int i = 1; i < interpolatedPath.size() - 1; ++i) {
        QVector3D pos = interpolatedPath[i].position();
        QVERIFY(pos.y() == 0);
        QVERIFY(pos.z() == 0);
        QVERIFY(pos.x() > 0 && pos.x() < 100);
    }
}

QTEST_APPLESS_MAIN(GCodeInterpreterTest)
#include "gcode_interpreter_test.moc"
