#include <QApplication>
#include <QOpenGLWidget>
#include <QOpenGLFunctions_4_1_Core>
#include <QSurfaceFormat>
#include <QDebug>
#include <QMainWindow>

class TestGLWidget : public QOpenGLWidget, protected QOpenGLFunctions_4_1_Core
{
public:
    TestGLWidget(QWidget* parent = nullptr) : QOpenGLWidget(parent) {}

protected:
    void initializeGL() override
    {
        qDebug() << "TestGLWidget::initializeGL called";
        
        // Initialize OpenGL functions
        if (!initializeOpenGLFunctions()) {
            qCritical() << "Failed to initialize OpenGL functions!";
            return;
        }
        
        // Get OpenGL version information
        QString glVersion = reinterpret_cast<const char*>(glGetString(GL_VERSION));
        QString glRenderer = reinterpret_cast<const char*>(glGetString(GL_RENDERER));
        QString glVendor = reinterpret_cast<const char*>(glGetString(GL_VENDOR));
        QString glShadingLanguageVersion = reinterpret_cast<const char*>(glGetString(GL_SHADING_LANGUAGE_VERSION));
        
        qDebug() << "OpenGL Version:" << glVersion;
        qDebug() << "OpenGL Renderer:" << glRenderer;
        qDebug() << "OpenGL Vendor:" << glVendor;
        qDebug() << "GLSL Version:" << glShadingLanguageVersion;
        
        // Set clear color
        glClearColor(0.2f, 0.3f, 0.3f, 1.0f);
    }
    
    void paintGL() override
    {
        qDebug() << "TestGLWidget::paintGL called";
        glClear(GL_COLOR_BUFFER_BIT);
    }
    
    void resizeGL(int width, int height) override
    {
        qDebug() << "TestGLWidget::resizeGL called with" << width << "x" << height;
        glViewport(0, 0, width, height);
    }
};

int main(int argc, char *argv[])
{
    // Set application information
    QCoreApplication::setApplicationName("OpenGL Test");
    QCoreApplication::setOrganizationName("Vizion3D");
    
    // Create application
    QApplication app(argc, argv);
    
    // Set up OpenGL format
    QSurfaceFormat format;
    format.setVersion(4, 1);
    format.setProfile(QSurfaceFormat::CoreProfile);
    format.setDepthBufferSize(24);
    format.setStencilBufferSize(8);
    format.setSamples(4);
    QSurfaceFormat::setDefaultFormat(format);
    
    qDebug() << "Setting up OpenGL format:" << format.version().first << "." << format.version().second
             << (format.profile() == QSurfaceFormat::CoreProfile ? " Core" : " Compatibility");
    
    // Create main window
    QMainWindow mainWindow;
    mainWindow.setWindowTitle("OpenGL Test");
    mainWindow.resize(800, 600);
    
    // Create OpenGL widget
    TestGLWidget* glWidget = new TestGLWidget(&mainWindow);
    mainWindow.setCentralWidget(glWidget);
    
    // Show main window
    mainWindow.show();
    
    // Run application
    return app.exec();
}
