cmake_minimum_required(VERSION 3.16)
project(TestOpenGL VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Find Qt packages
find_package(Qt6 6.2 COMPONENTS Core Gui Widgets OpenGL OpenGLWidgets QUIET)
if(NOT Qt6_FOUND)
    message(STATUS "Qt6 not found, trying Qt5...")
    find_package(Qt5 5.15 COMPONENTS Core Gui Widgets OpenGL REQUIRED)
    set(QT_VERSION 5)
else()
    set(QT_VERSION 6)
endif()

# Create executable
add_executable(TestOpenGL test_opengl.cpp)

# Link Qt libraries
if(QT_VERSION EQUAL 6)
    target_link_libraries(TestOpenGL PRIVATE
        Qt6::Core
        Qt6::Gui
        Qt6::Widgets
        Qt6::OpenGL
        Qt6::OpenGLWidgets
    )
else()
    target_link_libraries(TestOpenGL PRIVATE
        Qt5::Core
        Qt5::Gui
        Qt5::Widgets
        Qt5::OpenGL
    )
endif()

# Set output directory
set_target_properties(TestOpenGL PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)