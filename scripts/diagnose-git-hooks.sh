#!/bin/bash
# scripts/diagnose-git-hooks.sh
# Diagnostic script for Git hooks configuration issues
#
# This script analyzes the current Git hooks setup and provides
# guidance for resolving conflicts with pre-commit installation.

set -e

echo "🔍 Diagnosing Git hooks configuration..."
echo "========================================"

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "❌ Error: Not in a git repository"
    exit 1
fi

echo ""
echo "📋 Current Git Configuration:"
echo "----------------------------"

# Check core.hooksPath setting
HOOKS_PATH=$(git config --get core.hooksPath 2>/dev/null || echo "not set")
echo "core.hooksPath: $HOOKS_PATH"

# Check if hooksPath directory exists
if [[ "$HOOKS_PATH" != "not set" ]]; then
    if [[ -d "$HOOKS_PATH" ]]; then
        echo "✅ Hooks directory exists: $HOOKS_PATH"
        echo "📁 Contents:"
        ls -la "$HOOKS_PATH" 2>/dev/null || echo "  (empty or inaccessible)"
    else
        echo "❌ Hooks directory missing: $HOOKS_PATH"
    fi
fi

echo ""
echo "📋 Standard Git Hooks Directory:"
echo "--------------------------------"
GIT_HOOKS_DIR=".git/hooks"
if [[ -d "$GIT_HOOKS_DIR" ]]; then
    echo "✅ Standard hooks directory exists: $GIT_HOOKS_DIR"

    # Check for active hooks (non-sample files)
    ACTIVE_HOOKS=$(find "$GIT_HOOKS_DIR" -type f ! -name "*.sample" 2>/dev/null || true)
    if [[ -n "$ACTIVE_HOOKS" ]]; then
        echo "🔧 Active hooks found:"
        echo "$ACTIVE_HOOKS" | while read -r hook; do
            echo "  - $(basename "$hook")"
        done
    else
        echo "📝 Only sample hooks found (no active hooks)"
    fi
else
    echo "❌ Standard hooks directory missing: $GIT_HOOKS_DIR"
fi

echo ""
echo "📋 Pre-commit Status:"
echo "--------------------"

# Check if pre-commit is installed
if command -v pre-commit >/dev/null 2>&1; then
    echo "✅ pre-commit framework installed: $(pre-commit --version)"

    # Check if pre-commit is configured
    if [[ -f ".pre-commit-config.yaml" ]]; then
        echo "✅ pre-commit configuration found: .pre-commit-config.yaml"
    else
        echo "📝 No pre-commit configuration found"
    fi

    # Check if pre-commit hooks are installed
    if pre-commit --help >/dev/null 2>&1; then
        if [[ -f ".git/hooks/pre-commit" ]] && grep -q "pre-commit" ".git/hooks/pre-commit" 2>/dev/null; then
            echo "✅ pre-commit hooks appear to be installed"
        else
            echo "📝 pre-commit hooks not installed"
        fi
    fi
else
    echo "❌ pre-commit framework not installed"
fi

echo ""
echo "📋 Vizion3D Quality Checks:"
echo "--------------------------"

# Check if our quality check scripts exist
QUALITY_SCRIPTS=(
    "scripts/pre-commit/check-logging.sh"
    "scripts/pre-commit/check-result-pattern.py"
    "scripts/pre-commit/build-check.sh"
    "scripts/pre-commit/quick-check.sh"
    "scripts/pre-commit/medium-check.sh"
)

for script in "${QUALITY_SCRIPTS[@]}"; do
    if [[ -f "$script" ]] && [[ -x "$script" ]]; then
        echo "✅ $script (executable)"
    elif [[ -f "$script" ]]; then
        echo "⚠️  $script (not executable)"
    else
        echo "❌ $script (missing)"
    fi
done

echo ""
echo "🎯 Diagnosis Summary:"
echo "===================="

# Provide diagnosis and recommendations
if [[ "$HOOKS_PATH" != "not set" ]] && [[ ! -d "$HOOKS_PATH" ]]; then
    echo "❌ ISSUE FOUND: core.hooksPath points to missing directory"
    echo "   Current setting: $HOOKS_PATH"
    echo "   This prevents pre-commit from installing hooks"
    echo ""
    echo "🔧 RECOMMENDED SOLUTION:"
    echo "   1. Remove the invalid hooksPath setting:"
    echo "      git config --unset core.hooksPath"
    echo "   2. Re-run the quality checks setup:"
    echo "      ./scripts/setup-quality-checks.sh"
    echo ""
elif [[ "$HOOKS_PATH" != "not set" ]] && [[ -d "$HOOKS_PATH" ]]; then
    echo "⚠️  POTENTIAL CONFLICT: Custom hooks directory in use"
    echo "   Current setting: $HOOKS_PATH"
    echo "   This may conflict with pre-commit installation"
    echo ""
    echo "🔧 RECOMMENDED SOLUTIONS:"
    echo "   Option A - Migrate to pre-commit:"
    echo "     1. Backup existing hooks: cp -r $HOOKS_PATH $HOOKS_PATH.backup"
    echo "     2. Remove hooksPath: git config --unset core.hooksPath"
    echo "     3. Install pre-commit: ./scripts/setup-quality-checks.sh"
    echo ""
    echo "   Option B - Keep existing setup:"
    echo "     1. Manually integrate quality checks with existing hooks"
    echo "     2. Add calls to our scripts in your existing hook files"
    echo ""
else
    echo "✅ No Git hooks configuration conflicts detected"
    if command -v pre-commit >/dev/null 2>&1; then
        echo "   Ready to install pre-commit hooks"
        echo "   Run: ./scripts/setup-quality-checks.sh"
    else
        echo "   Install pre-commit first: pip install pre-commit"
    fi
fi

echo ""
echo "📖 For more help:"
echo "   - Integration guide: docs/enhancements/integration_strategy.md"
echo "   - Setup script: ./scripts/setup-quality-checks.sh"
echo "   - Manual resolution: ./scripts/fix-git-hooks.sh"
