# Vizion3D Multi-Platform Build Verification System

> **⚠️ MIGRATION NOTICE**: This legacy build verification system has been **superseded** by the new automated code quality systems:
> - **Pre-commit hooks**: Fast, staged-file analysis in `scripts/pre-commit/`
> - **GitHub Actions CI/CD**: Comprehensive PR verification in `.github/workflows/pr-build-verification.yml`
> - **Modern app verification**: `scripts/ci/verify-app-launch.sh` with cross-platform support
>
> **Recommendation**: Use the new systems for current development. See `docs/development/automated_code_quality.md` for complete documentation.

This directory contains the legacy Multi-Platform Build Verification system for Vizion3D, designed to ensure code changes don't break compilation or basic application functionality across supported platforms.

## Overview

The build verification system provides automated testing of:
- **Clean Build Verification**: Ensures 100% successful compilation from scratch
- **Application Startup Verification**: Validates that the application launches and initializes correctly
- **Log Pattern Verification**: Confirms expected startup log messages are generated
- **Cross-Platform Support**: Works on macOS, Windows, and Linux
- **CI/CD Integration**: Designed for both local development and automated pipelines

## Components

### Core Scripts

- **`multi-platform-build-verification.sh`** - Main orchestration script
- **`verify-application-startup.sh`** - Application launch and verification logic
- **`platform-utils.sh`** - Platform detection and utility functions
- **`log-pattern-checker.sh`** - Log pattern verification utilities

### Integration

- Uses existing **`../pre-commit/build-check.sh`** for compilation verification
- Integrates with the Vizion3D logging system (VLOG_* macros)
- Compatible with the existing CMake build system

## Usage

### Basic Usage

```bash
# Full verification (recommended)
./multi-platform-build-verification.sh

# Verbose output with detailed report
./multi-platform-build-verification.sh --verbose --report

# Only verify debug build
./multi-platform-build-verification.sh --build-type debug

# Skip build, only verify application startup
./multi-platform-build-verification.sh --skip-build
```

### Command Line Options

```
Options:
  -s, --skip-build         Skip build verification (use existing build)
  -a, --skip-app           Skip application startup verification
  -t, --build-type TYPE    Build type: debug, release, or both (default: both)
  -k, --keep-builds        Keep build directories after successful verification
  -r, --report             Generate detailed verification report
  -v, --verbose            Enable verbose output
  -h, --help               Show help message
```

### Examples

```bash
# Development workflow - quick verification
./multi-platform-build-verification.sh --build-type debug --verbose

# CI/CD pipeline - full verification with report
./multi-platform-build-verification.sh --report

# Debugging - keep build artifacts
./multi-platform-build-verification.sh --keep-builds --verbose

# Application-only testing (after manual build)
./multi-platform-build-verification.sh --skip-build --verbose
```

## Verification Process

### 1. Platform Detection and Validation
- Detects operating system (macOS, Windows, Linux)
- Validates required tools (cmake, compilers, etc.)
- Checks display availability for GUI testing

### 2. Build Verification
- Performs clean debug and release builds
- Runs static analysis (clang-tidy, cppcheck)
- Validates compilation with all warnings as errors

### 3. Application Startup Verification
- Launches the built application programmatically
- Captures application logs in real-time
- Verifies expected startup log patterns:
  ```
  [TIMESTAMP] [INFO] [UI] Creating main window
  [TIMESTAMP] [INFO] [UI] MainWindow created successfully, showing window
  ```
- Checks for error patterns and validates log format
- Gracefully terminates the application

### 4. Reporting and Cleanup
- Generates detailed verification reports (optional)
- Cleans up temporary build directories (configurable)
- Provides clear success/failure status with actionable error messages

## Expected Log Patterns

The system verifies these specific log patterns from the application startup:

1. **Main Window Creation**: `"Creating main window"`
2. **Successful Initialization**: `"MainWindow created successfully, showing window"`

These patterns correspond to the VLOG_INFO calls in `src/main.cpp`:
- Line 497: `VLOG_INFO("UI", "Creating main window");`
- Line 506: `VLOG_INFO("UI", "MainWindow created successfully, showing window");`

## Platform-Specific Behavior

### macOS
- Uses native executable paths
- Handles Xcode command line tools validation
- Supports both GUI and headless environments

### Windows
- Handles `.exe` file extensions
- Uses Windows-specific path conventions
- Supports MSVC and MinGW compilers

### Linux
- Supports virtual display setup (xvfb) for headless CI
- Validates build essentials and compilers
- Handles X11 and Wayland display systems

## Timeout Configuration

- **Application Startup**: 30 seconds
- **Log Verification**: 10 seconds
- **Graceful Shutdown**: 5 seconds
- **Build Process**: Inherited from build-check.sh

## Error Handling

The system provides comprehensive error handling for:
- Build compilation failures
- Application launch failures
- Missing dependencies
- Timeout conditions
- Platform-specific issues
- Log pattern verification failures

## CI/CD Integration

### Exit Codes
- `0`: All verifications passed
- `1`: One or more verifications failed

### Environment Detection
- Automatically detects CI environments (GitHub Actions, Jenkins, Travis, etc.)
- Adjusts behavior for headless environments
- Provides CI-friendly output formatting

### Artifacts
- Build directories (preserved on failure)
- Verification reports (when requested)
- Application logs (for debugging)

## Troubleshooting

### Common Issues

1. **Display Not Available**
   ```bash
   # Use virtual display on Linux
   export DISPLAY=:99
   Xvfb :99 -screen 0 1024x768x24 &
   ```

2. **Application Hangs**
   - Check for modal dialogs or user input prompts
   - Verify Qt application initialization
   - Review application logs for errors

3. **Log Patterns Not Found**
   - Ensure application reaches the expected initialization points
   - Check log file permissions and location
   - Verify VLOG_* macro configuration

4. **Build Failures**
   - Run `../pre-commit/build-check.sh` separately for detailed build diagnostics
   - Check CMake configuration and dependencies
   - Verify compiler and tool availability

### Debug Mode

Enable verbose output for detailed diagnostics:
```bash
./multi-platform-build-verification.sh --verbose --report --keep-builds
```

This will:
- Show detailed progress information
- Generate a comprehensive verification report
- Preserve build directories for inspection
- Display platform and environment information

## Integration with Quality Checks

This build verification system is part of the broader Vizion3D quality assurance framework:

- **High Priority**: Multi-Platform Build Verification (this system)
- **High Priority**: Logging Pattern Compliance
- **High Priority**: Result<T> Pattern Enforcement
- **Medium Priority**: Layer Separation Validation
- **Medium Priority**: Memory Safety Checks

See `docs/enhancements/commit_checks_plan.md` for the complete quality framework.
