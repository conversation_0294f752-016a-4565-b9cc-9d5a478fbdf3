#!/bin/bash
# scripts/build-verification/platform-utils.sh
# Platform detection and utility functions for Vizion3D build verification

# Platform detection
detect_platform() {
    case "$(uname -s)" in
        Darwin*)
            echo "macos"
            ;;
        MINGW*|CYGWIN*|MSYS*)
            echo "windows"
            ;;
        Linux*)
            echo "linux"
            ;;
        *)
            echo "unknown"
            ;;
    esac
}

# Get CPU count for parallel builds
get_cpu_count() {
    case "$(detect_platform)" in
        macos)
            sysctl -n hw.ncpu
            ;;
        linux)
            nproc
            ;;
        windows)
            echo "${NUMBER_OF_PROCESSORS:-4}"
            ;;
        *)
            echo "4"  # fallback
            ;;
    esac
}

# Get executable path based on platform
get_executable_path() {
    local build_dir="$1"
    local platform="$(detect_platform)"

    case "$platform" in
        windows)
            echo "${build_dir}/bin/Vizion3D.exe"
            ;;
        *)
            echo "${build_dir}/bin/Vizion3D"
            ;;
    esac
}

# Check if display is available for GUI applications
check_display_available() {
    local platform="$(detect_platform)"

    case "$platform" in
        macos)
            # On macOS, check if we're in a GUI session
            if [ -n "$DISPLAY" ] || [ -n "$TERM_PROGRAM" ]; then
                return 0
            else
                return 1
            fi
            ;;
        linux)
            # On Linux, check for X11 or Wayland
            if [ -n "$DISPLAY" ] || [ -n "$WAYLAND_DISPLAY" ]; then
                return 0
            else
                return 1
            fi
            ;;
        windows)
            # On Windows, assume display is available
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# Setup virtual display if needed and available
setup_virtual_display() {
    local platform="$(detect_platform)"

    if check_display_available; then
        echo "Display already available"
        return 0
    fi

    case "$platform" in
        linux)
            if command -v xvfb-run >/dev/null 2>&1; then
                echo "Setting up virtual display with xvfb"
                export DISPLAY=:99
                Xvfb :99 -screen 0 1024x768x24 &
                export XVFB_PID=$!
                sleep 2
                return 0
            else
                echo "Warning: No display available and xvfb not found"
                return 1
            fi
            ;;
        macos)
            echo "Warning: No display available on macOS"
            return 1
            ;;
        windows)
            echo "Warning: Display setup not implemented for Windows"
            return 1
            ;;
        *)
            return 1
            ;;
    esac
}

# Cleanup virtual display
cleanup_virtual_display() {
    if [ -n "$XVFB_PID" ]; then
        echo "Cleaning up virtual display (PID: $XVFB_PID)"
        kill "$XVFB_PID" 2>/dev/null || true
        unset XVFB_PID
    fi
}

# Kill process gracefully with timeout
kill_process_gracefully() {
    local pid="$1"
    local timeout="${2:-5}"
    local process_name="${3:-process}"

    if [ -z "$pid" ] || ! kill -0 "$pid" 2>/dev/null; then
        echo "Process $process_name (PID: $pid) is not running"
        return 0
    fi

    echo "Terminating $process_name (PID: $pid) gracefully..."
    kill -TERM "$pid" 2>/dev/null || true

    # Wait for graceful shutdown
    local count=0
    while [ $count -lt "$timeout" ] && kill -0 "$pid" 2>/dev/null; do
        sleep 1
        count=$((count + 1))
    done

    # Force kill if still running
    if kill -0 "$pid" 2>/dev/null; then
        echo "Force killing $process_name (PID: $pid)..."
        kill -KILL "$pid" 2>/dev/null || true
        sleep 1
    fi

    if kill -0 "$pid" 2>/dev/null; then
        echo "Warning: Failed to kill $process_name (PID: $pid)"
        return 1
    else
        echo "Successfully terminated $process_name"
        return 0
    fi
}

# Get platform-specific temp directory
get_temp_dir() {
    case "$(detect_platform)" in
        windows)
            echo "${TEMP:-/tmp}"
            ;;
        *)
            echo "${TMPDIR:-/tmp}"
            ;;
    esac
}

# Check if running in CI environment
is_ci_environment() {
    [ -n "$CI" ] || [ -n "$GITHUB_ACTIONS" ] || [ -n "$JENKINS_URL" ] || [ -n "$TRAVIS" ]
}

# Print platform information
print_platform_info() {
    local platform="$(detect_platform)"
    local cpu_count="$(get_cpu_count)"
    local display_available="no"

    if check_display_available; then
        display_available="yes"
    fi

    echo "Platform Information:"
    echo "  OS: $platform"
    echo "  CPU cores: $cpu_count"
    echo "  Display available: $display_available"
    echo "  CI environment: $(is_ci_environment && echo "yes" || echo "no")"
    echo "  Temp directory: $(get_temp_dir)"
}

# Validate platform requirements
validate_platform_requirements() {
    local platform="$(detect_platform)"
    local errors=0

    echo "Validating platform requirements..."

    # Check for required tools
    local required_tools="cmake"
    for tool in $required_tools; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            echo "Error: Required tool '$tool' not found"
            errors=$((errors + 1))
        fi
    done

    # Platform-specific checks
    case "$platform" in
        macos)
            # Check for Xcode tools
            if ! xcode-select -p >/dev/null 2>&1; then
                echo "Error: Xcode command line tools not installed"
                errors=$((errors + 1))
            fi
            ;;
        linux)
            # Check for build essentials
            if ! command -v gcc >/dev/null 2>&1 && ! command -v clang >/dev/null 2>&1; then
                echo "Error: No C++ compiler found (gcc or clang)"
                errors=$((errors + 1))
            fi
            ;;
        windows)
            # Check for Visual Studio or MinGW
            if ! command -v cl >/dev/null 2>&1 && ! command -v gcc >/dev/null 2>&1; then
                echo "Error: No C++ compiler found (MSVC or MinGW)"
                errors=$((errors + 1))
            fi
            ;;
    esac

    if [ $errors -eq 0 ]; then
        echo "✅ Platform requirements validated"
        return 0
    else
        echo "❌ Platform validation failed with $errors errors"
        return 1
    fi
}
