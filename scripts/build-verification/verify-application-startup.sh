#!/bin/bash
# scripts/build-verification/verify-application-startup.sh
# Application launch and verification logic for Vizion3D build verification

set -e

# Get script directory and source utilities
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/platform-utils.sh"
source "$SCRIPT_DIR/log-pattern-checker.sh"

# Configuration
STARTUP_TIMEOUT=30
LOG_VERIFICATION_TIMEOUT=10
SHUTDOWN_TIMEOUT=5
VERBOSE=false

# Usage information
usage() {
    echo "Usage: $0 [OPTIONS] <build_directory>"
    echo ""
    echo "Options:"
    echo "  -t, --timeout SECONDS    Application startup timeout (default: $STARTUP_TIMEOUT)"
    echo "  -l, --log-timeout SECONDS Log verification timeout (default: $LOG_VERIFICATION_TIMEOUT)"
    echo "  -v, --verbose            Enable verbose output"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Arguments:"
    echo "  build_directory          Path to the build directory containing the executable"
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--timeout)
                STARTUP_TIMEOUT="$2"
                shift 2
                ;;
            -l|--log-timeout)
                LOG_VERIFICATION_TIMEOUT="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            -*)
                echo "Error: Unknown option $1"
                usage
                exit 1
                ;;
            *)
                BUILD_DIR="$1"
                shift
                ;;
        esac
    done

    if [ -z "$BUILD_DIR" ]; then
        echo "Error: Build directory not specified"
        usage
        exit 1
    fi
}

# Verify application startup
verify_application_startup() {
    local build_dir="$1"
    local platform="$(detect_platform)"
    local executable="$(get_executable_path "$build_dir")"
    local temp_dir="$(get_temp_dir)"
    local log_file="$temp_dir/vizion3d_startup_test.log"
    local app_pid=""
    local virtual_display_setup=false

    echo "🚀 Starting application verification..."
    echo "Platform: $platform"
    echo "Executable: $executable"
    echo "Log file: $log_file"
    echo "Startup timeout: ${STARTUP_TIMEOUT}s"

    # Validate executable exists
    if [ ! -f "$executable" ]; then
        echo "❌ Executable not found: $executable"
        return 1
    fi

    # Make executable if needed (Unix systems)
    if [ "$platform" != "windows" ]; then
        chmod +x "$executable" 2>/dev/null || true
    fi

    # Setup virtual display if needed
    if ! check_display_available && ! is_ci_environment; then
        echo "Setting up virtual display..."
        if setup_virtual_display; then
            virtual_display_setup=true
        else
            echo "Warning: Could not setup virtual display, proceeding anyway..."
        fi
    fi

    # Cleanup function
    cleanup_application_test() {
        echo "Cleaning up application test..."

        # Kill application if still running
        if [ -n "$app_pid" ] && kill -0 "$app_pid" 2>/dev/null; then
            kill_process_gracefully "$app_pid" "$SHUTDOWN_TIMEOUT" "Vizion3D"
        fi

        # Cleanup virtual display
        if [ "$virtual_display_setup" = "true" ]; then
            cleanup_virtual_display
        fi

        # Remove log file
        rm -f "$log_file"
    }
    trap cleanup_application_test EXIT

    # Prepare log file
    rm -f "$log_file"
    touch "$log_file"

    echo "Launching application..."

    # Launch application with logging
    case "$platform" in
        windows)
            # Windows-specific launch
            "$executable" --test-mode > "$log_file" 2>&1 &
            app_pid=$!
            ;;
        *)
            # Unix-like systems
            "$executable" --test-mode > "$log_file" 2>&1 &
            app_pid=$!
            ;;
    esac

    if [ -z "$app_pid" ]; then
        echo "❌ Failed to launch application"
        return 1
    fi

    echo "Application launched with PID: $app_pid"

    # Wait for application to start and generate logs
    echo "Waiting for application startup..."
    local start_time=$(date +%s)
    local startup_complete=false

    while [ $startup_complete = false ]; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))

        # Check timeout
        if [ $elapsed -ge $STARTUP_TIMEOUT ]; then
            echo "❌ Application startup timeout (${elapsed}s)"
            return 1
        fi

        # Check if process is still running
        if ! kill -0 "$app_pid" 2>/dev/null; then
            echo "❌ Application process terminated unexpectedly"
            if [ -f "$log_file" ]; then
                echo "Application output:"
                cat "$log_file"
            fi
            return 1
        fi

        # Check if log file has content
        if [ -f "$log_file" ] && [ -s "$log_file" ]; then
            # Look for startup completion indicators
            if grep -q "MainWindow created successfully" "$log_file" 2>/dev/null; then
                startup_complete=true
                echo "✅ Application startup detected (${elapsed}s)"
            fi
        fi

        if [ "$VERBOSE" = "true" ]; then
            echo "Startup check: ${elapsed}s elapsed, process running: $(kill -0 "$app_pid" 2>/dev/null && echo "yes" || echo "no")"
        fi

        sleep 1
    done

    # Verify log patterns
    echo "Verifying log patterns..."
    if ! verify_log_patterns "$log_file" "$LOG_VERIFICATION_TIMEOUT" "$VERBOSE"; then
        echo "❌ Log pattern verification failed"
        if [ "$VERBOSE" = "true" ]; then
            echo "Log file contents:"
            cat "$log_file"
        fi
        return 1
    fi

    # Check for errors in logs
    echo "Checking for error patterns..."
    if ! check_error_patterns "$log_file" "$VERBOSE"; then
        echo "❌ Error patterns found in logs"
        return 1
    fi

    # Validate log format
    echo "Validating log format..."
    if ! validate_log_format "$log_file" "$VERBOSE"; then
        echo "❌ Log format validation failed"
        return 1
    fi

    echo "✅ Application verification completed successfully"

    # Generate report if verbose
    if [ "$VERBOSE" = "true" ]; then
        local report_file="$temp_dir/vizion3d_verification_report.txt"
        generate_log_report "$log_file" "$report_file"
        echo "Detailed report: $report_file"
    fi

    return 0
}

# Main function
main() {
    echo "Vizion3D Application Startup Verification"
    echo "=========================================="

    # Parse arguments
    parse_arguments "$@"

    # Print platform info if verbose
    if [ "$VERBOSE" = "true" ]; then
        print_platform_info
        echo ""
    fi

    # Validate platform requirements
    if ! validate_platform_requirements; then
        echo "❌ Platform requirements not met"
        exit 1
    fi

    # Verify build directory exists
    if [ ! -d "$BUILD_DIR" ]; then
        echo "❌ Build directory not found: $BUILD_DIR"
        exit 1
    fi

    # Run application verification
    if verify_application_startup "$BUILD_DIR"; then
        echo ""
        echo "🎉 Application startup verification PASSED"
        exit 0
    else
        echo ""
        echo "💥 Application startup verification FAILED"
        exit 1
    fi
}

# Run main function if script is executed directly
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
