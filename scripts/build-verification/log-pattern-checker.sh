#!/bin/bash
# scripts/build-verification/log-pattern-checker.sh
# Log pattern verification utilities for Vizion3D build verification

# Expected log patterns from main.cpp
declare -a EXPECTED_LOG_PATTERNS=(
    "Creating main window"
    "MainWindow created successfully, showing window"
)

# Log pattern verification with timeout
verify_log_patterns() {
    local log_file="$1"
    local timeout="${2:-10}"
    local verbose="${3:-false}"

    if [ ! -f "$log_file" ]; then
        echo "Error: Log file '$log_file' not found"
        return 1
    fi

    echo "Verifying log patterns in '$log_file'..."

    local patterns_found=0
    local total_patterns=${#EXPECTED_LOG_PATTERNS[@]}
    local start_time=$(date +%s)

    # Wait for patterns to appear with timeout
    while [ $patterns_found -lt $total_patterns ]; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))

        if [ $elapsed -ge $timeout ]; then
            echo "❌ Timeout waiting for log patterns (${elapsed}s)"
            break
        fi

        # Check each pattern
        for i in "${!EXPECTED_LOG_PATTERNS[@]}"; do
            local pattern="${EXPECTED_LOG_PATTERNS[$i]}"

            if grep -q "$pattern" "$log_file" 2>/dev/null; then
                if [ "$verbose" = "true" ]; then
                    echo "✅ Found pattern: '$pattern'"
                fi
                # Remove found pattern to avoid double counting
                unset "EXPECTED_LOG_PATTERNS[$i]"
                patterns_found=$((patterns_found + 1))
            fi
        done

        # Rebuild array to remove unset elements
        local temp_array=()
        for pattern in "${EXPECTED_LOG_PATTERNS[@]}"; do
            if [ -n "$pattern" ]; then
                temp_array+=("$pattern")
            fi
        done
        EXPECTED_LOG_PATTERNS=("${temp_array[@]}")

        sleep 0.5
    done

    # Report results
    echo "Log pattern verification results:"
    echo "  Patterns found: $patterns_found/$total_patterns"
    echo "  Time elapsed: ${elapsed}s"

    if [ $patterns_found -eq $total_patterns ]; then
        echo "✅ All expected log patterns found"
        return 0
    else
        echo "❌ Missing log patterns:"
        for pattern in "${EXPECTED_LOG_PATTERNS[@]}"; do
            if [ -n "$pattern" ]; then
                echo "  - '$pattern'"
            fi
        done
        return 1
    fi
}

# Extract and validate log format
validate_log_format() {
    local log_file="$1"
    local verbose="${2:-false}"

    if [ ! -f "$log_file" ]; then
        echo "Error: Log file '$log_file' not found"
        return 1
    fi

    echo "Validating log format..."

    # Expected log format: [TIMESTAMP] [LEVEL] [CATEGORY] [file:line] [function] [Thread ID] message
    local log_format_regex='^\[[0-9]{4}-[0-9]{2}-[0-9]{2}[[:space:]][0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]{3}\][[:space:]]\[[A-Z]+\][[:space:]]\[[A-Za-z]+\][[:space:]]\[[^]]+\][[:space:]]\[[^]]+\][[:space:]]\[[^]]+\]'

    local valid_lines=0
    local total_lines=0
    local invalid_lines=()

    while IFS= read -r line; do
        if [ -n "$line" ]; then
            total_lines=$((total_lines + 1))

            if echo "$line" | grep -E "$log_format_regex" >/dev/null 2>&1; then
                valid_lines=$((valid_lines + 1))
                if [ "$verbose" = "true" ]; then
                    echo "✅ Valid: $line"
                fi
            else
                invalid_lines+=("$line")
                if [ "$verbose" = "true" ]; then
                    echo "❌ Invalid: $line"
                fi
            fi
        fi
    done < "$log_file"

    echo "Log format validation results:"
    echo "  Total lines: $total_lines"
    echo "  Valid lines: $valid_lines"
    echo "  Invalid lines: ${#invalid_lines[@]}"

    if [ ${#invalid_lines[@]} -gt 0 ] && [ "$verbose" = "true" ]; then
        echo "Invalid log lines:"
        for line in "${invalid_lines[@]}"; do
            echo "  $line"
        done
    fi

    # Allow some invalid lines (startup messages, etc.)
    local invalid_threshold=5
    if [ ${#invalid_lines[@]} -le $invalid_threshold ]; then
        echo "✅ Log format validation passed (${#invalid_lines[@]} invalid lines within threshold)"
        return 0
    else
        echo "❌ Log format validation failed (${#invalid_lines[@]} invalid lines exceeds threshold of $invalid_threshold)"
        return 1
    fi
}

# Check for error patterns in logs
check_error_patterns() {
    local log_file="$1"
    local verbose="${2:-false}"

    if [ ! -f "$log_file" ]; then
        echo "Error: Log file '$log_file' not found"
        return 1
    fi

    echo "Checking for error patterns..."

    # Error patterns to look for
    local error_patterns=(
        "FATAL"
        "ERROR"
        "Exception"
        "Segmentation fault"
        "Assertion failed"
        "abort"
        "terminate"
    )

    local errors_found=0
    local error_lines=()

    for pattern in "${error_patterns[@]}"; do
        while IFS= read -r line; do
            if echo "$line" | grep -i "$pattern" >/dev/null 2>&1; then
                errors_found=$((errors_found + 1))
                error_lines+=("$line")
                if [ "$verbose" = "true" ]; then
                    echo "❌ Error found: $line"
                fi
            fi
        done < "$log_file"
    done

    echo "Error pattern check results:"
    echo "  Errors found: $errors_found"

    if [ $errors_found -eq 0 ]; then
        echo "✅ No error patterns found"
        return 0
    else
        echo "❌ Found $errors_found error patterns:"
        for line in "${error_lines[@]}"; do
            echo "  $line"
        done
        return 1
    fi
}

# Monitor log file for patterns in real-time
monitor_log_patterns() {
    local log_file="$1"
    local timeout="${2:-30}"
    local verbose="${3:-false}"

    echo "Monitoring log file '$log_file' for patterns (timeout: ${timeout}s)..."

    local start_time=$(date +%s)
    local patterns_found=0
    local total_patterns=${#EXPECTED_LOG_PATTERNS[@]}

    # Create log file if it doesn't exist
    touch "$log_file"

    # Monitor file with tail -f
    tail -f "$log_file" 2>/dev/null &
    local tail_pid=$!

    # Monitor for patterns
    while [ $patterns_found -lt $total_patterns ]; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))

        if [ $elapsed -ge $timeout ]; then
            echo "❌ Timeout monitoring log patterns (${elapsed}s)"
            kill $tail_pid 2>/dev/null || true
            return 1
        fi

        # Check for new patterns
        for i in "${!EXPECTED_LOG_PATTERNS[@]}"; do
            local pattern="${EXPECTED_LOG_PATTERNS[$i]}"

            if [ -n "$pattern" ] && tail -n 50 "$log_file" 2>/dev/null | grep -q "$pattern"; then
                if [ "$verbose" = "true" ]; then
                    echo "✅ Found pattern: '$pattern'"
                fi
                unset "EXPECTED_LOG_PATTERNS[$i]"
                patterns_found=$((patterns_found + 1))
            fi
        done

        # Rebuild array
        local temp_array=()
        for pattern in "${EXPECTED_LOG_PATTERNS[@]}"; do
            if [ -n "$pattern" ]; then
                temp_array+=("$pattern")
            fi
        done
        EXPECTED_LOG_PATTERNS=("${temp_array[@]}")

        sleep 0.5
    done

    kill $tail_pid 2>/dev/null || true

    echo "✅ All expected log patterns found in ${elapsed}s"
    return 0
}

# Generate log verification report
generate_log_report() {
    local log_file="$1"
    local output_file="${2:-log_verification_report.txt}"

    echo "Generating log verification report..."

    {
        echo "Vizion3D Log Verification Report"
        echo "================================"
        echo "Generated: $(date)"
        echo "Log file: $log_file"
        echo ""

        echo "Pattern Verification:"
        verify_log_patterns "$log_file" 10 true
        echo ""

        echo "Format Validation:"
        validate_log_format "$log_file" false
        echo ""

        echo "Error Check:"
        check_error_patterns "$log_file" true
        echo ""

        echo "Log File Contents:"
        echo "=================="
        cat "$log_file"

    } > "$output_file"

    echo "Report saved to: $output_file"
}
