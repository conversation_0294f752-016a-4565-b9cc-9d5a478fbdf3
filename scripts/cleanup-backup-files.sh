#!/bin/bash
# scripts/cleanup-backup-files.sh
# Clean up backup files created by logging fix scripts
#
# This script removes .bak files that may be left behind by sed operations
# in the logging fix scripts. These files are typically identical to the
# original files and are safe to remove.

set -e

echo "🧹 Cleaning up backup files..."

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

cd "$PROJECT_ROOT"

# Find all .bak files
BAK_FILES=$(find . -name "*.bak" -type f 2>/dev/null || true)

if [ -z "$BAK_FILES" ]; then
    echo "✅ No backup files found to clean up"
    exit 0
fi

echo "📁 Found backup files:"
echo "$BAK_FILES" | while read -r file; do
    echo "   - $file"
done

echo ""
echo "🤔 These files are typically created by sed operations in logging fix scripts."
echo "   They are usually identical to the original files and safe to remove."
echo ""
echo "Would you like to:"
echo "1) 🗑️  DELETE all backup files"
echo "2) 🔍 COMPARE files before deletion"
echo "3) 🚫 CANCEL (keep backup files)"
echo ""
read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo ""
        echo "🗑️  Deleting backup files..."
        echo "$BAK_FILES" | while read -r file; do
            rm -f "$file"
            echo "   ✅ Deleted: $file"
        done
        echo ""
        echo "✅ Cleanup complete!"
        ;;

    2)
        echo ""
        echo "🔍 Comparing backup files with originals..."
        echo ""

        DIFFERENCES_FOUND=false

        echo "$BAK_FILES" | while read -r bak_file; do
            original_file="${bak_file%.bak}"

            if [ -f "$original_file" ]; then
                if diff -q "$original_file" "$bak_file" >/dev/null 2>&1; then
                    echo "✅ $bak_file: Identical to original (safe to delete)"
                else
                    echo "⚠️  $bak_file: DIFFERS from original"
                    DIFFERENCES_FOUND=true
                fi
            else
                echo "❓ $bak_file: Original file not found"
            fi
        done

        echo ""
        if [ "$DIFFERENCES_FOUND" = true ]; then
            echo "⚠️  Some backup files differ from originals."
            echo "   Review the differences before deleting."
            echo "   Use 'diff <original> <backup>' to see changes."
        else
            echo "✅ All backup files are identical to originals."
            echo ""
            read -p "Delete all backup files? (y/N): " confirm
            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                echo "$BAK_FILES" | while read -r file; do
                    rm -f "$file"
                    echo "   ✅ Deleted: $file"
                done
                echo "✅ Cleanup complete!"
            else
                echo "🚫 Cleanup cancelled"
            fi
        fi
        ;;

    3)
        echo ""
        echo "🚫 Cleanup cancelled"
        echo "   Backup files preserved"
        ;;

    *)
        echo ""
        echo "❌ Invalid choice"
        exit 1
        ;;
esac
