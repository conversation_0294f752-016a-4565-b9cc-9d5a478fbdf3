#!/bin/bash
# scripts/setup-quality-checks.sh
# Setup script for Vizion3D automated quality checks
#
# This script helps developers set up the quality check system with
# different enforcement levels based on their preferences and project phase.

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "🔧 Vizion3D Quality Checks Setup"
echo "================================="
echo ""

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "❌ Error: Not in a git repository"
    echo "   Please run this script from the Vizion3D project root"
    exit 1
fi

# Check for Git hooks configuration conflicts
HOOKS_PATH=$(git config --get core.hooksPath 2>/dev/null || echo "")
if [[ -n "$HOOKS_PATH" ]]; then
    echo "⚠️  Git hooks configuration conflict detected!"
    echo "   core.hooksPath is set to: $HOOKS_PATH"
    echo ""
    echo "🔧 This needs to be resolved before installing quality checks."
    echo ""
    echo "Options:"
    echo "1) 🔍 DIAGNOSE - Analyze the current setup"
    echo "2) 🔧 AUTO-FIX - Automatically resolve the conflict"
    echo "3) 📖 MANUAL - Get instructions for manual resolution"
    echo "4) 🚫 CONTINUE - Proceed anyway (may fail)"
    echo ""
    read -p "Enter your choice (1-4): " hooks_choice

    case $hooks_choice in
        1)
            echo ""
            echo "🔍 Running diagnostic..."
            "$SCRIPT_DIR/diagnose-git-hooks.sh"
            echo ""
            echo "After reviewing the diagnosis, re-run this script to continue setup."
            exit 0
            ;;
        2)
            echo ""
            echo "🔧 Running automatic fix..."
            "$SCRIPT_DIR/fix-git-hooks.sh"
            echo ""
            echo "✅ Fix completed! Continuing with setup..."
            echo ""
            ;;
        3)
            echo ""
            echo "📖 Manual resolution instructions:"
            echo "   1. Run: git config --unset core.hooksPath"
            echo "   2. Re-run this script: ./scripts/setup-quality-checks.sh"
            echo ""
            echo "For detailed analysis: ./scripts/diagnose-git-hooks.sh"
            exit 0
            ;;
        4)
            echo ""
            echo "⚠️  Proceeding with existing configuration..."
            echo "   Note: Installation may fail due to hooks conflict"
            echo ""
            ;;
        *)
            echo "❌ Invalid choice. Exiting."
            exit 1
            ;;
    esac
fi

# Check if pre-commit is installed
if ! command -v pre-commit >/dev/null 2>&1; then
    echo "📦 Installing pre-commit framework..."
    if command -v pip3 >/dev/null 2>&1; then
        pip3 install pre-commit
    elif command -v pip >/dev/null 2>&1; then
        pip install pre-commit
    else
        echo "❌ Error: pip not found. Please install pre-commit manually:"
        echo "   https://pre-commit.com/#installation"
        exit 1
    fi
fi

echo "✅ pre-commit framework available"

# Present setup options
echo ""
echo "🎯 Choose your quality enforcement level:"
echo ""
echo "1) 🟢 MINIMAL (Recommended for existing projects)"
echo "   - Fast logging checks on commit"
echo "   - No blocking for existing violations"
echo "   - Gradual improvement approach"
echo ""
echo "2) 🟡 BALANCED (Recommended for active development)"
echo "   - Fast checks on commit"
echo "   - Medium checks on push"
echo "   - Allows urgent fixes with --no-verify"
echo ""
echo "3) 🔴 STRICT (Recommended for new features)"
echo "   - All checks on commit"
echo "   - Blocks all violations"
echo "   - Maximum quality enforcement"
echo ""
echo "4) 🔧 CUSTOM (Manual configuration)"
echo "   - Set up hooks manually"
echo "   - Full control over enforcement"
echo ""

read -p "Enter your choice (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🟢 Setting up MINIMAL enforcement..."

        # Install only fast checks
        pre-commit install --hook-type pre-commit

        # Create minimal config
        cat > "$PROJECT_ROOT/.pre-commit-config-minimal.yaml" << 'EOF'
repos:
  - repo: local
    hooks:
      - id: vizion3d-quick-check
        name: Vizion3D Quick Quality Check
        entry: scripts/pre-commit/quick-check.sh
        language: script
        pass_filenames: false
        stages: [commit]
        always_run: true

default_stages: [commit]
exclude: |
  (?x)^(
    build/.*|
    \.git/.*|
    test_opengl.*|
    .*\.autogen/.*
  )$
EOF

        # Use minimal config
        export PRE_COMMIT_CONFIG_FILE="$PROJECT_ROOT/.pre-commit-config-minimal.yaml"
        pre-commit install --config "$PROJECT_ROOT/.pre-commit-config-minimal.yaml"

        echo "✅ Minimal enforcement setup complete"
        echo "💡 Only fast logging checks will run on commit"
        ;;

    2)
        echo ""
        echo "🟡 Setting up BALANCED enforcement..."

        # Install both pre-commit and pre-push hooks
        pre-commit install --hook-type pre-commit
        pre-commit install --hook-type pre-push

        echo "✅ Balanced enforcement setup complete"
        echo "💡 Fast checks on commit, medium checks on push"
        ;;

    3)
        echo ""
        echo "🔴 Setting up STRICT enforcement..."

        # Install all hooks
        pre-commit install --hook-type pre-commit
        pre-commit install --hook-type pre-push

        # Create strict config that runs all checks on commit
        cat > "$PROJECT_ROOT/.pre-commit-config-strict.yaml" << 'EOF'
repos:
  - repo: local
    hooks:
      - id: vizion3d-all-checks
        name: Vizion3D All Quality Checks
        entry: scripts/pre-commit/medium-check.sh
        language: script
        pass_filenames: false
        stages: [commit]
        always_run: true

default_stages: [commit]
exclude: |
  (?x)^(
    build/.*|
    \.git/.*|
    test_opengl.*|
    .*\.autogen/.*
  )$
EOF

        # Use strict config
        export PRE_COMMIT_CONFIG_FILE="$PROJECT_ROOT/.pre-commit-config-strict.yaml"
        pre-commit install --config "$PROJECT_ROOT/.pre-commit-config-strict.yaml"

        echo "✅ Strict enforcement setup complete"
        echo "⚠️  All quality checks will run on every commit"
        ;;

    4)
        echo ""
        echo "🔧 Manual setup selected"
        echo "📖 Please refer to the documentation for manual configuration:"
        echo "   - .pre-commit-config.yaml (main configuration)"
        echo "   - docs/enhancements/phase1_quality_checks_implementation.md"
        exit 0
        ;;

    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Test the setup: git add . && git commit -m 'test quality checks'"
echo "2. If checks fail, fix violations or use: git commit --no-verify"
echo "3. Read the documentation: docs/enhancements/phase1_quality_checks_implementation.md"
echo ""
echo "🔧 To change enforcement level later, run this script again"
echo "🚫 To disable temporarily: git commit --no-verify"
echo "📖 For help with violations: scripts/pre-commit/README.md"
