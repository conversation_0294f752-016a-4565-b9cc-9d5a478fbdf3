#!/bin/bash
# scripts/fix-git-hooks.sh
# Automatic Git hooks configuration fix for Vizion3D
#
# This script safely resolves Git hooks configuration conflicts
# and prepares the repository for pre-commit installation.

set -e

echo "🔧 Fixing Git hooks configuration..."
echo "===================================="

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "❌ Error: Not in a git repository"
    exit 1
fi

# Function to backup existing hooks
backup_hooks() {
    local hooks_dir="$1"
    local backup_dir="$2"

    if [[ -d "$hooks_dir" ]]; then
        echo "📦 Backing up existing hooks..."
        mkdir -p "$backup_dir"
        cp -r "$hooks_dir"/* "$backup_dir/" 2>/dev/null || true
        echo "✅ Backup created: $backup_dir"
    fi
}

# Function to restore hooks from backup
restore_hooks() {
    local backup_dir="$1"
    local hooks_dir="$2"

    if [[ -d "$backup_dir" ]]; then
        echo "🔄 Restoring hooks from backup..."
        mkdir -p "$hooks_dir"
        cp -r "$backup_dir"/* "$hooks_dir/" 2>/dev/null || true
        echo "✅ Hooks restored to: $hooks_dir"
    fi
}

# Get current hooksPath setting
CURRENT_HOOKS_PATH=$(git config --get core.hooksPath 2>/dev/null || echo "")

if [[ -z "$CURRENT_HOOKS_PATH" ]]; then
    echo "✅ No custom hooksPath set, ready for pre-commit installation"
    exit 0
fi

echo "🔍 Current hooksPath: $CURRENT_HOOKS_PATH"

# Check if the hooks directory exists
if [[ -d "$CURRENT_HOOKS_PATH" ]]; then
    echo "📁 Custom hooks directory exists"

    # Check for active hooks
    ACTIVE_HOOKS=$(find "$CURRENT_HOOKS_PATH" -type f ! -name "*.sample" 2>/dev/null || true)

    if [[ -n "$ACTIVE_HOOKS" ]]; then
        echo "⚠️  Active hooks found in custom directory:"
        echo "$ACTIVE_HOOKS" | while read -r hook; do
            echo "  - $(basename "$hook")"
        done
        echo ""
        echo "🤔 How would you like to proceed?"
        echo ""
        echo "1) 🔄 MIGRATE - Move existing hooks to standard location and use pre-commit"
        echo "2) 🗑️  REMOVE - Remove custom hooksPath and lose existing hooks"
        echo "3) 🚫 CANCEL - Keep current setup (manual integration required)"
        echo ""
        read -p "Enter your choice (1-3): " choice

        case $choice in
            1)
                echo ""
                echo "🔄 Migrating existing hooks..."

                # Create backup
                BACKUP_DIR="$PROJECT_ROOT/.git/hooks-backup-$(date +%Y%m%d_%H%M%S)"
                backup_hooks "$CURRENT_HOOKS_PATH" "$BACKUP_DIR"

                # Move hooks to standard location
                restore_hooks "$CURRENT_HOOKS_PATH" "$PROJECT_ROOT/.git/hooks"

                # Remove custom hooksPath
                git config --unset core.hooksPath

                echo "✅ Migration complete!"
                echo "📦 Backup available at: $BACKUP_DIR"
                echo "🎯 Ready for pre-commit installation"
                ;;

            2)
                echo ""
                echo "🗑️  Removing custom hooksPath..."

                # Create backup just in case
                BACKUP_DIR="$PROJECT_ROOT/.git/hooks-backup-$(date +%Y%m%d_%H%M%S)"
                backup_hooks "$CURRENT_HOOKS_PATH" "$BACKUP_DIR"

                # Remove custom hooksPath
                git config --unset core.hooksPath

                echo "✅ Custom hooksPath removed!"
                echo "📦 Backup available at: $BACKUP_DIR"
                echo "🎯 Ready for pre-commit installation"
                ;;

            3)
                echo ""
                echo "🚫 Keeping current setup"
                echo "📖 For manual integration, see: docs/enhancements/integration_strategy.md"
                echo "🔧 You can call our quality scripts directly from your existing hooks"
                exit 0
                ;;

            *)
                echo "❌ Invalid choice. Exiting."
                exit 1
                ;;
        esac
    else
        echo "📝 No active hooks found in custom directory"
        echo "🗑️  Removing empty custom hooksPath..."
        git config --unset core.hooksPath
        echo "✅ Custom hooksPath removed!"
        echo "🎯 Ready for pre-commit installation"
    fi
else
    echo "❌ Custom hooksPath points to missing directory: $CURRENT_HOOKS_PATH"
    echo "🗑️  Removing invalid hooksPath..."
    git config --unset core.hooksPath
    echo "✅ Invalid hooksPath removed!"
    echo "🎯 Ready for pre-commit installation"
fi

echo ""
echo "🎉 Git hooks configuration fixed!"
echo ""
echo "📋 Next steps:"
echo "1. Run the quality checks setup: ./scripts/setup-quality-checks.sh"
echo "2. Choose your enforcement level (MINIMAL recommended)"
echo "3. Test the setup: git add . && git commit -m 'test quality checks'"
echo ""
echo "🔍 To verify the fix worked:"
echo "   git config --get core.hooksPath  # Should return nothing"
echo "   ./scripts/diagnose-git-hooks.sh  # Should show no conflicts"
