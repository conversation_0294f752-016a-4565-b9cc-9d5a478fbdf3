#!/bin/bash
# scripts/verify-quality-setup.sh
# Verification script for Vizion3D quality checks setup
#
# This script verifies that the quality checks are properly installed
# and working as expected.

set -e

echo "✅ Verifying Vizion3D quality checks setup..."
echo "============================================="

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Track verification results
VERIFICATION_PASSED=0

# Function to check a condition and report result
verify_condition() {
    local description="$1"
    local condition="$2"

    echo -n "🔍 $description... "

    if eval "$condition"; then
        echo "✅ PASS"
    else
        echo "❌ FAIL"
        VERIFICATION_PASSED=1
    fi
}

echo ""
echo "📋 Git Configuration Verification:"
echo "----------------------------------"

# Check that core.hooksPath is not set
verify_condition "Git hooksPath is not set" "! git config --get core.hooksPath >/dev/null 2>&1"

# Check that we're in a git repository
verify_condition "Git repository detected" "git rev-parse --git-dir >/dev/null 2>&1"

echo ""
echo "📋 Pre-commit Framework Verification:"
echo "------------------------------------"

# Check pre-commit is installed
verify_condition "Pre-commit framework installed" "command -v pre-commit >/dev/null 2>&1"

# Check pre-commit config exists
verify_condition "Pre-commit configuration exists" "[[ -f '.pre-commit-config.yaml' ]] || [[ -f '.pre-commit-config-minimal.yaml' ]]"

# Check pre-commit hooks are installed
verify_condition "Pre-commit hooks installed" "[[ -f '.git/hooks/pre-commit' ]] && grep -q 'pre-commit' '.git/hooks/pre-commit'"

echo ""
echo "📋 Quality Check Scripts Verification:"
echo "-------------------------------------"

# Check that our quality scripts exist and are executable
QUALITY_SCRIPTS=(
    "scripts/pre-commit/check-logging.sh"
    "scripts/pre-commit/check-result-pattern.py"
    "scripts/pre-commit/quick-check.sh"
    "scripts/pre-commit/medium-check.sh"
    "scripts/pre-commit/build-check.sh"
)

for script in "${QUALITY_SCRIPTS[@]}"; do
    script_name=$(basename "$script")
    verify_condition "$script_name exists and executable" "[[ -f '$script' ]] && [[ -x '$script' ]]"
done

echo ""
echo "📋 Functional Testing:"
echo "---------------------"

# Test quick check script (should run without errors on current codebase)
echo -n "🔍 Quick check script runs without errors... "
if "$SCRIPT_DIR/pre-commit/quick-check.sh" >/dev/null 2>&1; then
    echo "✅ PASS (no violations blocking)"
else
    # Check if it failed due to violations (expected) vs actual errors
    if "$SCRIPT_DIR/pre-commit/quick-check.sh" 2>&1 | grep -q "❌.*failed"; then
        echo "⚠️  EXPECTED (violations found - this is normal)"
    else
        echo "❌ FAIL (script error)"
        VERIFICATION_PASSED=1
    fi
fi

# Test that pre-commit can run (dry run)
echo -n "🔍 Pre-commit dry run works... "
if pre-commit run --all-files --dry-run >/dev/null 2>&1; then
    echo "✅ PASS"
else
    echo "⚠️  EXPECTED (violations may prevent dry run)"
fi

echo ""
echo "📋 Integration Verification:"
echo "---------------------------"

# Check if there are staged files that would trigger hooks
STAGED_FILES=$(git diff --cached --name-only 2>/dev/null || true)
if [[ -n "$STAGED_FILES" ]]; then
    echo "⚠️  Staged files detected - hooks will run on next commit"
    echo "   Staged files: $(echo "$STAGED_FILES" | wc -l) files"
else
    echo "ℹ️  No staged files - hooks will not run until files are staged"
fi

# Check current enforcement level
if [[ -f ".pre-commit-config-minimal.yaml" ]]; then
    echo "📊 Enforcement level: MINIMAL (fast logging checks only)"
elif [[ -f ".pre-commit-config-strict.yaml" ]]; then
    echo "📊 Enforcement level: STRICT (all checks on commit)"
else
    echo "📊 Enforcement level: BALANCED (fast on commit, medium on push)"
fi

echo ""
echo "🎯 Verification Summary:"
echo "======================="

if [[ $VERIFICATION_PASSED -eq 0 ]]; then
    echo "✅ ALL VERIFICATIONS PASSED!"
    echo ""
    echo "🎉 Quality checks are properly installed and ready to use."
    echo ""
    echo "📋 Next steps:"
    echo "1. Stage some files: git add ."
    echo "2. Test commit: git commit -m 'test quality checks'"
    echo "3. Observe quality check output"
    echo "4. Fix any violations or use --no-verify for urgent commits"
    echo ""
    echo "📖 Documentation:"
    echo "   - Setup guide: docs/enhancements/integration_strategy.md"
    echo "   - Violation fixes: scripts/pre-commit/README.md"
    echo "   - Implementation details: docs/enhancements/phase1_quality_checks_implementation.md"
else
    echo "❌ SOME VERIFICATIONS FAILED"
    echo ""
    echo "🔧 Troubleshooting steps:"
    echo "1. Re-run setup: ./scripts/setup-quality-checks.sh"
    echo "2. Check diagnostics: ./scripts/diagnose-git-hooks.sh"
    echo "3. Fix Git hooks: ./scripts/fix-git-hooks.sh"
    echo "4. Verify again: ./scripts/verify-quality-setup.sh"
    echo ""
    echo "📖 For help: docs/enhancements/integration_strategy.md"
fi

echo ""
echo "🔧 Useful commands:"
echo "   ./scripts/diagnose-git-hooks.sh     # Diagnose Git hooks issues"
echo "   ./scripts/setup-quality-checks.sh   # Change enforcement level"
echo "   git commit --no-verify              # Bypass checks temporarily"
echo "   pre-commit run --all-files          # Run checks manually"

exit $VERIFICATION_PASSED
