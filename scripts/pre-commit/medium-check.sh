#!/bin/bash
# scripts/pre-commit/medium-check.sh
# Medium-depth pre-push quality checks for Vizion3D
#
# This script runs AST analysis and pattern enforcement checks that are too slow
# for pre-commit but important enough to run before pushing to shared branches.

set -e

echo "🔍 Running medium-depth quality checks..."

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Track overall success
OVERALL_SUCCESS=0

# Function to run a check and track results
run_check() {
    local check_name="$1"
    local check_script="$2"
    shift 2

    echo ""
    echo "📋 Running $check_name..."

    if "$check_script" "$@"; then
        echo "✅ $check_name passed"
    else
        echo "❌ $check_name failed"
        OVERALL_SUCCESS=1
    fi
}

# Get list of files to check (staged + recently modified)
FILES_TO_CHECK=""
if git rev-parse --git-dir > /dev/null 2>&1; then
    # Get staged files
    STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM 2>/dev/null | grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' || true)

    # Get files modified in last commit (for push scenarios)
    RECENT_FILES=$(git diff HEAD~1 --name-only --diff-filter=ACM 2>/dev/null | grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' || true)

    # Combine and deduplicate
    FILES_TO_CHECK=$(echo -e "$STAGED_FILES\n$RECENT_FILES" | sort -u | grep -v '^$' || true)
fi

if [ -z "$FILES_TO_CHECK" ]; then
    echo "ℹ️  No relevant C++ files to check, running full analysis"
    FILES_TO_CHECK=""
fi

if [ -n "$FILES_TO_CHECK" ]; then
    echo "📁 Checking $(echo "$FILES_TO_CHECK" | wc -l) modified C++ files"
else
    echo "📁 Running full codebase analysis"
fi

# Run medium-depth checks
run_check "Logging Pattern Compliance" "$SCRIPT_DIR/check-logging.sh"

if [ -n "$FILES_TO_CHECK" ]; then
    # Pass specific files to Result<T> checker
    run_check "Result<T> Pattern Enforcement" python3 "$SCRIPT_DIR/check-result-pattern.py" $FILES_TO_CHECK
else
    # Run on full codebase
    run_check "Result<T> Pattern Enforcement" python3 "$SCRIPT_DIR/check-result-pattern.py"
fi

# Summary
echo ""
echo "📊 Medium Check Summary:"
if [ $OVERALL_SUCCESS -eq 0 ]; then
    echo "✅ All medium-depth checks passed!"
    echo ""
    echo "💡 Note: Full build verification will run in CI/CD pipeline"
    exit 0
else
    echo "❌ Some medium-depth checks failed"
    echo ""
    echo "🔧 Fix the issues above before pushing, or use:"
    echo "   git push --no-verify  # to bypass checks (not recommended)"
    echo ""
    echo "📖 For help fixing violations:"
    echo "   - Logging: docs/features/logging_system.md"
    echo "   - Result<T>: docs/implementation/direct_error_handling_implementation.md"
    echo "   - Patterns: docs/enhancements/phase1_quality_checks_implementation.md"
    exit 1
fi
