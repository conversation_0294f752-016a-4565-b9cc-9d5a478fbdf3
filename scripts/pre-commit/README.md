# Vizion3D Pre-commit Quality Checks

This directory contains automated code quality checks for the Vizion3D project, providing **fast, staged-file-only analysis** for efficient development workflow.

## 🚀 **Performance Optimized** (Latest Update)

**Before optimization**: 60+ seconds (scanned entire codebase)
**After optimization**: 0.3-0.6 seconds (staged files only)

All hooks now properly analyze **only staged files**, providing lightning-fast feedback during commits.

## Scripts Overview

### Active Pre-commit Hooks

1. **logging-precommit-hook.sh** - Logging Pattern Compliance with Auto-fix
   - **Performance**: ~0.3 seconds (no staged files), ~0.6 seconds (with files)
   - Detects deprecated logging patterns (qDebug, qWarning, std::cout, etc.)
   - Automatically fixes violations where possible
   - Validates VLOG_* category usage and includes
   - **Staged files only**: Never scans entire codebase

2. **clang-tidy-precommit-hook.sh** - Clang-Tidy Analysis
   - **Performance**: ~0.08 seconds (no staged files), 10-30 seconds (with files)
   - Runs clang-tidy analysis on staged C++ files only
   - Uses existing `.clang-tidy` configuration
   - Requires build directory with `compile_commands.json`
   - Provides clear error messages and bypass options

### Supporting Scripts

3. **check-logging.sh** - Core logging compliance checker
   - Used by logging-precommit-hook.sh
   - Supports STAGED_FILES_LIST environment variable
   - Intelligent infrastructure exception handling

4. **build-check.sh** - Multi-Platform Build Verification
   - Comprehensive build testing (Debug/Release)
   - Executes clang-tidy analysis on changed files
   - Runs cppcheck with Qt6-specific settings

5. **check-result-pattern.py** - Result<T> Pattern Enforcement
   - Detects functions that should return Result<T>
   - Validates error handling patterns in engine code

## Usage

### Normal Development Workflow (Recommended)

```bash
# Hooks run automatically on commit - no manual intervention needed!
git add src/my_file.cpp
git commit -m "My changes"
# ✅ Hooks run in ~0.3-0.6 seconds

# If clang-tidy finds issues:
# 1. Fix the reported issues
# 2. Re-stage: git add src/my_file.cpp
# 3. Commit again: git commit -m "My changes"

# Emergency bypass (not recommended):
git commit --no-verify
```

### Prerequisites for Clang-Tidy Hook

```bash
# Build project first (required for clang-tidy analysis)
mkdir build && cd build
cmake .. && cmake --build .
```

### Manual Testing (Optional)

```bash
# Test individual hooks
./scripts/pre-commit/logging-precommit-hook.sh
./scripts/pre-commit/clang-tidy-precommit-hook.sh

# Test core scripts
./scripts/pre-commit/check-logging.sh
./scripts/pre-commit/build-check.sh

# Run all hooks manually
pre-commit run --all-files
```

### Pre-commit Framework Management

```bash
# Install pre-commit framework (if not already installed)
pip install pre-commit

# Install/update hooks
pre-commit install

# Update hook versions
pre-commit autoupdate
```

## Configuration

- `.clang-tidy` - Clang-tidy configuration with Vizion3D-specific rules
- `.pre-commit-config.yaml` - Pre-commit framework configuration

## Performance Characteristics

### Hook Performance (After Optimization)

| Hook | No Staged Files | With Staged Files | Scaling |
|------|----------------|-------------------|---------|
| Logging Compliance | ~0.3 seconds | ~0.6 seconds | Linear with staged files |
| Clang-Tidy Analysis | ~0.08 seconds | 10-30 seconds | Linear with staged files |
| **Total** | **~0.4 seconds** | **~0.6-30 seconds** | **Staged files only** |

### Key Performance Features

- ✅ **Staged files only**: Never scans entire codebase during commits
- ✅ **Fast exit**: Immediate exit when no relevant files staged
- ✅ **Efficient analysis**: Linear scaling with number of changed files
- ✅ **Smart caching**: Uses existing build artifacts when available

## Troubleshooting

### Common Issues

**Clang-Tidy Hook Fails**:
```bash
❌ No build directory with compile_commands.json found

# Solution: Build the project first
mkdir build && cd build
cmake .. && cmake --build .
```

**Slow Commit Performance**:
```bash
# Check if you're running old version of hooks
git log --oneline -5 | grep "performance"

# Update to latest version if needed
git pull origin main
pre-commit install --overwrite
```

**Permission Issues**:
```bash
chmod +x scripts/pre-commit/logging-precommit-hook.sh
chmod +x scripts/pre-commit/clang-tidy-precommit-hook.sh
```

### Debug Mode

```bash
# Run hooks with verbose output
pre-commit run --verbose

# Test individual hooks manually
./scripts/pre-commit/logging-precommit-hook.sh
./scripts/pre-commit/clang-tidy-precommit-hook.sh
```

## Valid VLOG Categories

- "GCode" - G-code processing and interpretation
- "Engine" - Core simulation engine operations
- "UI" - User interface components and interactions
- "OpenGL" - OpenGL rendering and graphics operations
- "FileIO" - File input/output operations
- "System" - System-level operations and initialization
- "Threading" - Multi-threading and concurrency
- "Validation" - Input validation and data verification

## Error Code Ranges

- 1000-1999: GCode processing errors
- 2000-2999: Engine operation errors
- 3000-3999: OpenGL rendering errors
- 4000-4999: File I/O errors
- 5000-5999: System errors
- 6000-6999: Threading errors
- 7000-7999: Validation errors
- 8000-8999: General system errors

## Integration with CI/CD

The pre-commit hooks complement the GitHub Actions CI/CD pipeline:

- **Pre-commit**: Fast feedback on staged files (~0.3-0.6 seconds)
- **GitHub Actions**: Comprehensive PR verification (5-15 minutes)
- **Both**: Maintain consistent code quality standards

See `.github/workflows/pr-build-verification.yml` for the complete CI/CD setup.
