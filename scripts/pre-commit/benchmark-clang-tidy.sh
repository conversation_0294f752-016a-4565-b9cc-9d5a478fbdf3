#!/bin/bash

# Benchmark Clang-Tidy Pre-commit Hook Performance
# This script measures the performance of the clang-tidy pre-commit hook
# and provides detailed timing analysis for optimization purposes.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo -e "${BLUE}=== Clang-Tidy Pre-commit Hook Performance Benchmark ===${NC}"
echo "Project: Vizion3D"
echo "Timestamp: $(date)"
echo ""

# Check prerequisites
echo -e "${YELLOW}Checking prerequisites...${NC}"

if ! command -v clang-tidy &> /dev/null; then
    echo -e "${RED}Error: clang-tidy not found${NC}"
    exit 1
fi

if ! command -v time &> /dev/null; then
    echo -e "${RED}Error: time command not found${NC}"
    exit 1
fi

cd "$PROJECT_ROOT"

# Check for build directory
BUILD_DIR=""
for build_dir in "build" "build-debug" "cmake-build-debug" "cmake-build-release"; do
    if [[ -d "$build_dir" && -f "$build_dir/compile_commands.json" ]]; then
        BUILD_DIR="$build_dir"
        break
    fi
done

if [[ -z "$BUILD_DIR" ]]; then
    echo -e "${RED}Error: No build directory with compile_commands.json found${NC}"
    exit 1
fi

echo -e "${GREEN}Prerequisites OK${NC}"
echo "Using build directory: $BUILD_DIR"
echo ""

# Get test files (use actual staged files or sample files)
TEST_FILES=$(git diff --cached --name-only --diff-filter=ACM 2>/dev/null | \
    grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' | \
    grep -v -E '(build/|.*_autogen/|ui_.*\.h$)' || true)

if [[ -z "$TEST_FILES" ]]; then
    # If no staged files, use sample files for testing
    echo -e "${YELLOW}No staged files found, using sample files for benchmarking${NC}"
    TEST_FILES=$(find src include -name "*.cpp" -o -name "*.h" | head -5)
fi

if [[ -z "$TEST_FILES" ]]; then
    echo -e "${RED}Error: No C++ files found for testing${NC}"
    exit 1
fi

echo "Test files:"
echo "$TEST_FILES" | while read -r file; do
    echo "  - $file"
done
echo ""

# Function to run clang-tidy with timing
benchmark_clang_tidy() {
    local file="$1"
    local config_name="$2"
    local extra_args="$3"

    echo -n "  Testing $(basename "$file") ($config_name)... "

    # Use time command for more reliable timing
    local time_output
    time_output=$( { time clang-tidy -p "$BUILD_DIR" $extra_args "$file" > /dev/null 2>&1; } 2>&1 )

    # Extract real time from time output (format: real 0m1.234s)
    local duration=$(echo "$time_output" | grep "real" | awk '{print $2}' | sed 's/[ms]//g' | awk -F: '{if(NF==2) print $1*60+$2; else print $1}')

    if [[ -z "$duration" ]]; then
        duration="0"
    fi

    printf "%.1fs\n" "$duration"
    echo "$duration"
}

# Function to benchmark configuration
benchmark_configuration() {
    local config_name="$1"
    local extra_args="$2"

    echo -e "${YELLOW}Benchmarking: $config_name${NC}"

    local total_time=0
    local file_count=0

    while IFS= read -r file; do
        if [[ -f "$file" ]]; then
            local file_time=$(benchmark_clang_tidy "$file" "$config_name" "$extra_args")
            # Use awk for floating point arithmetic instead of bc
            total_time=$(awk "BEGIN {print $total_time + $file_time}")
            ((file_count++))
        fi
    done <<< "$TEST_FILES"

    if [[ $file_count -gt 0 ]]; then
        local avg_time=$(awk "BEGIN {printf \"%.1f\", $total_time / $file_count}")
        printf "  Total time: %.1fs\n" "$total_time"
        printf "  Average per file: %ss\n" "$avg_time"
        printf "  Files analyzed: %d\n" "$file_count"
    fi

    echo ""
    echo "$total_time $file_count"
}

# Benchmark different configurations
echo -e "${BLUE}=== Performance Benchmarks ===${NC}"
echo ""

# 1. Current full configuration
echo "1. Current Enhanced Configuration (all checks)"
current_result=$(benchmark_configuration "Enhanced" "")
current_total=$(echo "$current_result" | cut -d' ' -f1)
current_count=$(echo "$current_result" | cut -d' ' -f2)

# 2. Minimal configuration (only critical checks)
echo "2. Minimal Configuration (critical checks only)"
minimal_checks="-checks=-*,bugprone-*,clang-analyzer-*,readability-identifier-naming"
minimal_result=$(benchmark_configuration "Minimal" "$minimal_checks")
minimal_total=$(echo "$minimal_result" | cut -d' ' -f1)

# 3. Modernize only
echo "3. Modernize Checks Only"
modernize_checks="-checks=-*,modernize-*"
modernize_result=$(benchmark_configuration "Modernize" "$modernize_checks")
modernize_total=$(echo "$modernize_result" | cut -d' ' -f1)

# 4. Core Guidelines only
echo "4. Core Guidelines Only"
guidelines_checks="-checks=-*,cppcoreguidelines-*"
guidelines_result=$(benchmark_configuration "Guidelines" "$guidelines_checks")
guidelines_total=$(echo "$guidelines_result" | cut -d' ' -f1)

# 5. Header filter optimization test
echo "5. With Header Filter Optimization"
header_filter="--header-filter='^(?!.*/build/).*$'"
header_result=$(benchmark_configuration "HeaderFilter" "$header_filter")
header_total=$(echo "$header_result" | cut -d' ' -f1)

# Performance analysis
echo -e "${BLUE}=== Performance Analysis ===${NC}"
echo ""

if [[ $current_count -gt 0 ]]; then
    local avg_current=$(awk "BEGIN {printf \"%.1f\", $current_total / $current_count}")
    printf "Current configuration: %.1fs total, %ss per file\n" "$current_total" "$avg_current"

    # Simple integer comparison for recommendations
    local current_int=$(awk "BEGIN {printf \"%.0f\", $current_total}")

    if [[ $current_int -gt 30 ]]; then
        echo -e "${RED}SLOW: Current configuration takes >30s${NC}"
        echo "Recommendations:"
        echo "  1. Consider reducing check scope"
        echo "  2. Implement parallel processing"
        echo "  3. Add header filtering"
        echo "  4. Cache compilation database"
    elif [[ $current_int -gt 15 ]]; then
        echo -e "${YELLOW}MODERATE: Current configuration takes >15s${NC}"
        echo "Recommendations:"
        echo "  1. Consider parallel processing"
        echo "  2. Optimize header filtering"
    else
        echo -e "${GREEN}GOOD: Current configuration is reasonably fast${NC}"
        echo "Current performance is acceptable for pre-commit hooks"
    fi
else
    echo "No performance data available"
fi

echo ""
echo "Benchmark completed at $(date)"
