#!/bin/bash
# scripts/pre-commit/fix-logging.sh
# Vizion3D Automated Logging Pattern Fix Tool
#
# This script automatically fixes simple logging pattern violations,
# focusing on safe, single-line transformations while preserving
# legitimate infrastructure exceptions.

# Note: Not using 'set -e' to avoid early exit when functions return fix counts

# Configuration flags
DRY_RUN=true
CREATE_BACKUP=false
VERIFY_COMPILATION=false
SIMPLE_ONLY=true
SHOW_HELP=false
TARGET_FILES=""
OVERRIDE_CATEGORY=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --apply)
            DRY_RUN=false
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --backup)
            CREATE_BACKUP=true
            shift
            ;;
        --verify)
            VERIFY_COMPILATION=true
            shift
            ;;
        --simple-only)
            SIMPLE_ONLY=true
            shift
            ;;
        --files)
            TARGET_FILES="$2"
            shift 2
            ;;
        --category)
            OVERRIDE_CATEGORY="$2"
            shift 2
            ;;
        --help|-h)
            SHOW_HELP=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

if [[ "$SHOW_HELP" == true ]]; then
    cat << 'EOF'
Vizion3D Automated Logging Pattern Fix Tool

USAGE:
    ./scripts/pre-commit/fix-logging.sh [OPTIONS]

OPTIONS:
    --apply         Apply fixes (default is dry-run mode)
    --dry-run       Show what would be changed without modifying files (default)
    --backup        Create .bak backup files before modification
    --verify        Attempt compilation after applying fixes
    --simple-only   Only fix simple single-line cases (default)
    --files PATTERN Only process files matching pattern
    --category CAT  Override automatic category assignment
    --help, -h      Show this help message

DESCRIPTION:
    Phase 1 implementation focusing on safe, simple transformations:

    SUPPORTED TRANSFORMATIONS:
    - qDebug() → VLOG_DEBUG("Category", "message")
    - qWarning() → VLOG_WARNING("Category", "message")
    - qCritical() → VLOG_ERROR("Category", "message")
    - std::cout → VLOG_INFO("Category", "message")
    - std::cerr → VLOG_ERROR("Category", "message")
    - printf() → VLOG_INFO("Category", "message") (application code only)

    CATEGORY ASSIGNMENT:
    - src/ui/ → "UI"
    - src/engine/ → "Engine"
    - src/utils/ → "System"
    - *opengl*|*render*|*gl* → "OpenGL"
    - *gcode* → "GCode"
    - Default → "System"

    SAFETY FEATURES:
    - Preserves legitimate infrastructure exceptions
    - Only processes .cpp files (avoids headers)
    - Creates backups when requested
    - Verifies compilation when requested
    - Conservative pattern matching

EXAMPLES:
    # Dry run (safe preview)
    ./scripts/pre-commit/fix-logging.sh

    # Apply fixes with backup
    ./scripts/pre-commit/fix-logging.sh --apply --backup

    # Fix specific files only
    ./scripts/pre-commit/fix-logging.sh --apply --files "src/ui/*.cpp"

    # Apply and verify compilation
    ./scripts/pre-commit/fix-logging.sh --apply --verify

EOF
    exit 0
fi

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Define logging infrastructure files (same as in check-logging.sh)
declare -a INFRASTRUCTURE_FILES=(
    "src/utils/logger.cpp"
    "src/utils/log_config.cpp"
)

# Function to check if a file is logging infrastructure
is_infrastructure_file() {
    local file="$1"
    local relative_path="${file#$PROJECT_ROOT/}"

    for infra_file in "${INFRASTRUCTURE_FILES[@]}"; do
        if [[ "$relative_path" == "$infra_file" ]]; then
            return 0
        fi
    done
    return 1
}

echo "🔧 Vizion3D Automated Logging Fix Tool"
if [[ "$DRY_RUN" == true ]]; then
    echo "   Running in DRY-RUN mode (use --apply to make changes)"
else
    echo "   Running in APPLY mode - files will be modified"
fi
echo ""

# Statistics counters
TOTAL_FILES_PROCESSED=0
TOTAL_FIXES_APPLIED=0
FILES_WITH_FIXES=0
BACKUP_FILES_CREATED=0

# Function to determine category based on file path
determine_category() {
    local file="$1"
    local relative_path="${file#$PROJECT_ROOT/}"

    # Override category if specified
    if [[ -n "$OVERRIDE_CATEGORY" ]]; then
        echo "$OVERRIDE_CATEGORY"
        return
    fi

    # Path-based category assignment
    case "$relative_path" in
        src/ui/*)
            echo "UI"
            ;;
        src/engine/*)
            echo "Engine"
            ;;
        src/utils/*)
            echo "System"
            ;;
        *opengl*|*render*|*gl*)
            echo "OpenGL"
            ;;
        *gcode*)
            echo "GCode"
            ;;
        *)
            echo "System"
            ;;
    esac
}

# Function to check if file needs result.h include
needs_result_include() {
    local file="$1"

    # Check if file already includes result.h or logger.h
    if grep -q '#include "utils/result.h"' "$file" 2>/dev/null || \
       grep -q '#include "utils/logger.h"' "$file" 2>/dev/null; then
        return 1  # Already has include
    fi

    # Check if file uses VLOG_* macros (will need include after our fixes)
    if grep -q "VLOG_" "$file" 2>/dev/null; then
        return 0  # Needs include
    fi

    return 1  # Doesn't need include
}

# Function to add result.h include to file
add_result_include() {
    local file="$1"
    local temp_file="${file}.tmp"

    # Find the last #include line and insert after it
    awk '
        /^#include/ { last_include = NR }
        { lines[NR] = $0 }
        END {
            for (i = 1; i <= NR; i++) {
                print lines[i]
                if (i == last_include) {
                    print "#include \"utils/result.h\""
                }
            }
        }
    ' "$file" > "$temp_file"

    if [[ "$DRY_RUN" == false ]]; then
        mv "$temp_file" "$file"
        echo "    ✅ Added #include \"utils/result.h\""
    else
        rm "$temp_file"
        echo "    📝 Would add #include \"utils/result.h\""
    fi
}

# Function to create backup file
create_backup() {
    local file="$1"
    local backup_file="${file}.bak"

    if [[ "$CREATE_BACKUP" == true ]] && [[ "$DRY_RUN" == false ]]; then
        cp "$file" "$backup_file"
        BACKUP_FILES_CREATED=$((BACKUP_FILES_CREATED + 1))
        echo "    💾 Created backup: ${backup_file##*/}"
    fi
}

# Function to fix Qt logging patterns
fix_qt_logging() {
    local file="$1"
    local category="$2"
    local temp_file="${file}.tmp"
    local fixes_made=0

    # Copy original file to temp
    cp "$file" "$temp_file"

    # Fix qDebug() patterns
    if grep -q "qDebug()" "$temp_file" 2>/dev/null; then
        local original_count=$(grep -c "qDebug()" "$temp_file" 2>/dev/null | head -1 || echo 0)

        # Enhanced qDebug() pattern fixing using dedicated Python script for complex cases
        local python_fixes=$(python3 "$SCRIPT_DIR/fix-complex-logging.py" "$temp_file" "$category" 2>/dev/null || echo 0)

        if [[ $python_fixes -gt 0 ]]; then
            fixes_made=$((fixes_made + python_fixes))
            echo "    🔧 Fixed $python_fixes complex Qt logging calls"
        fi

        # Fallback to simple sed patterns for any remaining cases
        sed -i.bak 's/qDebug() << "\([^"]*\)";/VLOG_DEBUG("'$category'", "\1");/g' "$temp_file"
        sed -i.bak 's/qDebug() << \([a-zA-Z_][a-zA-Z0-9_]*\);/VLOG_DEBUG("'$category'", QString::number(\1));/g' "$temp_file"
        rm -f "${temp_file}.bak"

        local remaining_count=$(grep -c "qDebug()" "$temp_file" 2>/dev/null | head -1 || echo 0)
        if [[ $remaining_count -lt $original_count ]]; then
            local additional_fixes=$((original_count - remaining_count - python_fixes))
            if [[ $additional_fixes -gt 0 ]]; then
                fixes_made=$((fixes_made + additional_fixes))
                echo "    🔧 Fixed $additional_fixes additional qDebug() calls"
            fi
        fi
    fi

    # Fix qWarning() patterns
    if grep -q "qWarning()" "$temp_file" 2>/dev/null; then
        local original_count=$(grep -c "qWarning()" "$temp_file" 2>/dev/null || echo 0)

        sed -i.bak 's/qWarning() << "\([^"]*\)";/VLOG_WARNING("'$category'", "\1");/g' "$temp_file"
        sed -i.bak 's/qWarning() << \([a-zA-Z_][a-zA-Z0-9_]*\);/VLOG_WARNING("'$category'", QString::number(\1));/g' "$temp_file"
        rm -f "${temp_file}.bak"

        local remaining_count=$(grep -c "qWarning()" "$temp_file" 2>/dev/null || echo 0)
        if [[ $remaining_count -lt $original_count ]]; then
            local fixed_count=$((original_count - remaining_count))
            fixes_made=$((fixes_made + fixed_count))
            echo "    🔧 Fixed $fixed_count qWarning() calls"
        fi
    fi

    # Fix qCritical() patterns
    if grep -q "qCritical()" "$temp_file" 2>/dev/null; then
        local original_count=$(grep -c "qCritical()" "$temp_file" 2>/dev/null || echo 0)

        sed -i.bak 's/qCritical() << "\([^"]*\)";/VLOG_ERROR("'$category'", "\1");/g' "$temp_file"
        sed -i.bak 's/qCritical() << \([a-zA-Z_][a-zA-Z0-9_]*\);/VLOG_ERROR("'$category'", QString::number(\1));/g' "$temp_file"
        rm -f "${temp_file}.bak"

        local remaining_count=$(grep -c "qCritical()" "$temp_file" 2>/dev/null || echo 0)
        if [[ $remaining_count -lt $original_count ]]; then
            local fixed_count=$((original_count - remaining_count))
            fixes_made=$((fixes_made + fixed_count))
            echo "    🔧 Fixed $fixed_count qCritical() calls"
        fi
    fi

    # Apply changes or show what would be changed
    if [[ $fixes_made -gt 0 ]]; then
        if [[ "$DRY_RUN" == false ]]; then
            mv "$temp_file" "$file"
        else
            echo "    📝 Would apply $fixes_made Qt logging fixes"
            rm "$temp_file"
        fi
        return $fixes_made
    else
        rm "$temp_file"
        return 0
    fi
}

# Function to fix stream output patterns
fix_stream_output() {
    local file="$1"
    local category="$2"
    local temp_file="${file}.tmp"
    local fixes_made=0

    # Copy original file to temp
    cp "$file" "$temp_file"

    # Fix std::cout patterns
    if grep -q "std::cout" "$temp_file" 2>/dev/null; then
        # Simple std::cout << "message";
        sed -i '' 's/std::cout << "\([^"]*\)";/VLOG_INFO("'$category'", "\1");/g' "$temp_file" 2>/dev/null || \
        sed -i 's/std::cout << "\([^"]*\)";/VLOG_INFO("'$category'", "\1");/g' "$temp_file"
        # std::cout << variable;
        sed -i '' 's/std::cout << \([a-zA-Z_][a-zA-Z0-9_]*\);/VLOG_INFO("'$category'", QString::number(\1));/g' "$temp_file" 2>/dev/null || \
        sed -i 's/std::cout << \([a-zA-Z_][a-zA-Z0-9_]*\);/VLOG_INFO("'$category'", QString::number(\1));/g' "$temp_file"

        local remaining_cout=$(grep -c "std::cout" "$temp_file" 2>/dev/null || echo 0)
        local original_cout=$(grep -c "std::cout" "$file" 2>/dev/null || echo 0)
        if [[ $remaining_cout -lt $original_cout ]]; then
            local fixed_count=$((original_cout - remaining_cout))
            fixes_made=$((fixes_made + fixed_count))
            echo "    🔧 Fixed $fixed_count std::cout calls"
        fi
    fi

    # Fix std::cerr patterns
    if grep -q "std::cerr" "$temp_file" 2>/dev/null; then
        sed -i '' 's/std::cerr << "\([^"]*\)";/VLOG_ERROR("'$category'", "\1");/g' "$temp_file" 2>/dev/null || \
        sed -i 's/std::cerr << "\([^"]*\)";/VLOG_ERROR("'$category'", "\1");/g' "$temp_file"
        sed -i '' 's/std::cerr << \([a-zA-Z_][a-zA-Z0-9_]*\);/VLOG_ERROR("'$category'", QString::number(\1));/g' "$temp_file" 2>/dev/null || \
        sed -i 's/std::cerr << \([a-zA-Z_][a-zA-Z0-9_]*\);/VLOG_ERROR("'$category'", QString::number(\1));/g' "$temp_file"

        local remaining_cerr=$(grep -c "std::cerr" "$temp_file" 2>/dev/null || echo 0)
        local original_cerr=$(grep -c "std::cerr" "$file" 2>/dev/null || echo 0)
        if [[ $remaining_cerr -lt $original_cerr ]]; then
            local fixed_count=$((original_cerr - remaining_cerr))
            fixes_made=$((fixes_made + fixed_count))
            echo "    🔧 Fixed $fixed_count std::cerr calls"
        fi
    fi

    # Apply changes or show what would be changed
    if [[ $fixes_made -gt 0 ]]; then
        if [[ "$DRY_RUN" == false ]]; then
            mv "$temp_file" "$file"
        else
            echo "    📝 Would apply $fixes_made stream output fixes"
            rm "$temp_file"
        fi
        return $fixes_made
    else
        rm "$temp_file"
        return 0
    fi
}

# Function to fix printf patterns (only in application code, not infrastructure)
fix_printf_patterns() {
    local file="$1"
    local category="$2"
    local temp_file="${file}.tmp"
    local fixes_made=0

    # Skip infrastructure files (they have legitimate printf usage)
    if is_infrastructure_file "$file"; then
        return 0
    fi

    # Copy original file to temp
    cp "$file" "$temp_file"

    # Fix simple printf patterns
    if grep -q 'printf(' "$temp_file" 2>/dev/null; then
        # Simple printf("message\n"); -> VLOG_INFO("Category", "message");
        sed -i '' 's/printf("\([^"]*\)\\n");/VLOG_INFO("'$category'", "\1");/g' "$temp_file" 2>/dev/null || \
        sed -i 's/printf("\([^"]*\)\\n");/VLOG_INFO("'$category'", "\1");/g' "$temp_file"
        # printf("message"); -> VLOG_INFO("Category", "message");
        sed -i '' 's/printf("\([^"]*\)");/VLOG_INFO("'$category'", "\1");/g' "$temp_file" 2>/dev/null || \
        sed -i 's/printf("\([^"]*\)");/VLOG_INFO("'$category'", "\1");/g' "$temp_file"

        local remaining_printf=$(grep -c 'printf(' "$temp_file" 2>/dev/null || echo 0)
        local original_printf=$(grep -c 'printf(' "$file" 2>/dev/null || echo 0)
        if [[ $remaining_printf -lt $original_printf ]]; then
            local fixed_count=$((original_printf - remaining_printf))
            fixes_made=$((fixes_made + fixed_count))
            echo "    🔧 Fixed $fixed_count printf() calls"
        fi
    fi

    # Apply changes or show what would be changed
    if [[ $fixes_made -gt 0 ]]; then
        if [[ "$DRY_RUN" == false ]]; then
            mv "$temp_file" "$file"
        else
            echo "    📝 Would apply $fixes_made printf fixes"
            rm "$temp_file"
        fi
        return $fixes_made
    else
        rm "$temp_file"
        return 0
    fi
}

# Function to process a single file
process_file() {
    local file="$1"
    local relative_path="${file#$PROJECT_ROOT/}"

    echo "📁 Processing: $relative_path"

    # Determine category for this file
    local category=$(determine_category "$file")
    echo "    📂 Category: $category"

    # Create backup if requested
    create_backup "$file"

    # Track fixes for this file
    local file_fixes=0

    # Apply Qt logging fixes
    local qt_fixes=0
    fix_qt_logging "$file" "$category"
    qt_fixes=$?
    file_fixes=$((file_fixes + qt_fixes))

    # Apply stream output fixes
    local stream_fixes=0
    fix_stream_output "$file" "$category"
    stream_fixes=$?
    file_fixes=$((file_fixes + stream_fixes))

    # Apply printf fixes (only for non-infrastructure files)
    local printf_fixes=0
    fix_printf_patterns "$file" "$category"
    printf_fixes=$?
    file_fixes=$((file_fixes + printf_fixes))

    # Add include if needed and fixes were made
    if [[ $file_fixes -gt 0 ]]; then
        # Check if we need to add the include (after potential fixes)
        if [[ "$DRY_RUN" == false ]]; then
            if needs_result_include "$file"; then
                add_result_include "$file"
            fi
        else
            # In dry-run mode, check if include would be needed
            if ! grep -q '#include "utils/result.h"' "$file" 2>/dev/null && \
               ! grep -q '#include "utils/logger.h"' "$file" 2>/dev/null; then
                echo "    📝 Would add #include \"utils/result.h\""
            fi
        fi

        FILES_WITH_FIXES=$((FILES_WITH_FIXES + 1))
        TOTAL_FIXES_APPLIED=$((TOTAL_FIXES_APPLIED + file_fixes))
        echo "    ✅ Total fixes in this file: $file_fixes"
    else
        echo "    ℹ️  No simple violations found to fix"
    fi

    TOTAL_FILES_PROCESSED=$((TOTAL_FILES_PROCESSED + 1))
    echo ""
}

# Function to get files to process
get_files_to_process() {
    if [[ -n "$TARGET_FILES" ]]; then
        # Use specified file pattern
        if [[ "$TARGET_FILES" == *"*"* ]]; then
            # Pattern with wildcards
            find $PROJECT_ROOT -path "$TARGET_FILES" 2>/dev/null || true
        else
            # Specific file
            if [[ -f "$PROJECT_ROOT/$TARGET_FILES" ]]; then
                echo "$PROJECT_ROOT/$TARGET_FILES"
            elif [[ -f "$TARGET_FILES" ]]; then
                echo "$TARGET_FILES"
            fi
        fi
    else
        # Get all .cpp files, excluding infrastructure if in simple mode
        if git rev-parse --git-dir > /dev/null 2>&1; then
            # In git repo, check staged files first
            STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM 2>/dev/null | grep '\.cpp$' || true)
            if [ -n "$STAGED_FILES" ]; then
                echo "$STAGED_FILES" | while read -r file; do
                    echo "$PROJECT_ROOT/$file"
                done
            else
                # No staged files, process all .cpp files
                find "$PROJECT_ROOT/src" -name "*.cpp" 2>/dev/null || true
            fi
        else
            # Not in git, process all .cpp files
            find "$PROJECT_ROOT/src" -name "*.cpp" 2>/dev/null || true
        fi
    fi
}

# Function to verify compilation
verify_compilation() {
    echo "🔨 Verifying compilation..."

    cd "$PROJECT_ROOT"

    # Try to build the project
    if command -v qmake >/dev/null 2>&1; then
        echo "    Using qmake build system..."
        if qmake && make -j$(nproc) >/dev/null 2>&1; then
            echo "    ✅ Compilation successful"
            return 0
        else
            echo "    ❌ Compilation failed"
            return 1
        fi
    elif [ -f "CMakeLists.txt" ]; then
        echo "    Using CMake build system..."
        if cmake . && make -j$(nproc) >/dev/null 2>&1; then
            echo "    ✅ Compilation successful"
            return 0
        else
            echo "    ❌ Compilation failed"
            return 1
        fi
    else
        echo "    ⚠️  No recognized build system found, skipping compilation verification"
        return 0
    fi
}

# Main execution
echo "🔍 Scanning for files to process..."

FILES_TO_PROCESS=$(get_files_to_process)

if [ -z "$FILES_TO_PROCESS" ]; then
    echo "❌ No .cpp files found to process"
    exit 1
fi

FILE_COUNT=$(echo "$FILES_TO_PROCESS" | wc -l)
echo "📊 Found $FILE_COUNT .cpp files to process"
echo ""

# Process each file
while read -r file; do
    if [ -f "$file" ]; then
        process_file "$file"
    fi
done <<< "$FILES_TO_PROCESS"

# Final reporting
echo "📋 PROCESSING SUMMARY:"
echo "   Files processed: $TOTAL_FILES_PROCESSED"
echo "   Files with fixes: $FILES_WITH_FIXES"
echo "   Total fixes applied: $TOTAL_FIXES_APPLIED"

if [[ "$CREATE_BACKUP" == true ]] && [[ $BACKUP_FILES_CREATED -gt 0 ]]; then
    echo "   Backup files created: $BACKUP_FILES_CREATED"
fi

if [[ "$DRY_RUN" == true ]]; then
    echo ""
    echo "🔍 DRY-RUN COMPLETE"
    echo "   No files were modified (use --apply to make changes)"
    echo "   Run with --apply to apply these fixes"
else
    echo ""
    echo "✅ FIXES APPLIED"

    # Verify compilation if requested
    if [[ "$VERIFY_COMPILATION" == true ]] && [[ $TOTAL_FIXES_APPLIED -gt 0 ]]; then
        echo ""
        if verify_compilation; then
            echo "✅ All fixes applied successfully and code compiles"
        else
            echo "❌ Compilation failed after applying fixes"
            echo "   Please review the changes and fix any compilation errors"
            exit 1
        fi
    fi

    # Suggest running the check script
    if [[ $TOTAL_FIXES_APPLIED -gt 0 ]]; then
        echo ""
        echo "💡 Next steps:"
        echo "   1. Review the applied changes"
        echo "   2. Run ./scripts/pre-commit/check-logging.sh to verify violation reduction"
        echo "   3. Test your application to ensure functionality is preserved"
        echo "   4. Commit the changes"
    fi
fi

echo ""
if [[ $TOTAL_FIXES_APPLIED -gt 0 ]]; then
    echo "🎉 Successfully processed $TOTAL_FIXES_APPLIED logging violations!"
else
    echo "ℹ️  No simple violations found to fix automatically"
    echo "   Complex cases may require manual review and fixing"
fi

# Exit with success code
exit 0
