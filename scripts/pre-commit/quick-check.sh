#!/bin/bash
# scripts/pre-commit/quick-check.sh
# Fast pre-commit quality checks for Vizion3D
#
# This script runs only the fastest quality checks to maintain development velocity
# while catching the most common violations early in the development cycle.

set -e

echo "🚀 Running quick quality checks..."

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Track overall success
OVERALL_SUCCESS=0

# Function to run a check and track results
run_check() {
    local check_name="$1"
    local check_script="$2"

    echo ""
    echo "📋 Running $check_name..."

    if "$check_script"; then
        echo "✅ $check_name passed"
    else
        echo "❌ $check_name failed"
        OVERALL_SUCCESS=1
    fi
}

# Get list of staged files for targeted checking
STAGED_FILES=""
if git rev-parse --git-dir > /dev/null 2>&1; then
    STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM 2>/dev/null | grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' || true)
fi

if [ -z "$STAGED_FILES" ]; then
    echo "ℹ️  No C++ files staged for commit, skipping quality checks"
    exit 0
fi

echo "📁 Checking $(echo "$STAGED_FILES" | wc -l) staged C++ files"

# Run fast checks only
run_check "Logging Pattern Compliance" "$SCRIPT_DIR/check-logging.sh"

# Summary
echo ""
echo "📊 Quick Check Summary:"
if [ $OVERALL_SUCCESS -eq 0 ]; then
    echo "✅ All quick checks passed!"
    echo ""
    echo "💡 Note: Full quality checks (Result<T> patterns, builds) will run on push/PR"
    exit 0
else
    echo "❌ Some quick checks failed"
    echo ""
    echo "🔧 Fix the issues above before committing, or use:"
    echo "   git commit --no-verify  # to bypass checks (not recommended)"
    echo ""
    echo "📖 For help fixing violations:"
    echo "   - Logging: docs/features/logging_system.md"
    echo "   - Patterns: docs/implementation/direct_error_handling_implementation.md"
    exit 1
fi
