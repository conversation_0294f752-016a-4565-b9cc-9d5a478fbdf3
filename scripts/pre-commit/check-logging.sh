#!/bin/bash
# scripts/pre-commit/check-logging.sh
# Vizion3D Intelligent Logging Pattern Compliance Checker
#
# This script intelligently distinguishes between legitimate and illegitimate
# logging pattern violations, allowing infrastructure exceptions while blocking
# application code violations.

set -e

# Configuration flags
STRICT_MODE=false
EXPLAIN_MODE=false
SHOW_HELP=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --strict)
            STRICT_MODE=true
            shift
            ;;
        --explain)
            EXPLAIN_MODE=true
            shift
            ;;
        --help|-h)
            SHOW_HELP=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

if [[ "$SHOW_HELP" == true ]]; then
    cat << 'EOF'
Vizion3D Intelligent Logging Pattern Compliance Checker

USAGE:
    ./scripts/pre-commit/check-logging.sh [OPTIONS]

OPTIONS:
    --strict    Flag ALL violations including legitimate infrastructure exceptions
    --explain   Show detailed reasoning for each decision
    --help, -h  Show this help message

DESCRIPTION:
    This script intelligently analyzes logging patterns to distinguish between:

    LEGITIMATE EXCEPTIONS (normally allowed):
    - fprintf() in logging infrastructure exception handlers
    - fprintf() during logger initialization (when !m_initialized)
    - fprintf() in LogConfig::applyConfiguration() to avoid circular dependencies
    - fflush() calls in logging infrastructure

    ILLEGITIMATE VIOLATIONS (always blocked):
    - qDebug()/qWarning()/qCritical() in application code
    - std::cout/std::cerr anywhere in codebase
    - fprintf() in non-infrastructure files
    - printf() calls anywhere

EXIT CODES:
    0 - Success (only legitimate exceptions found)
    1 - Failure (illegitimate violations found)
    2 - Warning (strict mode with legitimate exceptions)

EOF
    exit 0
fi

echo "🔍 Checking logging pattern compliance..."
if [[ "$STRICT_MODE" == true ]]; then
    echo "   Running in STRICT mode - flagging ALL violations"
fi
if [[ "$EXPLAIN_MODE" == true ]]; then
    echo "   Running in EXPLAIN mode - showing detailed reasoning"
fi

# Define patterns to check
declare -a QT_PATTERNS=("qDebug()" "qWarning()" "qCritical()" "qInfo()" "qFatal()")
declare -a STREAM_PATTERNS=("std::cout" "std::cerr" "cout <<" "cerr <<")
declare -a PRINTF_PATTERNS=("printf(" "fprintf(stdout" "fprintf(stderr" "fflush(")

# Define valid VLOG categories
declare -a VALID_CATEGORIES=(
    "GCode" "Engine" "UI" "OpenGL" "FileIO" "System" "Threading" "Validation"
)

# Define logging infrastructure files (legitimate fprintf usage)
# NOTE: main.cpp is NOT included - it uses precise context analysis instead
declare -a INFRASTRUCTURE_FILES=(
    "src/utils/logger.cpp"
    "src/utils/log_config.cpp"
)

# Violation counters
BLOCKED_VIOLATIONS=0
ALLOWED_VIOLATIONS=0
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Function to check files (either staged files or all files)
get_files_to_check() {
    # If STAGED_FILES_LIST environment variable is set, use those files
    if [[ -n "$STAGED_FILES_LIST" && -f "$STAGED_FILES_LIST" ]]; then
        # Read staged files from the provided list
        cat "$STAGED_FILES_LIST" | grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' || true
        return
    fi

    if git rev-parse --git-dir > /dev/null 2>&1; then
        # We're in a git repository, check staged files
        STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM 2>/dev/null | grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' || true)
        if [ -n "$STAGED_FILES" ]; then
            echo "$STAGED_FILES"
        else
            # No staged files, exit early to avoid checking entire codebase
            echo ""
        fi
    else
        # Not in git, check all source files
        find "$PROJECT_ROOT/src" "$PROJECT_ROOT/include" -name "*.cpp" -o -name "*.cxx" -o -name "*.cc" -o -name "*.h" -o -name "*.hpp" 2>/dev/null || true
    fi
}

# Function to check if a file is logging infrastructure
is_infrastructure_file() {
    local file="$1"
    local relative_path="${file#$PROJECT_ROOT/}"

    for infra_file in "${INFRASTRUCTURE_FILES[@]}"; do
        if [[ "$relative_path" == "$infra_file" ]]; then
            return 0
        fi
    done
    return 1
}

# Function to analyze context around a violation with STRICT enforcement
analyze_context() {
    local file="$1"
    local line_num="$2"
    local pattern="$3"

    # Get precise context lines around the violation (only ±5 lines for better analysis)
    local start_line=$((line_num - 5))
    local end_line=$((line_num + 5))
    [[ $start_line -lt 1 ]] && start_line=1

    local context=$(sed -n "${start_line},${end_line}p" "$file" 2>/dev/null || echo "")
    local current_line=$(sed -n "${line_num}p" "$file" 2>/dev/null || echo "")

    # STRICT: Check for direct exception handler context (catch block)
    if echo "$context" | grep -q "catch\s*(" || echo "$context" | grep -q "} catch"; then
        echo "exception_handler"
        return
    fi

    # STRICT: Check for test mode context
    if echo "$context" | grep -q "#ifdef QT_TESTLIB_LIB" || echo "$context" | grep -q "#ifndef QT_TESTLIB_LIB"; then
        echo "test_mode"
        return
    fi

    # STRICT: Check for constructor context (high recursion risk)
    # Only detect actual constructors, not regular member functions
    if echo "$context" | grep -q "LogConfig::LogConfig\|Logger::Logger" || echo "$context" | grep -q "^\s*LogConfig()\|^\s*Logger()"; then
        echo "constructor_initialization"
        return
    fi

    # STRICT: For main.cpp only, check for VERY specific logging initialization messages
    local relative_path="${file#$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)/}"
    if [[ "$relative_path" == "src/main.cpp" ]]; then
        # Only allow fprintf with these EXACT initialization messages
        if echo "$current_line" | grep -q "fprintf.*Initializing LogConfig" || \
           echo "$current_line" | grep -q "fprintf.*LogConfig initialized" || \
           echo "$current_line" | grep -q "fprintf.*Setting category log levels" || \
           echo "$current_line" | grep -q "fprintf.*Category log levels set" || \
           echo "$current_line" | grep -q "fprintf.*Installing Qt message handler" || \
           echo "$current_line" | grep -q "fprintf.*Qt message handler installed"; then
            echo "critical_initialization"
            return
        fi

        # Allow fflush in main.cpp if it's in the logging initialization section
        if [[ "$pattern" == "fflush(" ]]; then
            # Check if we're in the logging initialization try-catch block
            if echo "$context" | grep -qi "logging\|logconfig\|message.*handler\|vlog_info.*system"; then
                echo "critical_flush"
                return
            fi

            # Allow if it immediately follows an allowed fprintf
            local prev_line=$(sed -n "$((line_num - 1))p" "$file" 2>/dev/null || echo "")
            if echo "$prev_line" | grep -q "fprintf.*LogConfig\|fprintf.*message handler\|fprintf.*log levels"; then
                echo "critical_flush"
                return
            fi

            # Allow if it immediately follows a VLOG_* call (for proper output flushing)
            if echo "$prev_line" | grep -q "VLOG_"; then
                echo "critical_flush"
                return
            fi
        fi
    fi

    # STRICT: For infrastructure files, check for specific patterns
    if is_infrastructure_file "$file"; then
        # Check for initialization context (!m_initialized or before m_initialized = true)
        if echo "$context" | grep -q "!m_initialized" || echo "$context" | grep -q "m_initialized.*false"; then
            echo "infrastructure_initialization"
            return
        fi

        # Enhanced: Check for functions that occur before m_initialized = true
        # Look for initializeWithoutMessageHandler context
        if echo "$context" | grep -q "initializeWithoutMessageHandler\|Starting logger initialization"; then
            # Check if we're before the line that sets m_initialized = true
            local function_start=$(grep -n "initializeWithoutMessageHandler" "$file" | head -1 | cut -d: -f1)
            local initialized_line=$(sed -n "${function_start},\$p" "$file" | grep -n "m_initialized = true" | head -1 | cut -d: -f1)
            if [[ -n "$function_start" ]] && [[ -n "$initialized_line" ]]; then
                local actual_initialized_line=$((function_start + initialized_line - 1))
                if [[ $line_num -lt $actual_initialized_line ]]; then
                    echo "infrastructure_initialization"
                    return
                fi
            fi
        fi

        # Enhanced: Better detection of !m_initialized checks
        # Look for explicit checks like "if (!m_initialized)" or "Logger not initialized"
        if echo "$current_line" | grep -q "Logger not initialized\|!m_initialized" || echo "$context" | grep -q "if.*!m_initialized"; then
            echo "infrastructure_initialization"
            return
        fi

        # CRITICAL: Allow fprintf() during Logger initialization to prevent recursive calls
        # These specific patterns are required to avoid circular dependency during Logger::initializeWithoutMessageHandler
        if echo "$context" | grep -q "initializeWithoutMessageHandler\|LogConfig.*LogConfig()"; then
            if echo "$current_line" | grep -q "Logger already initialized\|Setting up console sink\|Console sink added\|Basic logger initialization\|File sink added\|LogConfig initialized\|Logging system initialized\|LogConfig constructor"; then
                echo "infrastructure_critical_initialization"
                return
            fi
        fi

        # ENHANCED: More specific patterns for critical Logger initialization messages
        # Match specific fprintf() patterns that are essential for Logger initialization
        if echo "$current_line" | grep -q "Logger already initialized, returning early\|Setting up console sink\|Console sink added\|Basic logger initialization complete\|File sink added, loading configuration\|LogConfig initialized\|Logging system initialized"; then
            # Verify we're in a Logger infrastructure file
            if [[ "$file" == *"src/utils/logger.cpp"* ]] || [[ "$file" == *"src/utils/log_config.cpp"* ]]; then
                echo "infrastructure_critical_initialization"
                return
            fi
        fi

        # Check for circular dependency prevention context
        if echo "$context" | grep -q "circular\|recursion\|recursive" || echo "$context" | grep -q "isApplyingConfiguration"; then
            echo "circular_dependency"
            return
        fi

        # ENHANCED: Allow fprintf() in configuration functions (legitimate infrastructure operations)
        # These functions handle logging system configuration and state tracking
        if echo "$context" | grep -q "setFileLoggingEnabled\|loadConfiguration\|updateSinks"; then
            echo "infrastructure_configuration"
            return
        fi

        # ENHANCED: Allow fprintf() for error reporting in infrastructure files
        # These are critical error messages when logging system components fail
        if echo "$current_line" | grep -q "Failed to create\|Failed to open"; then
            echo "infrastructure_error_reporting"
            return
        fi

        # ENHANCED: Allow fprintf() for configuration change tracking in LogConfig
        # These debug messages track configuration state changes for troubleshooting
        if echo "$current_line" | grep -q "LogConfig.*called with\|LogConfig.*Changing\|LogConfig.*enabled=\|LogConfig.*Applying\|LogConfig.*No changes"; then
            echo "infrastructure_configuration_tracking"
            return
        fi

        # ENHANCED: Allow fprintf() for category and sink management in applyConfiguration
        # These messages are part of the configuration application process
        if echo "$context" | grep -q "applyConfiguration"; then
            if echo "$current_line" | grep -q "category log levels\|Updating Logger sinks\|Configuration applied successfully"; then
                echo "infrastructure_configuration_application"
                return
            fi
            # Also match "Applying" messages in applyConfiguration context
            if echo "$current_line" | grep -q "Applying"; then
                echo "infrastructure_configuration_application"
                return
            fi
        fi

        # Allow fflush in infrastructure files
        if [[ "$pattern" == "fflush(" ]]; then
            echo "infrastructure_flush"
            return
        fi
    fi

    # STRICT: Everything else is blocked
    echo "blocked"
}

# Function to determine if a violation should be allowed with STRICT enforcement
should_allow_violation() {
    local file="$1"
    local line_num="$2"
    local pattern="$3"
    local context="$4"

    # In strict mode, block everything
    if [[ "$STRICT_MODE" == true ]]; then
        return 1
    fi

    # STRICT: ALWAYS block Qt logging patterns - NO EXCEPTIONS
    for qt_pattern in "${QT_PATTERNS[@]}"; do
        if [[ "$pattern" == "$qt_pattern" ]]; then
            return 1  # ALWAYS BLOCK
        fi
    done

    # STRICT: ALWAYS block stream patterns - NO EXCEPTIONS
    for stream_pattern in "${STREAM_PATTERNS[@]}"; do
        if [[ "$pattern" == "$stream_pattern" ]]; then
            return 1  # ALWAYS BLOCK
        fi
    done

    # STRICT: Handle printf patterns with very limited exceptions
    for printf_pattern in "${PRINTF_PATTERNS[@]}"; do
        if [[ "$pattern" == "$printf_pattern" ]]; then
            # STRICT: Only allow very specific contexts
            case "$context" in
                "exception_handler")
                    return 0  # Allow in catch blocks
                    ;;
                "test_mode")
                    return 0  # Allow in test mode fallbacks
                    ;;
                "constructor_initialization")
                    return 0  # Allow in constructors (high recursion risk)
                    ;;
                "critical_initialization")
                    return 0  # Allow specific main.cpp initialization messages
                    ;;
                "critical_flush")
                    return 0  # Allow fflush after critical initialization
                    ;;
                "infrastructure_initialization")
                    return 0  # Allow in infrastructure during initialization
                    ;;
                "infrastructure_critical_initialization")
                    return 0  # Allow fprintf() during critical Logger initialization (prevents recursion)
                    ;;
                "circular_dependency")
                    return 0  # Allow to prevent circular dependencies
                    ;;
                "infrastructure_flush")
                    return 0  # Allow fflush in infrastructure files
                    ;;
                "infrastructure_configuration")
                    return 0  # Allow fprintf() in configuration functions
                    ;;
                "infrastructure_error_reporting")
                    return 0  # Allow fprintf() for error reporting in infrastructure
                    ;;
                "infrastructure_configuration_tracking")
                    return 0  # Allow fprintf() for configuration change tracking
                    ;;
                "infrastructure_configuration_application")
                    return 0  # Allow fprintf() for configuration application
                    ;;
                *)
                    return 1  # BLOCK everything else
                    ;;
            esac
        fi
    done

    # STRICT: Block all unknown patterns
    return 1
}

# Function to get explanation for a decision with STRICT policy messaging
get_explanation() {
    local file="$1"
    local pattern="$2"
    local context="$3"
    local allowed="$4"

    local relative_path="${file#$PROJECT_ROOT/}"

    if [[ "$allowed" == "true" ]]; then
        case "$context" in
            "exception_handler")
                echo "ALLOWED: fprintf() in exception handler - critical path where logging system may be compromised"
                ;;
            "test_mode")
                echo "ALLOWED: fprintf() in test mode fallback - logging system unavailable in test environment"
                ;;
            "constructor_initialization")
                echo "ALLOWED: fprintf() in constructor - high recursion risk with VLOG_* during static initialization"
                ;;
            "critical_initialization")
                echo "ALLOWED: fprintf() for critical logging system initialization in main.cpp"
                ;;
            "critical_flush")
                echo "ALLOWED: fflush() immediately following critical initialization fprintf()"
                ;;
            "infrastructure_initialization")
                echo "ALLOWED: fprintf() during infrastructure initialization when logging system unavailable"
                ;;
            "infrastructure_critical_initialization")
                echo "ALLOWED: fprintf() during critical Logger initialization - REQUIRED to prevent recursive VLOG_* calls"
                ;;
            "circular_dependency")
                echo "ALLOWED: fprintf() to prevent circular dependency in logging configuration"
                ;;
            "infrastructure_flush")
                echo "ALLOWED: fflush() in logging infrastructure for proper output handling"
                ;;
            "infrastructure_configuration")
                echo "ALLOWED: fprintf() in infrastructure configuration functions (setFileLoggingEnabled, loadConfiguration, updateSinks)"
                ;;
            "infrastructure_error_reporting")
                echo "ALLOWED: fprintf() for critical error reporting in infrastructure files"
                ;;
            "infrastructure_configuration_tracking")
                echo "ALLOWED: fprintf() for configuration change tracking in LogConfig"
                ;;
            "infrastructure_configuration_application")
                echo "ALLOWED: fprintf() for configuration application process (category levels, sink updates)"
                ;;
            *)
                echo "ALLOWED: fprintf() in logging infrastructure file ($relative_path)"
                ;;
        esac
    else
        if [[ "$STRICT_MODE" == true ]]; then
            echo "BLOCKED: Strict mode - all violations flagged for review"
        else
            for qt_pattern in "${QT_PATTERNS[@]}"; do
                if [[ "$pattern" == "$qt_pattern" ]]; then
                    echo "BLOCKED: Qt logging function - MUST use VLOG_* macros (strict policy)"
                    return
                fi
            done

            for stream_pattern in "${STREAM_PATTERNS[@]}"; do
                if [[ "$pattern" == "$stream_pattern" ]]; then
                    echo "BLOCKED: C++ stream output - MUST use VLOG_* macros (strict policy)"
                    return
                fi
            done

            echo "BLOCKED: fprintf() not in approved critical initialization context - MUST use VLOG_* macros (strict policy)"
        fi
    fi
}

# Main pattern checking logic
echo "Checking for deprecated logging patterns..."
FILES_TO_CHECK=$(get_files_to_check)

if [ -z "$FILES_TO_CHECK" ]; then
    echo "No C++ source files to check."
    exit 0
fi

# Combine all patterns for checking
ALL_PATTERNS=("${QT_PATTERNS[@]}" "${STREAM_PATTERNS[@]}" "${PRINTF_PATTERNS[@]}")

echo "Analyzing $(echo "$FILES_TO_CHECK" | wc -l) files..."

# Check each pattern in each file
for pattern in "${ALL_PATTERNS[@]}"; do
    while read -r file; do
        if [ ! -f "$file" ]; then
            continue
        fi

        # Find all occurrences of the pattern
        while IFS=: read -r line_num line_content; do
            if [ -z "$line_num" ]; then
                continue
            fi

            # Analyze the context around this violation
            context=$(analyze_context "$file" "$line_num" "$pattern")

            # Determine if this violation should be allowed
            if should_allow_violation "$file" "$line_num" "$pattern" "$context"; then
                # This is a legitimate exception
                ALLOWED_VIOLATIONS=$((ALLOWED_VIOLATIONS + 1))

                if [[ "$EXPLAIN_MODE" == true ]] || [[ "$STRICT_MODE" == true ]]; then
                    explanation=$(get_explanation "$file" "$pattern" "$context" "true")
                    if [[ "$STRICT_MODE" == true ]]; then
                        echo "⚠️  $explanation"
                    else
                        echo "✅ $explanation"
                    fi
                    echo "   ${file#$PROJECT_ROOT/}:$line_num: $line_content"
                    echo ""
                fi
            else
                # This is an illegitimate violation that must be fixed
                BLOCKED_VIOLATIONS=$((BLOCKED_VIOLATIONS + 1))

                explanation=$(get_explanation "$file" "$pattern" "$context" "false")
                echo "❌ $explanation"
                echo "   ${file#$PROJECT_ROOT/}:$line_num: $line_content"

                # Show fix suggestion
                case "$pattern" in
                    "qDebug()")
                        echo "   💡 Fix: Replace with VLOG_DEBUG(\"Category\", \"message\")"
                        ;;
                    "qWarning()")
                        echo "   💡 Fix: Replace with VLOG_WARNING(\"Category\", \"message\")"
                        ;;
                    "qCritical()")
                        echo "   💡 Fix: Replace with VLOG_ERROR(\"Category\", \"message\")"
                        ;;
                    "std::cout"|"cout <<")
                        echo "   💡 Fix: Replace with VLOG_INFO(\"Category\", \"message\")"
                        ;;
                    "std::cerr"|"cerr <<")
                        echo "   💡 Fix: Replace with VLOG_ERROR(\"Category\", \"message\")"
                        ;;
                    "printf("|"fprintf(stdout"|"fprintf(stderr")
                        echo "   💡 Fix: Replace with appropriate VLOG_* macro"
                        ;;
                esac
                echo ""
            fi
        done < <(grep -n "$pattern" "$file" 2>/dev/null || true)
    done < <(echo "$FILES_TO_CHECK")
done

# Check VLOG_* category usage
echo "Checking VLOG_* category compliance..."
echo "$FILES_TO_CHECK" | while read -r file; do
    if [ ! -f "$file" ]; then
        continue
    fi

    # Extract VLOG_* calls and check categories
    grep -n "VLOG_[A-Z]*(" "$file" 2>/dev/null | while IFS=: read -r line_num line_content; do
        # Extract the category from VLOG_*(category, message)
        category=$(echo "$line_content" | sed -n 's/.*VLOG_[A-Z]*(\s*"\([^"]*\)".*/\1/p')

        if [[ -n "$category" ]]; then
            # Check if category is valid
            valid=0
            for valid_cat in "${VALID_CATEGORIES[@]}"; do
                if [[ "$category" == "$valid_cat" ]]; then
                    valid=1
                    break
                fi
            done

            if [[ $valid -eq 0 ]]; then
                echo "❌ Invalid VLOG category '$category' in: $file:$line_num"
                echo "  $line_content"
                VIOLATIONS_FOUND=1
            fi
        fi
    done
done

# Check for missing includes
echo "Checking for proper logging includes..."
echo "$FILES_TO_CHECK" | while read -r file; do
    if [ ! -f "$file" ]; then
        continue
    fi

    # Skip header files for include checks (they might not need includes)
    if [[ "$file" == *.h ]] || [[ "$file" == *.hpp ]]; then
        continue
    fi

    # Check if file uses VLOG_* but doesn't include logger.h or result.h
    if grep -q "VLOG_" "$file" 2>/dev/null; then
        if ! grep -q '#include "utils/logger.h"' "$file" 2>/dev/null && \
           ! grep -q '#include "utils/result.h"' "$file" 2>/dev/null; then
            echo "❌ File $file uses VLOG_* but missing proper include"
            echo "  Add: #include \"utils/result.h\" (for Result<T> + logging)"
            echo "  Or:  #include \"utils/logger.h\" (for direct VLOG_* usage)"
            VIOLATIONS_FOUND=1
        fi
    fi

    # Check if file uses Result<T> but doesn't include result.h
    if grep -q "Result<" "$file" 2>/dev/null; then
        if ! grep -q '#include "utils/result.h"' "$file" 2>/dev/null; then
            echo "❌ File $file uses Result<T> but missing #include \"utils/result.h\""
            VIOLATIONS_FOUND=1
        fi
    fi
done

# Count violations by re-scanning (since subshells don't propagate variables)
echo ""
echo "📊 Scanning for final violation counts..."

ACTUAL_BLOCKED=0
ACTUAL_ALLOWED=0

for pattern in "${ALL_PATTERNS[@]}"; do
    while read -r file; do
        if [ ! -f "$file" ]; then
            continue
        fi

        while IFS=: read -r line_num line_content; do
            if [ -z "$line_num" ]; then
                continue
            fi

            context=$(analyze_context "$file" "$line_num" "$pattern")

            if should_allow_violation "$file" "$line_num" "$pattern" "$context"; then
                ACTUAL_ALLOWED=$((ACTUAL_ALLOWED + 1))
            else
                ACTUAL_BLOCKED=$((ACTUAL_BLOCKED + 1))
            fi
        done < <(grep -n "$pattern" "$file" 2>/dev/null || true)
    done < <(echo "$FILES_TO_CHECK")
done

# Check for invalid VLOG categories
INVALID_CATEGORIES=0
echo "$FILES_TO_CHECK" | while read -r file; do
    if [ ! -f "$file" ]; then
        continue
    fi

    if grep -q "VLOG_[A-Z]*(" "$file" 2>/dev/null; then
        categories=$(grep "VLOG_[A-Z]*(" "$file" 2>/dev/null | sed -n 's/.*VLOG_[A-Z]*(\s*"\([^"]*\)".*/\1/p')
        for category in $categories; do
            valid=0
            for valid_cat in "${VALID_CATEGORIES[@]}"; do
                if [[ "$category" == "$valid_cat" ]]; then
                    valid=1
                    break
                fi
            done
            if [[ $valid -eq 0 ]] && [[ -n "$category" ]]; then
                echo "❌ Invalid VLOG category '$category' in: ${file#$PROJECT_ROOT/}"
                INVALID_CATEGORIES=$((INVALID_CATEGORIES + 1))
                ACTUAL_BLOCKED=$((ACTUAL_BLOCKED + 1))
            fi
        done
    fi
done

# Final reporting and exit logic
echo ""
echo "📋 COMPLIANCE SUMMARY:"
echo "   Blocked violations (must fix): $ACTUAL_BLOCKED"
echo "   Allowed exceptions: $ACTUAL_ALLOWED"
echo "   Invalid VLOG categories: $INVALID_CATEGORIES"

if [[ $ACTUAL_BLOCKED -gt 0 ]]; then
    echo ""
    echo "❌ STRICT LOGGING POLICY VIOLATION"
    echo ""
    echo "🚫 STRICT POLICY: 99% of codebase must use VLOG_* macros"
    echo ""
    echo "💡 Required fixes:"
    echo "  - qDebug() → VLOG_DEBUG(\"Category\", \"message\") [NO EXCEPTIONS]"
    echo "  - qWarning() → VLOG_WARNING(\"Category\", \"message\") [NO EXCEPTIONS]"
    echo "  - qCritical() → VLOG_ERROR(\"Category\", \"message\") [NO EXCEPTIONS]"
    echo "  - std::cout → VLOG_INFO(\"Category\", \"message\") [NO EXCEPTIONS]"
    echo "  - std::cerr → VLOG_ERROR(\"Category\", \"message\") [NO EXCEPTIONS]"
    echo "  - printf()/fprintf() → VLOG_* macro [VERY LIMITED EXCEPTIONS]"
    echo ""
    echo "⚠️  fprintf() only allowed in:"
    echo "  - Critical logging system initialization (main.cpp)"
    echo "  - Exception handlers (catch blocks)"
    echo "  - Test mode fallbacks (#ifdef QT_TESTLIB_LIB)"
    echo ""
    echo "📚 Valid categories: ${VALID_CATEGORIES[*]}"
    echo "📖 Documentation: docs/features/logging_system.md"
    echo ""
    echo "🔧 Run with --explain to see detailed reasoning"
    echo "🔍 Run with --strict to flag all violations for review"
    exit 1
elif [[ $ACTUAL_ALLOWED -gt 0 ]] && [[ "$STRICT_MODE" == true ]]; then
    echo ""
    echo "⚠️  STRICT MODE WARNING"
    echo "Found $ACTUAL_ALLOWED legitimate exceptions that should be reviewed."
    echo "These are normally allowed but flagged in strict mode for code review."
    exit 2
else
    echo ""
    echo "✅ STRICT LOGGING POLICY COMPLIANCE VERIFIED"
    if [[ $ACTUAL_ALLOWED -gt 0 ]]; then
        echo "   ($ACTUAL_ALLOWED critical infrastructure exceptions allowed)"
        echo "   🎯 Target: <10 fprintf() calls in entire codebase"
    else
        echo "   🎯 Perfect compliance: 100% VLOG_* usage"
    fi
    exit 0
fi
