#!/bin/bash

# Compare Performance of Original vs Optimized Clang-Tidy Hook
# This script measures and compares the performance of both hook versions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo -e "${BLUE}=== Clang-Tidy Hook Performance Comparison ===${NC}"
echo "Project: Vizion3D"
echo "Timestamp: $(date)"
echo ""

cd "$PROJECT_ROOT"

# Check prerequisites
echo -e "${YELLOW}Checking prerequisites...${NC}"

if [[ ! -f "$SCRIPT_DIR/clang-tidy-precommit-hook.sh" ]]; then
    echo -e "${RED}Error: Original hook not found${NC}"
    exit 1
fi

if [[ ! -f "$SCRIPT_DIR/clang-tidy-precommit-hook-optimized.sh" ]]; then
    echo -e "${RED}Error: Optimized hook not found${NC}"
    exit 1
fi

# Check for build directory
BUILD_DIR=""
for build_dir in "build" "build-debug" "cmake-build-debug" "cmake-build-release"; do
    if [[ -d "$build_dir" && -f "$build_dir/compile_commands.json" ]]; then
        BUILD_DIR="$build_dir"
        break
    fi
done

if [[ -z "$BUILD_DIR" ]]; then
    echo -e "${RED}Error: No build directory with compile_commands.json found${NC}"
    exit 1
fi

echo -e "${GREEN}Prerequisites OK${NC}"
echo "Using build directory: $BUILD_DIR"
echo ""

# Get test files (use actual staged files or sample files)
TEST_FILES=$(git diff --cached --name-only --diff-filter=ACM 2>/dev/null | \
    grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' | \
    grep -v -E '(build/|.*_autogen/|ui_.*\.h$)' || true)

if [[ -z "$TEST_FILES" ]]; then
    # If no staged files, use existing files for testing
    echo -e "${YELLOW}No staged files found, using existing files for testing${NC}"

    # Find some representative files
    TEST_FILES=$(find src include -name "*.cpp" -o -name "*.h" | grep -v -E '(build/|.*_autogen/|ui_.*\.h$)' | head -3)

    if [[ -n "$TEST_FILES" ]]; then
        # Stage them temporarily for testing
        echo "$TEST_FILES" | while read -r file; do
            git add "$file" 2>/dev/null || true
        done

        # Get the staged files
        TEST_FILES=$(git diff --cached --name-only --diff-filter=ACM 2>/dev/null | \
            grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' | \
            grep -v -E '(build/|.*_autogen/|ui_.*\.h$)' || true)
    fi
fi

if [[ -z "$TEST_FILES" ]]; then
    echo -e "${RED}Error: No C++ files available for testing${NC}"
    exit 1
fi

echo "Test files:"
echo "$TEST_FILES" | while read -r file; do
    echo "  - $file"
done
echo ""

# Function to run hook and measure time
run_hook_test() {
    local hook_script="$1"
    local test_name="$2"
    local env_vars="$3"

    echo -e "${YELLOW}Testing: $test_name${NC}"

    local start_time=$(date +%s)
    local output
    local exit_code

    # Run the hook with environment variables
    if output=$(env $env_vars "$hook_script" 2>&1); then
        exit_code=0
    else
        exit_code=$?
    fi

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    echo "  Duration: ${duration}s"
    echo "  Exit code: $exit_code"

    # Extract file count from output
    local file_count=$(echo "$output" | grep -o "Found [0-9]* staged" | grep -o "[0-9]*" || echo "0")
    echo "  Files analyzed: $file_count"

    # Show summary line from output
    local summary=$(echo "$output" | grep -E "(All files passed|found issues)" | head -1 || echo "No summary found")
    echo "  Result: $summary"

    echo ""
    echo "$duration $exit_code $file_count"
}

# Run performance tests
echo -e "${BLUE}=== Performance Tests ===${NC}"
echo ""

# Test 1: Original hook
echo "1. Original Hook (Full Configuration)"
original_result=$(run_hook_test "$SCRIPT_DIR/clang-tidy-precommit-hook.sh" "Original" "")
original_time=$(echo "$original_result" | cut -d' ' -f1)
original_exit=$(echo "$original_result" | cut -d' ' -f2)
original_files=$(echo "$original_result" | cut -d' ' -f3)

# Test 2: Optimized hook (sequential)
echo "2. Optimized Hook (Sequential)"
optimized_seq_result=$(run_hook_test "$SCRIPT_DIR/clang-tidy-precommit-hook-optimized.sh" "Optimized Sequential" "CLANG_TIDY_PARALLEL=false")
optimized_seq_time=$(echo "$optimized_seq_result" | cut -d' ' -f1)
optimized_seq_exit=$(echo "$optimized_seq_result" | cut -d' ' -f2)

# Test 3: Optimized hook (parallel)
echo "3. Optimized Hook (Parallel)"
optimized_par_result=$(run_hook_test "$SCRIPT_DIR/clang-tidy-precommit-hook-optimized.sh" "Optimized Parallel" "CLANG_TIDY_PARALLEL=true")
optimized_par_time=$(echo "$optimized_par_result" | cut -d' ' -f1)
optimized_par_exit=$(echo "$optimized_par_result" | cut -d' ' -f2)

# Test 4: Optimized hook (fast mode)
echo "4. Optimized Hook (Fast Mode)"
optimized_fast_result=$(run_hook_test "$SCRIPT_DIR/clang-tidy-precommit-hook-optimized.sh" "Optimized Fast" "CLANG_TIDY_FAST=true CLANG_TIDY_PARALLEL=true")
optimized_fast_time=$(echo "$optimized_fast_result" | cut -d' ' -f1)
optimized_fast_exit=$(echo "$optimized_fast_result" | cut -d' ' -f2)

# Performance analysis
echo -e "${BLUE}=== Performance Analysis ===${NC}"
echo ""

printf "%-25s %8s %10s %12s\n" "Configuration" "Time(s)" "Exit Code" "Improvement"
printf "%-25s %8s %10s %12s\n" "-------------------------" "--------" "----------" "------------"
printf "%-25s %8s %10s %12s\n" "Original" "$original_time" "$original_exit" "baseline"

if [[ $original_time -gt 0 ]]; then
    local seq_improvement=$(awk "BEGIN {printf \"%.1fx\", $original_time / $optimized_seq_time}")
    printf "%-25s %8s %10s %12s\n" "Optimized Sequential" "$optimized_seq_time" "$optimized_seq_exit" "$seq_improvement"

    local par_improvement=$(awk "BEGIN {printf \"%.1fx\", $original_time / $optimized_par_time}")
    printf "%-25s %8s %10s %12s\n" "Optimized Parallel" "$optimized_par_time" "$optimized_par_exit" "$par_improvement"

    local fast_improvement=$(awk "BEGIN {printf \"%.1fx\", $original_time / $optimized_fast_time}")
    printf "%-25s %8s %10s %12s\n" "Optimized Fast Mode" "$optimized_fast_time" "$optimized_fast_exit" "$fast_improvement"
fi

echo ""

# Recommendations
echo -e "${BLUE}=== Recommendations ===${NC}"
echo ""

# Determine best configuration
local best_time=$original_time
local best_config="Original"

if [[ $optimized_seq_time -lt $best_time ]]; then
    best_time=$optimized_seq_time
    best_config="Optimized Sequential"
fi

if [[ $optimized_par_time -lt $best_time ]]; then
    best_time=$optimized_par_time
    best_config="Optimized Parallel"
fi

if [[ $optimized_fast_time -lt $best_time ]]; then
    best_time=$optimized_fast_time
    best_config="Optimized Fast Mode"
fi

echo -e "${GREEN}Best performing configuration: $best_config (${best_time}s)${NC}"
echo ""

if [[ $best_time -le 10 ]]; then
    echo -e "${GREEN}EXCELLENT: Hook completes in ≤10s${NC}"
    echo "Recommended: Use optimized hook with parallel processing"
elif [[ $best_time -le 30 ]]; then
    echo -e "${YELLOW}GOOD: Hook completes in ≤30s${NC}"
    echo "Recommended: Use optimized hook, consider fast mode for frequent commits"
else
    echo -e "${RED}SLOW: Hook takes >30s${NC}"
    echo "Recommended: Use fast mode for regular commits, full analysis for important commits"
fi

echo ""
echo "To use the optimized hook:"
echo "  1. Replace the original hook:"
echo "     cp scripts/pre-commit/clang-tidy-precommit-hook-optimized.sh scripts/pre-commit/clang-tidy-precommit-hook.sh"
echo ""
echo "  2. Configure performance options:"
echo "     export CLANG_TIDY_PARALLEL=true    # Enable parallel processing"
echo "     export CLANG_TIDY_FAST=true        # Enable fast mode"
echo "     export CLANG_TIDY_JOBS=4           # Set number of parallel jobs"
echo ""
echo "Performance comparison completed at $(date)"
