#!/bin/bash
# scripts/pre-commit/benchmark-precommit.sh
# Benchmark script to measure pre-commit hook performance

set -e

echo "🔍 Benchmarking Pre-commit Hook Performance"
echo "=========================================="

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Get staged files for testing
get_staged_files() {
    if git rev-parse --git-dir > /dev/null 2>&1; then
        git diff --cached --name-only --diff-filter=ACM 2>/dev/null | grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' || true
    fi
}

# Benchmark a command
benchmark_command() {
    local name="$1"
    local command="$2"
    
    echo ""
    echo "📊 Benchmarking: $name"
    echo "Command: $command"
    
    local start_time=$(date +%s.%N)
    
    # Run the command and capture exit code
    local exit_code=0
    eval "$command" || exit_code=$?
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    
    printf "⏱️  Duration: %.2f seconds (exit code: %d)\n" "$duration" "$exit_code"
    
    return $exit_code
}

# Main benchmarking
main() {
    cd "$PROJECT_ROOT"
    
    # Check if we have staged files
    STAGED_FILES=$(get_staged_files)
    
    if [ -z "$STAGED_FILES" ]; then
        echo "❌ No staged C++ files found for benchmarking"
        echo ""
        echo "To benchmark, stage some C++ files first:"
        echo "  git add src/some_file.cpp"
        echo "  ./scripts/pre-commit/benchmark-precommit.sh"
        exit 1
    fi
    
    local file_count=$(echo "$STAGED_FILES" | wc -l)
    echo "📁 Found $file_count staged C++ files:"
    echo "$STAGED_FILES" | while read -r file; do
        echo "  - $file"
    done
    
    echo ""
    echo "🚀 Starting benchmarks..."
    
    # Benchmark individual components
    benchmark_command "Logging Compliance Check" \
        "$SCRIPT_DIR/check-logging.sh"
    
    benchmark_command "Clang-Tidy Analysis" \
        "CLANG_TIDY_FAST=true $SCRIPT_DIR/clang-tidy-precommit-hook.sh"
    
    # Benchmark combined hooks (current system)
    echo ""
    echo "🔄 Benchmarking combined current system..."
    local combined_start=$(date +%s.%N)
    
    local logging_exit=0
    local clang_exit=0
    
    # Run logging check
    STAGED_FILES_LIST=$(mktemp)
    echo "$STAGED_FILES" > "$STAGED_FILES_LIST"
    STAGED_FILES_LIST="$STAGED_FILES_LIST" "$SCRIPT_DIR/check-logging.sh" || logging_exit=$?
    rm -f "$STAGED_FILES_LIST"
    
    # Run clang-tidy check
    CLANG_TIDY_FAST=true "$SCRIPT_DIR/clang-tidy-precommit-hook.sh" || clang_exit=$?
    
    local combined_end=$(date +%s.%N)
    local combined_duration=$(echo "$combined_end - $combined_start" | bc -l)
    
    printf "⏱️  Combined Duration: %.2f seconds\n" "$combined_duration"
    
    echo ""
    echo "📋 BENCHMARK SUMMARY"
    echo "==================="
    echo "Files analyzed: $file_count"
    printf "Total time: %.2f seconds\n" "$combined_duration"
    echo "Logging check exit code: $logging_exit"
    echo "Clang-tidy check exit code: $clang_exit"
    
    # Performance assessment
    if (( $(echo "$combined_duration > 30" | bc -l) )); then
        echo ""
        echo "❌ PERFORMANCE ISSUE: >30 seconds (unacceptable)"
        echo "🎯 Target: <15 seconds for typical commits"
    elif (( $(echo "$combined_duration > 15" | bc -l) )); then
        echo ""
        echo "⚠️  PERFORMANCE WARNING: >15 seconds (needs optimization)"
        echo "🎯 Target: <15 seconds for typical commits"
    else
        echo ""
        echo "✅ PERFORMANCE ACCEPTABLE: <15 seconds"
    fi
    
    echo ""
    echo "💡 To optimize performance:"
    echo "  1. Run parallel checks instead of sequential"
    echo "  2. Optimize logging checker for single-pass analysis"
    echo "  3. Cache compilation database location"
    echo "  4. Use faster pattern matching algorithms"
}

# Check dependencies
if ! command -v bc > /dev/null; then
    echo "❌ Error: 'bc' command not found (required for timing calculations)"
    echo "Install with: brew install bc"
    exit 1
fi

# Run main function
main "$@"
