#!/bin/bash
# scripts/pre-commit/fast-precommit-dispatcher.sh
# Optimized Pre-commit Hook Dispatcher with Parallel Execution
#
# This script runs logging compliance and clang-tidy checks in parallel
# to achieve <15 second execution time for typical commits.

set -e

echo "🚀 Fast Pre-commit Analysis (Parallel Execution)"

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Performance tracking
START_TIME=$(date +%s.%N)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get staged C++ files (optimized single call)
get_staged_files() {
    if git rev-parse --git-dir > /dev/null 2>&1; then
        git diff --cached --name-only --diff-filter=ACM 2>/dev/null | \
            grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' | \
            grep -v -E '(build/|.*_autogen/|ui_.*\.h$|moc_.*\.cpp$|qrc_.*\.cpp$)' || true
    fi
}

# Fast exit if no files to check
STAGED_FILES=$(get_staged_files)

if [ -z "$STAGED_FILES" ]; then
    echo "ℹ️  No C++ files staged for commit, skipping analysis"
    exit 0
fi

file_count=$(echo "$STAGED_FILES" | wc -l)
echo "📁 Analyzing $file_count staged C++ files in parallel..."

# Create temporary files for results
LOGGING_RESULT=$(mktemp)
CLANG_RESULT=$(mktemp)
STAGED_FILES_LIST=$(mktemp)

# Cleanup on exit
trap "rm -f '$LOGGING_RESULT' '$CLANG_RESULT' '$STAGED_FILES_LIST'" EXIT

# Write staged files to temporary file
echo "$STAGED_FILES" > "$STAGED_FILES_LIST"

echo ""
echo "🔄 Starting parallel analysis..."

# Start logging compliance check in background
(
    echo "🔍 [Logging] Starting compliance check..." > "$LOGGING_RESULT"
    if STAGED_FILES_LIST="$STAGED_FILES_LIST" "$SCRIPT_DIR/fast-logging-check.sh" >> "$LOGGING_RESULT" 2>&1; then
        echo "EXIT_CODE:0" >> "$LOGGING_RESULT"
    else
        echo "EXIT_CODE:1" >> "$LOGGING_RESULT"
    fi
) &
LOGGING_PID=$!

# Start clang-tidy analysis in background
(
    echo "🔧 [Clang-Tidy] Starting analysis..." > "$CLANG_RESULT"
    if CLANG_TIDY_FAST=true ENABLE_PARALLEL=true "$SCRIPT_DIR/fast-clang-tidy.sh" >> "$CLANG_RESULT" 2>&1; then
        echo "EXIT_CODE:0" >> "$CLANG_RESULT"
    else
        echo "EXIT_CODE:1" >> "$CLANG_RESULT"
    fi
) &
CLANG_PID=$!

# Wait for both processes with progress indication
echo "⏳ Waiting for parallel analysis to complete..."

# Simple progress indicator
progress_counter=0
while kill -0 $LOGGING_PID 2>/dev/null || kill -0 $CLANG_PID 2>/dev/null; do
    printf "."
    sleep 0.5
    ((progress_counter++))
    if [ $progress_counter -gt 60 ]; then  # 30 seconds timeout
        echo ""
        echo "⚠️  Analysis taking longer than expected..."
        break
    fi
done

echo ""

# Wait for processes to complete and get exit codes
wait $LOGGING_PID 2>/dev/null || true
wait $CLANG_PID 2>/dev/null || true

# Extract results
LOGGING_EXIT=$(grep "EXIT_CODE:" "$LOGGING_RESULT" | cut -d: -f2)
CLANG_EXIT=$(grep "EXIT_CODE:" "$CLANG_RESULT" | cut -d: -f2)

# Calculate total time
END_TIME=$(date +%s.%N)
TOTAL_TIME=$(echo "$END_TIME - $START_TIME" | bc -l 2>/dev/null || echo "unknown")

echo ""
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BLUE}                           PARALLEL ANALYSIS RESULTS                        ${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# Show logging results
echo ""
echo -e "${BLUE}📋 Logging Compliance Results:${NC}"
if [ "$LOGGING_EXIT" = "0" ]; then
    echo -e "  ${GREEN}✅ PASSED${NC}"
else
    echo -e "  ${RED}❌ FAILED${NC}"
    echo ""
    echo "Logging compliance issues:"
    grep -v "EXIT_CODE:" "$LOGGING_RESULT" | sed 's/^/  /'
fi

# Show clang-tidy results
echo ""
echo -e "${BLUE}🔧 Clang-Tidy Analysis Results:${NC}"
if [ "$CLANG_EXIT" = "0" ]; then
    echo -e "  ${GREEN}✅ PASSED${NC}"
else
    echo -e "  ${RED}❌ FAILED${NC}"
    echo ""
    echo "Clang-tidy issues:"
    grep -v "EXIT_CODE:" "$CLANG_RESULT" | sed 's/^/  /'
fi

# Performance summary
echo ""
echo -e "${BLUE}⏱️  Performance Summary:${NC}"
echo "  Files analyzed: $file_count"
if [ "$TOTAL_TIME" != "unknown" ]; then
    printf "  Total time: %.2f seconds\n" "$TOTAL_TIME"
    
    if (( $(echo "$TOTAL_TIME < 15" | bc -l 2>/dev/null || echo 0) )); then
        echo -e "  ${GREEN}🎯 Performance target achieved (<15s)${NC}"
    else
        echo -e "  ${YELLOW}⚠️  Performance target missed (>15s)${NC}"
    fi
else
    echo "  Total time: $TOTAL_TIME"
fi

# Final result
echo ""
if [ "$LOGGING_EXIT" = "0" ] && [ "$CLANG_EXIT" = "0" ]; then
    echo -e "${GREEN}✅ All checks passed - commit can proceed${NC}"
    exit 0
else
    echo -e "${RED}❌ Quality checks failed - commit blocked${NC}"
    echo ""
    echo "To fix issues:"
    if [ "$LOGGING_EXIT" != "0" ]; then
        echo "  1. Fix logging compliance violations"
        echo "     Run: ./scripts/pre-commit/fix-logging-violations.py --apply"
    fi
    if [ "$CLANG_EXIT" != "0" ]; then
        echo "  2. Fix clang-tidy warnings"
        echo "     Review warnings above and fix in your code"
    fi
    echo "  3. Re-stage fixed files: git add <files>"
    echo "  4. Commit again: git commit"
    echo ""
    echo "To bypass (not recommended): git commit --no-verify"
    exit 1
fi
