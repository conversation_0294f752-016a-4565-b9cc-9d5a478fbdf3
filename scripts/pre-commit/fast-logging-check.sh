#!/bin/bash
# scripts/pre-commit/fast-logging-check.sh
# Optimized Logging Compliance Checker with Single-Pass Analysis
#
# This script performs all logging pattern checks in a single pass per file
# for maximum performance while maintaining strict compliance checking.

set -e

# Configuration
STRICT_MODE=${STRICT_MODE:-false}
EXPLAIN_MODE=${EXPLAIN_MODE:-false}

# Define patterns (combined for efficiency)
QT_PATTERNS="qDebug()|qWarning()|qCritical()|qInfo()|qFatal()"
STREAM_PATTERNS="std::cout|std::cerr|cout[[:space:]]*<<|cerr[[:space:]]*<<"
PRINTF_PATTERNS="printf(|fprintf(stdout|fprintf(stderr|fflush("
ALL_PATTERNS="$QT_PATTERNS|$STREAM_PATTERNS|$PRINTF_PATTERNS"

# Valid VLOG categories
VALID_CATEGORIES="GCode|Engine|UI|OpenGL|FileIO|System|Threading|Validation"

# Infrastructure files (legitimate fprintf usage)
INFRASTRUCTURE_FILES="src/utils/logger.cpp|src/utils/log_config.cpp"

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Get files to check (optimized)
get_files_to_check() {
    if [[ -n "$STAGED_FILES_LIST" && -f "$STAGED_FILES_LIST" ]]; then
        cat "$STAGED_FILES_LIST" | grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' || true
    elif git rev-parse --git-dir > /dev/null 2>&1; then
        git diff --cached --name-only --diff-filter=ACM 2>/dev/null | \
            grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' || true
    else
        find "$PROJECT_ROOT/src" "$PROJECT_ROOT/include" -name "*.cpp" -o -name "*.h" -o -name "*.hpp" 2>/dev/null || true
    fi
}

# Fast infrastructure file check
is_infrastructure_file() {
    local file="$1"
    local relative_path="${file#$PROJECT_ROOT/}"
    [[ "$relative_path" =~ $INFRASTRUCTURE_FILES ]]
}

# Fast context analysis (simplified for performance)
analyze_context_fast() {
    local file="$1"
    local line_num="$2"
    local pattern="$3"
    local line_content="$4"
    
    # Get minimal context (±2 lines for speed)
    local context=$(sed -n "$((line_num-2)),$((line_num+2))p" "$file" 2>/dev/null || echo "")
    
    # Fast pattern matching for common cases
    if [[ "$context" =~ catch[[:space:]]*\( ]] || [[ "$context" =~ \}[[:space:]]*catch ]]; then
        echo "exception_handler"
        return
    fi
    
    if [[ "$context" =~ \#ifdef[[:space:]]+QT_TESTLIB_LIB ]] || [[ "$context" =~ \#ifndef[[:space:]]+QT_TESTLIB_LIB ]]; then
        echo "test_mode"
        return
    fi
    
    # Infrastructure file checks
    if is_infrastructure_file "$file"; then
        if [[ "$context" =~ !m_initialized ]] || [[ "$line_content" =~ Logger[[:space:]]+not[[:space:]]+initialized ]]; then
            echo "infrastructure_initialization"
            return
        fi
        
        if [[ "$pattern" == "fflush(" ]]; then
            echo "infrastructure_flush"
            return
        fi
        
        # Critical initialization patterns
        if [[ "$line_content" =~ (Logger already initialized|Setting up console|Console sink added|Basic logger|File sink added|LogConfig initialized|Logging system initialized) ]]; then
            echo "infrastructure_critical_initialization"
            return
        fi
    fi
    
    # Main.cpp specific checks
    local relative_path="${file#$PROJECT_ROOT/}"
    if [[ "$relative_path" == "src/main.cpp" ]]; then
        if [[ "$line_content" =~ fprintf.*Initializing[[:space:]]+LogConfig ]] || \
           [[ "$line_content" =~ fprintf.*LogConfig[[:space:]]+initialized ]] || \
           [[ "$line_content" =~ fprintf.*Setting[[:space:]]+category[[:space:]]+log[[:space:]]+levels ]] || \
           [[ "$line_content" =~ fprintf.*Installing[[:space:]]+Qt[[:space:]]+message[[:space:]]+handler ]]; then
            echo "critical_initialization"
            return
        fi
        
        if [[ "$pattern" == "fflush(" ]] && [[ "$context" =~ (logging|logconfig|message.*handler|VLOG_INFO.*system) ]]; then
            echo "critical_flush"
            return
        fi
    fi
    
    echo "blocked"
}

# Fast violation checking
should_allow_violation_fast() {
    local pattern="$1"
    local context="$2"
    
    # Strict mode blocks everything
    [[ "$STRICT_MODE" == "true" ]] && return 1
    
    # Always block Qt and stream patterns
    [[ "$pattern" =~ ^(qDebug|qWarning|qCritical|qInfo|qFatal|std::cout|std::cerr|cout|cerr) ]] && return 1
    
    # Allow printf patterns only in specific contexts
    case "$context" in
        "exception_handler"|"test_mode"|"constructor_initialization"|"critical_initialization"|"critical_flush"|"infrastructure_initialization"|"infrastructure_critical_initialization"|"infrastructure_flush")
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# Single-pass file analysis (main optimization)
analyze_file_fast() {
    local file="$1"
    local violations=0
    
    # Single grep call to find all patterns
    local matches=$(grep -n -E "($ALL_PATTERNS)" "$file" 2>/dev/null || true)
    
    if [[ -n "$matches" ]]; then
        while IFS=: read -r line_num line_content; do
            [[ -z "$line_num" ]] && continue
            
            # Determine which pattern matched
            local pattern=""
            if [[ "$line_content" =~ qDebug\(\) ]]; then pattern="qDebug()"
            elif [[ "$line_content" =~ qWarning\(\) ]]; then pattern="qWarning()"
            elif [[ "$line_content" =~ qCritical\(\) ]]; then pattern="qCritical()"
            elif [[ "$line_content" =~ qInfo\(\) ]]; then pattern="qInfo()"
            elif [[ "$line_content" =~ qFatal\(\) ]]; then pattern="qFatal()"
            elif [[ "$line_content" =~ std::cout ]]; then pattern="std::cout"
            elif [[ "$line_content" =~ std::cerr ]]; then pattern="std::cerr"
            elif [[ "$line_content" =~ cout[[:space:]]*\<\< ]]; then pattern="cout <<"
            elif [[ "$line_content" =~ cerr[[:space:]]*\<\< ]]; then pattern="cerr <<"
            elif [[ "$line_content" =~ printf\( ]]; then pattern="printf("
            elif [[ "$line_content" =~ fprintf\(stdout ]]; then pattern="fprintf(stdout"
            elif [[ "$line_content" =~ fprintf\(stderr ]]; then pattern="fprintf(stderr"
            elif [[ "$line_content" =~ fflush\( ]]; then pattern="fflush("
            fi
            
            [[ -z "$pattern" ]] && continue
            
            # Fast context analysis
            local context=$(analyze_context_fast "$file" "$line_num" "$pattern" "$line_content")
            
            # Check if violation should be allowed
            if ! should_allow_violation_fast "$pattern" "$context"; then
                echo "❌ BLOCKED: $pattern in ${file#$PROJECT_ROOT/}:$line_num"
                echo "   $line_content"
                ((violations++))
            fi
        done <<< "$matches"
    fi
    
    # Fast VLOG category check
    local vlog_matches=$(grep -n "VLOG_[A-Z]*(" "$file" 2>/dev/null || true)
    if [[ -n "$vlog_matches" ]]; then
        while IFS=: read -r line_num line_content; do
            [[ -z "$line_num" ]] && continue
            
            # Extract category
            local category=$(echo "$line_content" | sed -n 's/.*VLOG_[A-Z]*(\s*"\([^"]*\)".*/\1/p')
            
            if [[ -n "$category" ]] && ! [[ "$category" =~ ^($VALID_CATEGORIES)$ ]]; then
                echo "❌ Invalid VLOG category '$category' in ${file#$PROJECT_ROOT/}:$line_num"
                ((violations++))
            fi
        done <<< "$vlog_matches"
    fi
    
    # Fast include check (only for .cpp files)
    if [[ "$file" =~ \.(cpp|cxx|cc|c\+\+)$ ]]; then
        if grep -q "VLOG_" "$file" 2>/dev/null; then
            if ! grep -q '#include "utils/logger.h"' "$file" 2>/dev/null && \
               ! grep -q '#include "utils/result.h"' "$file" 2>/dev/null; then
                echo "❌ File ${file#$PROJECT_ROOT/} uses VLOG_* but missing proper include"
                ((violations++))
            fi
        fi
        
        if grep -q "Result<" "$file" 2>/dev/null; then
            if ! grep -q '#include "utils/result.h"' "$file" 2>/dev/null; then
                echo "❌ File ${file#$PROJECT_ROOT/} uses Result<T> but missing #include \"utils/result.h\""
                ((violations++))
            fi
        fi
    fi
    
    return $violations
}

# Main execution
main() {
    local files_to_check=$(get_files_to_check)
    
    if [[ -z "$files_to_check" ]]; then
        echo "✅ No C++ files to check"
        exit 0
    fi
    
    local file_count=$(echo "$files_to_check" | wc -l)
    local total_violations=0
    
    echo "🔍 Fast logging compliance check on $file_count files..."
    
    # Process files efficiently
    while read -r file; do
        [[ ! -f "$file" ]] && continue
        
        local file_violations=0
        analyze_file_fast "$file" || file_violations=$?
        total_violations=$((total_violations + file_violations))
    done <<< "$files_to_check"
    
    echo ""
    if [[ $total_violations -eq 0 ]]; then
        echo "✅ Logging compliance verified ($file_count files)"
        exit 0
    else
        echo "❌ Found $total_violations logging violations"
        echo ""
        echo "💡 Run auto-fix: ./scripts/pre-commit/fix-logging-violations.py --apply"
        exit 1
    fi
}

# Run main function
main "$@"
