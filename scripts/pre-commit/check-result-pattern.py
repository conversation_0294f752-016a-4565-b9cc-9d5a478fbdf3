#!/usr/bin/env python3
"""
scripts/pre-commit/check-result-pattern.py
Vizion3D Result<T> Pattern Enforcement

This script automatically detects functions that should return Result<T>
but use legacy patterns, identifies functions with try/catch blocks that
don't return Result<T>, and validates error handling patterns.
"""

import sys
import re
import os
from pathlib import Path
from typing import List, Dict, Any

class ResultPatternChecker:
    def __init__(self):
        self.violations = []
        self.engine_keywords = [
            'parse', 'load', 'save', 'init', 'process', 'execute', 'create',
            'export', 'import', 'read', 'write', 'open', 'close', 'connect',
            'setup', 'configure', 'validate', 'verify'
        ]
        self.deprecated_patterns = [
            'qDebug', 'qWarning', 'qCritical', 'qInfo', 'qFatal',
            'std::cout', 'std::cerr', 'printf', 'fprintf'
        ]

    def check_file(self, filepath: Path) -> None:
        """Check a single file for Result<T> pattern violations."""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"Warning: Could not read {filepath}: {e}")
            return

        lines = content.split('\n')

        # Check if this is an engine file
        is_engine_file = ('src/engine/' in str(filepath) or
                         'include/engine/' in str(filepath))

        self._check_functions(content, lines, filepath, is_engine_file)
        self._check_deprecated_logging(content, lines, filepath)
        self._check_try_catch_patterns(content, lines, filepath)

    def _check_functions(self, content: str, lines: List[str],
                        filepath: Path, is_engine_file: bool) -> None:
        """Check if functions should return Result<T>."""

        # Pattern to match function declarations/definitions
        # This is a simplified regex - in practice, you'd want a proper C++ parser
        function_pattern = r'^\s*(?:(?:virtual|static|inline)\s+)*(\w+(?:<[^>]*>)?)\s+(\w+)\s*\([^)]*\)\s*(?:const\s*)?(?:override\s*)?[{;]'

        for i, line in enumerate(lines):
            match = re.match(function_pattern, line)
            if not match:
                continue

            return_type = match.group(1).strip()
            func_name = match.group(2).strip()

            # Skip constructors, destructors, and operators
            if (func_name == filepath.stem or
                func_name.startswith('~') or
                func_name.startswith('operator')):
                continue

            # Skip common getters/setters and simple functions
            if (func_name.startswith('get') or func_name.startswith('set') or
                func_name.startswith('is') or func_name.startswith('has') or
                return_type in ['void', 'int', 'bool', 'QString', 'QStringList']):
                continue

            # Check engine functions with error-prone keywords
            if is_engine_file:
                func_name_lower = func_name.lower()
                if any(keyword in func_name_lower for keyword in self.engine_keywords):
                    if return_type in ['bool', 'void'] and 'Result<' not in return_type:
                        self.violations.append({
                            'type': 'missing_result_type',
                            'file': str(filepath),
                            'line': i + 1,
                            'function': func_name,
                            'return_type': return_type,
                            'message': f"Engine function '{func_name}' should return Result<T>, not '{return_type}'"
                        })

    def _check_deprecated_logging(self, content: str, lines: List[str],
                                 filepath: Path) -> None:
        """Check for deprecated logging patterns."""

        for i, line in enumerate(lines):
            for pattern in self.deprecated_patterns:
                if pattern in line and not line.strip().startswith('//'):
                    self.violations.append({
                        'type': 'deprecated_logging',
                        'file': str(filepath),
                        'line': i + 1,
                        'pattern': pattern,
                        'message': f"Use VLOG_* macros instead of '{pattern}'"
                    })

    def _check_try_catch_patterns(self, content: str, lines: List[str],
                                 filepath: Path) -> None:
        """Check for functions with try/catch that don't return Result<T>."""

        # Find try blocks and check the containing function
        in_try_block = False
        try_start_line = 0
        brace_count = 0

        for i, line in enumerate(lines):
            stripped = line.strip()

            # Count braces to track scope
            brace_count += stripped.count('{') - stripped.count('}')

            if 'try' in stripped and '{' in stripped:
                in_try_block = True
                try_start_line = i + 1

            elif in_try_block and 'catch' in stripped:
                # Found a try/catch block, now find the containing function
                func_info = self._find_containing_function(lines, try_start_line)
                if func_info and 'Result<' not in func_info['return_type']:
                    self.violations.append({
                        'type': 'try_catch_without_result',
                        'file': str(filepath),
                        'line': func_info['line'],
                        'function': func_info['name'],
                        'message': f"Function '{func_info['name']}' uses try/catch but doesn't return Result<T>"
                    })
                in_try_block = False

    def _find_containing_function(self, lines: List[str], try_line: int) -> Dict[str, Any]:
        """Find the function that contains the given try block."""

        # Look backwards from try_line to find function declaration
        for i in range(try_line - 1, max(0, try_line - 20), -1):
            line = lines[i]

            # Simple function pattern matching
            func_match = re.match(r'^\s*(\w+(?:<[^>]*>)?)\s+(\w+)\s*\([^)]*\)', line)
            if func_match:
                return {
                    'line': i + 1,
                    'return_type': func_match.group(1),
                    'name': func_match.group(2)
                }

        return None

def get_files_to_check() -> List[Path]:
    """Get list of files to check (either from git or all source files)."""
    script_dir = Path(__file__).parent
    project_root = script_dir.parent.parent

    files = []

    # Try to get staged files from git first
    try:
        import subprocess
        result = subprocess.run(
            ['git', 'diff', '--cached', '--name-only', '--diff-filter=ACM'],
            capture_output=True, text=True, cwd=project_root
        )
        if result.returncode == 0 and result.stdout.strip():
            staged_files = result.stdout.strip().split('\n')
            files = [project_root / f for f in staged_files
                    if f.endswith(('.cpp', '.cxx', '.cc', '.h', '.hpp'))]
    except:
        pass

    # If no staged files or not in git, check all source files
    if not files:
        for pattern in ['**/*.cpp', '**/*.cxx', '**/*.cc', '**/*.h', '**/*.hpp']:
            files.extend(project_root.glob(f'src/{pattern}'))
            files.extend(project_root.glob(f'include/{pattern}'))

    return files

def main():
    """Main entry point."""
    if len(sys.argv) > 1:
        # Files specified on command line
        files = [Path(f) for f in sys.argv[1:]]
    else:
        # Auto-detect files to check
        files = get_files_to_check()

    if not files:
        print("No C++ source files to check.")
        return 0

    checker = ResultPatternChecker()

    for filepath in files:
        if filepath.exists() and filepath.suffix in ['.cpp', '.cxx', '.cc', '.h', '.hpp']:
            checker.check_file(filepath)

    if checker.violations:
        print("❌ Result<T> pattern violations found:")
        print()

        # Group violations by type
        by_type = {}
        for violation in checker.violations:
            vtype = violation['type']
            if vtype not in by_type:
                by_type[vtype] = []
            by_type[vtype].append(violation)

        for vtype, violations in by_type.items():
            print(f"📋 {vtype.replace('_', ' ').title()}:")
            for violation in violations[:10]:  # Limit output
                print(f"  {violation['file']}:{violation['line']} - {violation['message']}")
            if len(violations) > 10:
                print(f"  ... and {len(violations) - 10} more")
            print()

        print("💡 Result<T> pattern fixes:")
        print("  - Change engine functions to return Result<T> instead of bool/void")
        print("  - Replace try/catch with Result<T> error propagation")
        print("  - Use VLOG_* macros instead of deprecated logging")
        print("  - Include 'utils/result.h' for Result<T> support")
        print()
        print("📖 Documentation: docs/implementation/direct_error_handling_implementation.md")

        return 1
    else:
        print("✅ No Result<T> pattern violations found")
        return 0

if __name__ == '__main__':
    sys.exit(main())
