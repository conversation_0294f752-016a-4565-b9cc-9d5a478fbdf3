#!/usr/bin/env python3
"""
Vizion3D Complex Logging Pattern Fix Tool

This script handles complex qDebug(), qWarning(), qCritical(), and fprintf() patterns
that involve multiple stream operations and multi-line statements.
"""

import re
import sys
import argparse

def parse_qdebug_stream(stream_content):
    """Parse a qDebug() stream operation and extract text and variable parts"""
    parts = []
    current_part = ""
    in_quotes = False
    paren_depth = 0
    i = 0

    while i < len(stream_content):
        char = stream_content[i]

        if char == '"' and (i == 0 or stream_content[i-1] != '\\'):
            in_quotes = not in_quotes
            current_part += char
        elif char == '(' and not in_quotes:
            paren_depth += 1
            current_part += char
        elif char == ')' and not in_quotes:
            paren_depth -= 1
            current_part += char
        elif char == '<' and not in_quotes and paren_depth == 0:
            # Check if this is a << operator
            if i + 1 < len(stream_content) and stream_content[i + 1] == '<':
                # End current part and start new one
                if current_part.strip():
                    parts.append(current_part.strip())
                current_part = ""
                i += 1  # Skip the second <
            else:
                current_part += char
        else:
            current_part += char

        i += 1

    # Add the last part
    if current_part.strip():
        parts.append(current_part.strip())

    return parts

def convert_qdebug_to_vlog(parts, category):
    """Convert parsed qDebug() parts to VLOG_DEBUG format"""
    if not parts:
        return None

    # Check if it's a simple text-only case
    if len(parts) == 1 and parts[0].startswith('"') and parts[0].endswith('"'):
        text = parts[0][1:-1]  # Remove quotes
        return f'VLOG_DEBUG("{category}", "{text}");'

    # Build format string and collect variables
    format_str = ""
    var_parts = []
    var_index = 1

    for i, part in enumerate(parts):
        if part.startswith('"') and part.endswith('"'):
            # Text part - remove quotes and add to format string
            text = part[1:-1]
            format_str += text
        else:
            # Variable part - add placeholder to format string and collect variable
            # Add a space before the placeholder if needed for readability
            if i > 0 and format_str and not format_str.endswith(' ') and not format_str.endswith('('):
                format_str += " "
            format_str += f"%{var_index}"
            var_parts.append(part)
            var_index += 1

    # Build the arg chain
    if var_parts:
        arg_chain = ""
        for var in var_parts:
            arg_chain += f".arg({var})"
        return f'VLOG_DEBUG("{category}", QString("{format_str}"){arg_chain});'
    else:
        # No variables, just text
        return f'VLOG_DEBUG("{category}", "{format_str}");'

def fix_complex_qdebug_patterns(content, category):
    """Fix complex qDebug() patterns using a proper parser"""
    fixes = 0

    # Handle qDebug().nospace() patterns first
    nospace_pattern = r'qDebug\(\)\.nospace\(\)\s*<<\s*(.+?);'
    def replace_nospace(match):
        nonlocal fixes
        stream_content = match.group(1)
        parts = parse_qdebug_stream(stream_content)
        result = convert_qdebug_to_vlog(parts, category)
        if result:
            fixes += 1
            return result
        return match.group(0)  # Return original if conversion failed

    content = re.sub(nospace_pattern, replace_nospace, content, flags=re.DOTALL)

    # Handle regular qDebug() patterns
    regular_pattern = r'qDebug\(\)\s*<<\s*(.+?);'
    def replace_regular(match):
        nonlocal fixes
        stream_content = match.group(1)
        parts = parse_qdebug_stream(stream_content)
        result = convert_qdebug_to_vlog(parts, category)
        if result:
            fixes += 1
            return result
        return match.group(0)  # Return original if conversion failed

    content = re.sub(regular_pattern, replace_regular, content, flags=re.DOTALL)

    return content, fixes

def fix_complex_qwarning_patterns(content, category):
    """Fix complex qWarning() patterns"""
    fixes = 0

    # Pattern for qWarning() with stream operations
    pattern1 = r'qWarning\(\)\s*<<\s*"([^"]*?)"\s*<<\s*([^;]+?);'
    def replace1(match):
        nonlocal fixes
        fixes += 1
        text = match.group(1)
        var = match.group(2).strip()
        return f'VLOG_WARNING("{category}", QString("{text}%1").arg({var}));'
    content = re.sub(pattern1, replace1, content)

    # Pattern for simple qWarning() << "message";
    pattern2 = r'qWarning\(\)\s*<<\s*"([^"]*?)"\s*;'
    def replace2(match):
        nonlocal fixes
        fixes += 1
        text = match.group(1)
        return f'VLOG_WARNING("{category}", "{text}");'
    content = re.sub(pattern2, replace2, content)

    return content, fixes

def fix_complex_qcritical_patterns(content, category):
    """Fix complex qCritical() patterns"""
    fixes = 0

    # Similar patterns for qCritical
    pattern1 = r'qCritical\(\)\s*<<\s*"([^"]*?)"\s*<<\s*([^;]+?);'
    def replace1(match):
        nonlocal fixes
        fixes += 1
        text = match.group(1)
        var = match.group(2).strip()
        return f'VLOG_ERROR("{category}", QString("{text}%1").arg({var}));'
    content = re.sub(pattern1, replace1, content)

    return content, fixes

def fix_nospace_patterns(content, category):
    """Fix qDebug().nospace() patterns"""
    fixes = 0

    # Pattern for .nospace() calls
    pattern1 = r'qDebug\(\)\.nospace\(\)\s*<<\s*"([^"]*?)"\s*<<\s*([^;]+?);'
    def replace1(match):
        nonlocal fixes
        fixes += 1
        text = match.group(1)
        var = match.group(2).strip()
        return f'VLOG_DEBUG("{category}", QString("{text}%1").arg({var}));'
    content = re.sub(pattern1, replace1, content)

    return content, fixes

def is_infrastructure_exception(line, line_number, lines):
    """
    STRICT POLICY: Determine if an fprintf() call is a legitimate infrastructure exception.

    Only allows fprintf() in very specific critical contexts:
    - Exception handlers (catch blocks)
    - Test mode fallbacks (#ifdef QT_TESTLIB_LIB)
    - Critical logging system initialization messages in main.cpp

    Returns True if the fprintf() should NOT be converted (is an exception).
    """
    # Get precise context (only ±2 lines for strict analysis)
    start = max(0, line_number - 2)
    end = min(len(lines), line_number + 3)
    context = '\n'.join(lines[start:end]).lower()

    # Also check the current line and immediate neighbors
    current_line = lines[line_number].lower()
    prev_line = lines[line_number - 1].lower() if line_number > 0 else ""
    next_line = lines[line_number + 1].lower() if line_number < len(lines) - 1 else ""

    # STRICT: Check for direct exception handling context (catch block)
    if 'catch' in prev_line or 'catch' in current_line or 'catch' in next_line:
        return True

    # STRICT: Check for test mode context
    if '#ifdef qt_testlib' in context or '#ifndef qt_testlib' in context:
        return True

    # STRICT: Only allow very specific logging initialization messages
    fprintf_match = re.search(r'fprintf\s*\([^,]+,\s*"([^"]*)"', current_line, re.IGNORECASE)
    if fprintf_match:
        message = fprintf_match.group(1).lower()
        # STRICT: Only these exact critical initialization messages
        critical_init_messages = [
            'initializing logconfig',
            'logconfig initialized',
            'setting category log levels',
            'category log levels set',
            'installing qt message handler',
            'qt message handler installed'
        ]

        for init_msg in critical_init_messages:
            if init_msg in message:
                return True

    # STRICT: Check for comments indicating infrastructure use
    if '// exception handler' in current_line or '// fallback' in current_line:
        return True

    # STRICT: Everything else is blocked
    return False

def convert_fprintf_format_string(format_str, args):
    """
    Convert C-style format string to QString format with .arg() calls.

    Args:
        format_str: The format string (e.g., "Value: %d, Name: %s")
        args: List of arguments

    Returns:
        Tuple of (converted_format_string, arg_chain)
    """
    if not args:
        return format_str, ""

    # Convert C format specifiers to Qt format specifiers
    # %s, %d, %f, %c, etc. -> %1, %2, %3, etc.
    converted_format = format_str
    arg_index = 1

    # Common C format specifiers
    c_specifiers = [r'%s', r'%d', r'%i', r'%f', r'%g', r'%e', r'%c', r'%x', r'%X', r'%o', r'%u']

    for spec in c_specifiers:
        while spec in converted_format:
            converted_format = converted_format.replace(spec, f'%{arg_index}', 1)
            arg_index += 1

    # Build .arg() chain
    arg_chain = ""
    for arg in args:
        arg_chain += f".arg({arg.strip()})"

    return converted_format, arg_chain

def fix_fprintf_patterns(content, category):
    """Fix fprintf() patterns while respecting infrastructure exceptions"""
    fixes = 0
    lines = content.split('\n')

    for i, line in enumerate(lines):
        # Skip if this is an infrastructure exception
        if is_infrastructure_exception(line, i, lines):
            continue

        # Pattern 1: fprintf(stdout, "simple message");
        pattern1 = r'fprintf\s*\(\s*stdout\s*,\s*"([^"]*?)"\s*\)\s*;'
        match1 = re.search(pattern1, line)
        if match1:
            message = match1.group(1)
            # Remove \n if present at the end
            message = message.rstrip('\\n')
            replacement = f'VLOG_INFO("{category}", "{message}");'
            lines[i] = re.sub(pattern1, replacement, line)
            fixes += 1
            continue

        # Pattern 2: fprintf(stderr, "simple message");
        pattern2 = r'fprintf\s*\(\s*stderr\s*,\s*"([^"]*?)"\s*\)\s*;'
        match2 = re.search(pattern2, line)
        if match2:
            message = match2.group(1)
            # Remove \n if present at the end
            message = message.rstrip('\\n')
            replacement = f'VLOG_ERROR("{category}", "{message}");'
            lines[i] = re.sub(pattern2, replacement, line)
            fixes += 1
            continue

        # Pattern 3: fprintf(stdout, "format %s %d", var1, var2);
        pattern3 = r'fprintf\s*\(\s*stdout\s*,\s*"([^"]*?)"\s*,\s*([^)]+?)\)\s*;'
        match3 = re.search(pattern3, line)
        if match3:
            format_str = match3.group(1)
            args_str = match3.group(2)
            args = [arg.strip() for arg in args_str.split(',')]

            # Remove \n if present at the end of format string
            format_str = format_str.rstrip('\\n')

            converted_format, arg_chain = convert_fprintf_format_string(format_str, args)
            replacement = f'VLOG_INFO("{category}", QString("{converted_format}"){arg_chain});'
            lines[i] = re.sub(pattern3, replacement, line)
            fixes += 1
            continue

        # Pattern 4: fprintf(stderr, "format %s %d", var1, var2);
        pattern4 = r'fprintf\s*\(\s*stderr\s*,\s*"([^"]*?)"\s*,\s*([^)]+?)\)\s*;'
        match4 = re.search(pattern4, line)
        if match4:
            format_str = match4.group(1)
            args_str = match4.group(2)
            args = [arg.strip() for arg in args_str.split(',')]

            # Remove \n if present at the end of format string
            format_str = format_str.rstrip('\\n')

            converted_format, arg_chain = convert_fprintf_format_string(format_str, args)
            replacement = f'VLOG_ERROR("{category}", QString("{converted_format}"){arg_chain});'
            lines[i] = re.sub(pattern4, replacement, line)
            fixes += 1
            continue

    return '\n'.join(lines), fixes

def ensure_logger_include(content, file_path):
    """
    Ensure that utils/logger.h is included in C++ files that use VLOG_* macros.
    """
    # Only process .cpp and .h files
    if not (file_path.endswith('.cpp') or file_path.endswith('.h')):
        return content, False

    # Check if VLOG_* macros are used
    if not re.search(r'VLOG_', content):
        return content, False

    # Check if logger.h is already included
    if '#include "utils/logger.h"' in content:
        return content, False

    # Find the first #include statement and add logger.h after it
    lines = content.split('\n')
    include_added = False

    for i, line in enumerate(lines):
        if line.strip().startswith('#include "') and not include_added:
            # Find the last local include (with quotes)
            last_local_include = i
            for j in range(i + 1, len(lines)):
                if lines[j].strip().startswith('#include "'):
                    last_local_include = j
                elif lines[j].strip().startswith('#include <') or not lines[j].strip().startswith('#include'):
                    break

            # Insert logger.h after the last local include
            lines.insert(last_local_include + 1, '#include "utils/logger.h"')
            include_added = True
            break

    if include_added:
        return '\n'.join(lines), True
    else:
        return content, False

def main():
    parser = argparse.ArgumentParser(description='Fix complex Qt logging and fprintf patterns')
    parser.add_argument('file', help='File to process')
    parser.add_argument('category', help='Logging category to use')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be changed')
    parser.add_argument('--fprintf-only', action='store_true', help='Only fix fprintf patterns')
    parser.add_argument('--qt-only', action='store_true', help='Only fix Qt logging patterns')

    args = parser.parse_args()

    try:
        with open(args.file, 'r') as f:
            content = f.read()

        original_content = content
        total_fixes = 0

        # Apply pattern fixes based on options
        if not args.fprintf_only:
            # Apply Qt logging pattern fixes
            content, qdebug_fixes = fix_complex_qdebug_patterns(content, args.category)
            total_fixes += qdebug_fixes

            content, qwarning_fixes = fix_complex_qwarning_patterns(content, args.category)
            total_fixes += qwarning_fixes

            content, qcritical_fixes = fix_complex_qcritical_patterns(content, args.category)
            total_fixes += qcritical_fixes

            content, nospace_fixes = fix_nospace_patterns(content, args.category)
            total_fixes += nospace_fixes

        if not args.qt_only:
            # Apply fprintf pattern fixes
            content, fprintf_fixes = fix_fprintf_patterns(content, args.category)
            total_fixes += fprintf_fixes

        # Ensure logger.h include is present if VLOG_* macros are used
        content, include_added = ensure_logger_include(content, args.file)
        if include_added:
            total_fixes += 1

        if not args.dry_run and content != original_content:
            with open(args.file, 'w') as f:
                f.write(content)

        print(total_fixes)
        return 0

    except Exception as e:
        print(f"Error processing file: {e}", file=sys.stderr)
        return 1

if __name__ == '__main__':
    sys.exit(main())
