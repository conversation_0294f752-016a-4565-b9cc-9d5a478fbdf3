#!/bin/bash
# scripts/pre-commit/clang-tidy-precommit-hook.sh
# Consolidated Git Pre-commit Hook for Clang-Tidy Analysis
#
# This script runs clang-tidy analysis on staged C++ files with:
# - Clear, professional output showing detailed warnings
# - Performance optimizations for faster analysis
# - Proper error handling and reporting
# - Support for both sequential and parallel analysis

set -e

# Performance configuration
MAX_PARALLEL_JOBS=${CLANG_TIDY_JOBS:-$(nproc 2>/dev/null || echo 2)}
ENABLE_PARALLEL=${CLANG_TIDY_PARALLEL:-false}  # Sequential by default for clearer output
FAST_MODE=${CLANG_TIDY_FAST:-false}

echo "Running clang-tidy pre-commit analysis..."

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Performance tracking
START_TIME=$(date +%s)

# Get staged C++ files (optimized filtering)
get_staged_files() {
    if git rev-parse --git-dir > /dev/null 2>&1; then
        git diff --cached --name-only --diff-filter=ACM 2>/dev/null | \
            grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' | \
            grep -v -E '(build/|.*_autogen/|ui_.*\.h$|moc_.*\.cpp$|qrc_.*\.cpp$)' || true
    fi
}

# Check if build directory exists with compile_commands.json
check_build_directory() {
    local build_dirs=("build" "build-debug" "cmake-build-debug" "cmake-build-release")

    for build_dir in "${build_dirs[@]}"; do
        if [[ -d "$PROJECT_ROOT/$build_dir" && -f "$PROJECT_ROOT/$build_dir/compile_commands.json" ]]; then
            echo "$PROJECT_ROOT/$build_dir"
            return 0
        fi
    done

    return 1
}

# Get optimized clang-tidy arguments
get_clang_tidy_args() {
    local args=""

    # Fast mode uses reduced check set for performance
    if [[ "$FAST_MODE" == "true" ]]; then
        args="--checks=-*,bugprone-*,clang-analyzer-*,readability-identifier-naming,modernize-use-nullptr,modernize-use-override"
    fi

    # Optimize header filtering to exclude build artifacts
    args="$args --header-filter='^(?!.*/build/)(?!.*moc_)(?!.*ui_).*\.(h|hpp)$'"

    # Performance and output optimizations
    args="$args --use-color=false"
    # Don't use --quiet so we can see detailed warnings

    echo "$args"
}

# Run clang-tidy on a single file with detailed output
analyze_file() {
    local file="$1"
    local build_dir="$2"
    local temp_output

    temp_output=$(mktemp)

    # Get clang-tidy arguments
    local clang_tidy_args=$(get_clang_tidy_args)

    # Run clang-tidy and capture output
    local clang_exit_code=0
    clang-tidy -p "$build_dir" $clang_tidy_args "$file" > "$temp_output" 2>&1 || clang_exit_code=$?

    # Check if there are any warnings or errors
    if grep -q -E "(warning:|error:)" "$temp_output"; then
        echo ""
        echo -e "${BLUE}━━━ Issues found in $file ━━━${NC}"
        echo ""

        # Process and display warnings/errors with better formatting
        local issue_count=0
        while IFS= read -r line; do
            if [[ "$line" =~ ^.*:([0-9]+):([0-9]+):.*(warning|error):(.*) ]]; then
                local line_num="${BASH_REMATCH[1]}"
                local col_num="${BASH_REMATCH[2]}"
                local severity="${BASH_REMATCH[3]}"
                local message="${BASH_REMATCH[4]}"
                ((issue_count++))

                if [[ "$severity" == "error" ]]; then
                    echo -e "  ${RED}ERROR${NC} at line $line_num:$col_num:$message"
                else
                    echo -e "  ${YELLOW}WARNING${NC} at line $line_num:$col_num:$message"
                fi
            elif [[ "$line" =~ ^.*:([0-9]+):([0-9]+):.*(note):(.*) ]]; then
                local line_num="${BASH_REMATCH[1]}"
                local col_num="${BASH_REMATCH[2]}"
                local message="${BASH_REMATCH[4]}"
                echo -e "    ${BLUE}NOTE${NC} at line $line_num:$col_num:$message"
            fi
        done < <(grep -E "(warning:|error:|note:)" "$temp_output")

        echo ""
        echo -e "${BLUE}━━━ End of issues for $file ━━━${NC}"
        echo ""
        rm -f "$temp_output"
        return 1
    elif [[ $clang_exit_code -ne 0 ]]; then
        echo ""
        echo -e "${RED}Analysis failed for $file${NC}"
        echo "Error output:"
        cat "$temp_output" | sed 's/^/  /'
        echo ""
        rm -f "$temp_output"
        return 1
    else
        # File is clean
        rm -f "$temp_output"
        return 0
    fi
}

# Parallel analysis function
analyze_files_parallel() {
    local build_dir="$1"
    shift
    local files=("$@")

    local pids=()
    local results=()
    local job_count=0
    local failed_files=0

    # Create temporary directory for job results
    local temp_dir=$(mktemp -d)

    for file in "${files[@]}"; do
        if [[ -f "$file" ]]; then
            # Wait if we've reached max parallel jobs
            while [[ ${#pids[@]} -ge $MAX_PARALLEL_JOBS ]]; do
                for i in "${!pids[@]}"; do
                    if ! kill -0 "${pids[$i]}" 2>/dev/null; then
                        wait "${pids[$i]}"
                        local exit_code=$?
                        if [[ $exit_code -ne 0 ]]; then
                            ((failed_files++))
                        fi
                        unset pids[$i]
                    fi
                done
                pids=("${pids[@]}")  # Reindex array
                sleep 0.1
            done

            # Start new job
            ((job_count++))
            (
                analyze_file "$file" "$build_dir" "$job_count"
                echo $? > "$temp_dir/job_$job_count.result"
            ) &
            pids+=($!)
        fi
    done

    # Wait for all remaining jobs
    for pid in "${pids[@]}"; do
        wait "$pid"
    done

    # Collect results
    for result_file in "$temp_dir"/job_*.result; do
        if [[ -f "$result_file" ]]; then
            local exit_code=$(cat "$result_file")
            if [[ $exit_code -ne 0 ]]; then
                ((failed_files++))
            fi
        fi
    done

    # Cleanup
    rm -rf "$temp_dir"

    echo "$failed_files $job_count"
}

# Note: Sequential analysis is now inlined in main() for better output control

# Main execution
main() {
    cd "$PROJECT_ROOT"

    # Get staged files
    STAGED_FILES=$(get_staged_files)

    if [[ -z "$STAGED_FILES" ]]; then
        echo "No C++ files staged for commit, skipping clang-tidy analysis"
        exit 0
    fi

    # Convert to array for easier handling (macOS compatible)
    local FILES_ARRAY=()
    while IFS= read -r line; do
        FILES_ARRAY+=("$line")
    done <<< "$STAGED_FILES"
    local file_count=${#FILES_ARRAY[@]}

    echo "Found $file_count staged C++ files to analyze:"
    for file in "${FILES_ARRAY[@]}"; do
        echo "  - $(basename "$file")"
    done
    echo ""

    # Check for build directory
    if ! BUILD_DIR=$(check_build_directory); then
        echo ""
        echo -e "${RED}No build directory with compile_commands.json found${NC}"
        echo ""
        echo "To fix this issue:"
        echo "   1. Build the project first:"
        echo "      mkdir build && cd build"
        echo "      cmake .. && cmake --build ."
        echo ""
        echo "Commit blocked - build the project first, then try again"
        echo ""
        echo "To bypass this check (not recommended):"
        echo "   git commit --no-verify"
        exit 1
    fi

    echo "Using build directory: $(basename "$BUILD_DIR")"
    echo ""

    # Analyze files
    local failed_files=0
    local total_files=0

    if [[ "$ENABLE_PARALLEL" == "true" && $file_count -gt 1 ]]; then
        # For parallel analysis, capture result
        local result=$(analyze_files_parallel "$BUILD_DIR" "${FILES_ARRAY[@]}")
        failed_files=$(echo "$result" | tail -1 | cut -d' ' -f1)
        total_files=$(echo "$result" | tail -1 | cut -d' ' -f2)
    else
        # For sequential analysis, run directly to show output, then count results
        echo "Analyzing files sequentially..."
        echo ""

        for file in "${FILES_ARRAY[@]}"; do
            if [[ -f "$file" ]]; then
                ((total_files++))
                echo -e "${BLUE}[$total_files/$file_count] Analyzing $(basename "$file")...${NC}"

                # Call analyze_file and capture its return code
                # The detailed output from analyze_file will be displayed directly
                if analyze_file "$file" "$BUILD_DIR"; then
                    echo -e "  ${GREEN}✓ $(basename "$file") - CLEAN${NC}"
                else
                    echo -e "  ${RED}✗ $(basename "$file") - ISSUES FOUND${NC}"
                    ((failed_files++))
                fi
                echo ""
            fi
        done
    fi

    # Ensure we have valid numbers
    if [[ ! "$failed_files" =~ ^[0-9]+$ ]]; then
        failed_files=0
    fi
    if [[ ! "$total_files" =~ ^[0-9]+$ ]]; then
        total_files=0
    fi

    local clean_files=$((total_files - failed_files))

    echo ""
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${BLUE}                              ANALYSIS SUMMARY                              ${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo ""
    echo "  Files analyzed: $total_files"
    echo "  Files with issues: $failed_files"
    echo "  Clean files: $clean_files"
    echo ""

    if [[ $failed_files -gt 0 ]]; then
        echo -e "${RED}Clang-tidy found issues in $failed_files file(s)${NC}"
        echo ""
        echo "To fix these issues:"
        echo "  1. Review the detailed warnings shown above"
        echo "  2. Fix the issues in your code"
        echo "  3. Re-stage the fixed files: git add <files>"
        echo "  4. Commit again: git commit"
        echo ""
        echo "To bypass this check (not recommended): git commit --no-verify"
        exit 1
    else
        echo -e "${GREEN}All files passed clang-tidy analysis${NC}"
        echo "Code quality checks passed - proceeding with commit"
        exit 0
    fi
}

# Run main function
main "$@"
