#!/bin/bash
# scripts/pre-commit/logging-precommit-hook.sh
# Git Pre-commit Hook for Logging Compliance with Auto-fix Integration
#
# This script implements the complete pre-commit workflow for logging compliance:
# 1. Checks staged files for logging violations
# 2. Automatically fixes violations where possible
# 3. Unstages modified files so developers can review changes
# 4. Provides clear instructions for next steps
#
# Workflow:
# - Developer stages files (git add)
# - Developer attempts commit (git commit)
# - This hook runs automatically
# - If violations found, auto-fixes are applied and files unstaged
# - <PERSON><PERSON><PERSON> must review fixes and re-stage files before committing

set -e

echo "🔍 Running logging compliance pre-commit hook..."

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Get staged C++ files
get_staged_files() {
    if git rev-parse --git-dir > /dev/null 2>&1; then
        git diff --cached --name-only --diff-filter=ACM 2>/dev/null | grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' || true
    fi
}

# Calculate file checksums to detect changes
calculate_checksums() {
    local files="$1"
    local checksum_file="$2"

    if [ -n "$files" ]; then
        echo "$files" | while read -r file; do
            if [ -f "$file" ]; then
                echo "$(md5sum "$file" 2>/dev/null || md5 "$file" 2>/dev/null)" >> "$checksum_file"
            fi
        done
    fi
}

# Compare checksums to find modified files
find_modified_files() {
    local before_checksums="$1"
    local after_checksums="$2"

    if [ ! -f "$before_checksums" ] || [ ! -f "$after_checksums" ]; then
        return
    fi

    # Compare checksum files and extract filenames of changed files
    diff "$before_checksums" "$after_checksums" 2>/dev/null | grep "^>" | sed 's/^> [a-f0-9]* *//' || true
}

# Main execution
main() {
    # Get staged files
    STAGED_FILES=$(get_staged_files)

    if [ -z "$STAGED_FILES" ]; then
        echo "ℹ️  No C++ files staged for commit, skipping logging compliance check"
        exit 0
    fi

    echo "📁 Checking $(echo "$STAGED_FILES" | wc -l) staged C++ files for logging compliance"

    # Create temporary files for checksum comparison
    BEFORE_CHECKSUMS=$(mktemp)
    AFTER_CHECKSUMS=$(mktemp)

    # Ensure cleanup on exit
    trap "rm -f '$BEFORE_CHECKSUMS' '$AFTER_CHECKSUMS'; find . -name '*.bak' -type f -delete 2>/dev/null || true" EXIT

    # Calculate checksums before any modifications
    calculate_checksums "$STAGED_FILES" "$BEFORE_CHECKSUMS"

    echo ""
    echo "🔍 Checking for logging violations..."

    # Create temporary file with staged files for check-logging.sh
    TEMP_STAGED_LIST=$(mktemp)
    echo "$STAGED_FILES" > "$TEMP_STAGED_LIST"

    # Run logging compliance check on staged files only by setting environment variable
    if STAGED_FILES_LIST="$TEMP_STAGED_LIST" "$SCRIPT_DIR/check-logging.sh"; then
        rm -f "$TEMP_STAGED_LIST"
        echo "✅ No logging violations found in staged files"
        exit 0
    fi

    rm -f "$TEMP_STAGED_LIST"

    echo ""
    echo "⚠️  Logging violations detected in staged files"
    echo "🔧 Attempting automatic fixes..."

    # Run auto-fix on staged files
    if ! "$SCRIPT_DIR/fix-logging-violations.py" --apply --files $(echo "$STAGED_FILES" | tr '\n' ' '); then
        echo ""
        echo "❌ Auto-fix failed. Please review and fix violations manually."
        echo ""
        echo "📖 For help with logging patterns:"
        echo "   - Documentation: docs/features/logging_system.md"
        echo "   - Manual fix: Run ./scripts/pre-commit/fix-logging-violations.py --help"
        echo ""
        echo "🚫 Commit blocked until violations are resolved"
        exit 1
    fi

    # Calculate checksums after modifications
    calculate_checksums "$STAGED_FILES" "$AFTER_CHECKSUMS"

    # Find which files were actually modified
    MODIFIED_FILES=$(find_modified_files "$BEFORE_CHECKSUMS" "$AFTER_CHECKSUMS")

    if [ -z "$MODIFIED_FILES" ]; then
        echo ""
        echo "ℹ️  No files were modified by auto-fix (violations may require manual attention)"
        echo "🚫 Commit blocked - please review and fix violations manually"
        exit 1
    fi

    echo ""
    echo "✅ Auto-fix completed successfully!"
    echo ""
    echo "📝 Modified files:"
    echo "$MODIFIED_FILES" | while read -r file; do
        echo "   - $file"
    done

    echo ""
    echo "🔄 Unstaging modified files for your review..."

    # Unstage the modified files
    echo "$MODIFIED_FILES" | while read -r file; do
        if [ -n "$file" ]; then
            git reset HEAD "$file" 2>/dev/null || true
            echo "   📤 Unstaged: $file"
        fi
    done

    echo ""
    echo "🎯 Next steps:"
    echo "   1. Review the auto-fixes in the modified files above"
    echo "   2. If the fixes look correct, re-stage the files:"
    echo "      git add <files>"
    echo "   3. Commit again:"
    echo "      git commit"
    echo ""
    echo "💡 The auto-fixes have been applied but not staged to ensure you can review them first"
    echo "🚫 Commit blocked - please review and re-stage the fixed files"

    exit 1
}

# Run main function
main "$@"
