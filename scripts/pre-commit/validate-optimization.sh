#!/bin/bash
# scripts/pre-commit/validate-optimization.sh
# Validation Script for Pre-commit Performance Optimizations
#
# This script validates that the optimized pre-commit system meets performance targets

set -e

echo "🎯 Pre-commit Optimization Validation"
echo "====================================="

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Performance targets
TARGET_TIME=15  # seconds
WARNING_TIME=30 # seconds

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    if ! command -v bc > /dev/null; then
        missing_deps+=("bc")
    fi
    
    if ! command -v timeout > /dev/null; then
        missing_deps+=("timeout (coreutils)")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        echo "❌ Missing dependencies:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        echo ""
        echo "Install with: brew install bc coreutils"
        exit 1
    fi
}

# Get staged files
get_staged_files() {
    if git rev-parse --git-dir > /dev/null 2>&1; then
        git diff --cached --name-only --diff-filter=ACM 2>/dev/null | grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' || true
    fi
}

# Validate optimized system performance
validate_performance() {
    local command="$1"
    local name="$2"
    
    echo ""
    echo -e "${BLUE}🔍 Testing: $name${NC}"
    
    local start_time=$(date +%s.%N)
    local exit_code=0
    
    # Run with timeout
    if timeout 60s bash -c "$command" > /dev/null 2>&1; then
        exit_code=0
    else
        exit_code=$?
        if [[ $exit_code -eq 124 ]]; then
            echo -e "  ${RED}❌ TIMEOUT (>60s) - FAILED${NC}"
            return 1
        fi
    fi
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    
    printf "  ⏱️  Duration: %.2f seconds\n" "$duration"
    
    # Performance assessment
    if (( $(echo "$duration < $TARGET_TIME" | bc -l) )); then
        echo -e "  ${GREEN}✅ EXCELLENT - Target achieved (<${TARGET_TIME}s)${NC}"
        return 0
    elif (( $(echo "$duration < $WARNING_TIME" | bc -l) )); then
        echo -e "  ${YELLOW}⚠️  ACCEPTABLE - Within warning threshold (<${WARNING_TIME}s)${NC}"
        return 0
    else
        echo -e "  ${RED}❌ POOR - Exceeds warning threshold (>${WARNING_TIME}s)${NC}"
        return 1
    fi
}

# Test individual components
test_components() {
    local staged_files_list="$1"
    local results=()
    
    echo ""
    echo -e "${BLUE}🧪 Testing Individual Components${NC}"
    
    # Test fast logging checker
    if validate_performance "STAGED_FILES_LIST='$staged_files_list' '$SCRIPT_DIR/fast-logging-check.sh'" "Fast Logging Check"; then
        results+=("logging:PASS")
    else
        results+=("logging:FAIL")
    fi
    
    # Test fast clang-tidy
    if validate_performance "CLANG_TIDY_FAST=true '$SCRIPT_DIR/fast-clang-tidy.sh'" "Fast Clang-Tidy"; then
        results+=("clang-tidy:PASS")
    else
        results+=("clang-tidy:FAIL")
    fi
    
    # Test integrated dispatcher
    if validate_performance "'$SCRIPT_DIR/fast-precommit-dispatcher.sh'" "Fast Dispatcher (Parallel)"; then
        results+=("dispatcher:PASS")
    else
        results+=("dispatcher:FAIL")
    fi
    
    echo "${results[@]}"
}

# Main validation
main() {
    cd "$PROJECT_ROOT"
    
    echo "🔧 Checking system requirements..."
    check_dependencies
    
    # Check for staged files
    STAGED_FILES=$(get_staged_files)
    
    if [[ -z "$STAGED_FILES" ]]; then
        echo ""
        echo "❌ No staged C++ files found for validation"
        echo ""
        echo "To run validation, stage some C++ files first:"
        echo "  git add src/some_file.cpp"
        echo "  ./scripts/pre-commit/validate-optimization.sh"
        exit 1
    fi
    
    local file_count=$(echo "$STAGED_FILES" | wc -l)
    echo ""
    echo "📁 Validating with $file_count staged C++ files:"
    echo "$STAGED_FILES" | while read -r file; do
        echo "  - $file"
    done
    
    # Create temporary file for staged files list
    STAGED_FILES_LIST=$(mktemp)
    echo "$STAGED_FILES" > "$STAGED_FILES_LIST"
    
    # Cleanup on exit
    trap "rm -f '$STAGED_FILES_LIST'" EXIT
    
    # Run component tests
    local test_results=($(test_components "$STAGED_FILES_LIST"))
    
    # Analyze results
    echo ""
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${BLUE}                              VALIDATION RESULTS                            ${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    
    local passed=0
    local failed=0
    
    echo ""
    echo "📊 Component Results:"
    for result in "${test_results[@]}"; do
        local component=$(echo "$result" | cut -d: -f1)
        local status=$(echo "$result" | cut -d: -f2)
        
        if [[ "$status" == "PASS" ]]; then
            echo -e "  ${GREEN}✅ $component: PASSED${NC}"
            ((passed++))
        else
            echo -e "  ${RED}❌ $component: FAILED${NC}"
            ((failed++))
        fi
    done
    
    echo ""
    echo "📈 Summary:"
    echo "  Files tested: $file_count"
    echo "  Components passed: $passed"
    echo "  Components failed: $failed"
    echo "  Performance target: <${TARGET_TIME} seconds"
    
    # Overall assessment
    echo ""
    if [[ $failed -eq 0 ]]; then
        echo -e "${GREEN}🎉 OPTIMIZATION VALIDATION SUCCESSFUL${NC}"
        echo ""
        echo "✅ All components meet performance targets"
        echo "✅ System is ready for production use"
        echo ""
        echo "🚀 Next steps:"
        echo "  1. The optimized system is already configured in .pre-commit-config.yaml"
        echo "  2. Test with a real commit: git commit"
        echo "  3. Monitor performance in daily use"
        
        exit 0
    else
        echo -e "${RED}❌ OPTIMIZATION VALIDATION FAILED${NC}"
        echo ""
        echo "⚠️  Some components failed performance targets"
        echo ""
        echo "🔧 Recommended actions:"
        if [[ " ${test_results[*]} " =~ " logging:FAIL " ]]; then
            echo "  - Optimize logging checker further"
            echo "  - Consider reducing pattern complexity"
        fi
        if [[ " ${test_results[*]} " =~ " clang-tidy:FAIL " ]]; then
            echo "  - Enable parallel clang-tidy analysis"
            echo "  - Reduce check set in fast mode"
            echo "  - Consider moving to CI/CD only"
        fi
        if [[ " ${test_results[*]} " =~ " dispatcher:FAIL " ]]; then
            echo "  - Debug parallel execution issues"
            echo "  - Check for resource contention"
        fi
        
        echo ""
        echo "🔄 Revert to original system if needed:"
        echo "  git checkout .pre-commit-config.yaml"
        
        exit 1
    fi
}

# Run main function
main "$@"
