#!/bin/bash
# scripts/pre-commit/generate-baseline.sh
# Generate baseline violation report for tracking improvement progress
#
# This script creates a snapshot of current violations to track progress
# over time without blocking development on existing issues.

set -e

echo "📊 Generating quality baseline for Vizion3D..."

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
BASELINE_DIR="$PROJECT_ROOT/.quality-baseline"

# Create baseline directory
mkdir -p "$BASELINE_DIR"

# Generate timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BASELINE_FILE="$BASELINE_DIR/baseline_$TIMESTAMP.json"

echo "📁 Baseline will be saved to: $BASELINE_FILE"

# Function to run a check and capture results
capture_violations() {
    local check_name="$1"
    local check_script="$2"
    shift 2

    echo "🔍 Capturing $check_name violations..."

    # Run check and capture output (ignore exit code for baseline)
    local output
    output=$("$check_script" "$@" 2>&1 || true)

    # Count violations
    local violation_count
    violation_count=$(echo "$output" | grep -c "❌" || echo "0")

    echo "  Found $violation_count violations"

    # Return structured data
    cat << EOF
{
  "check_name": "$check_name",
  "violation_count": $violation_count,
  "timestamp": "$(date -Iseconds)",
  "details": $(echo "$output" | jq -Rs .)
}
EOF
}

# Start JSON structure
cat > "$BASELINE_FILE" << EOF
{
  "baseline_info": {
    "timestamp": "$(date -Iseconds)",
    "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "git_branch": "$(git branch --show-current 2>/dev/null || echo 'unknown')",
    "project_root": "$PROJECT_ROOT"
  },
  "violations": [
EOF

# Capture logging violations
LOGGING_RESULT=$(capture_violations "Logging Pattern Compliance" "$SCRIPT_DIR/check-logging.sh")
echo "$LOGGING_RESULT" >> "$BASELINE_FILE"

echo "," >> "$BASELINE_FILE"

# Capture Result<T> pattern violations
RESULT_RESULT=$(capture_violations "Result<T> Pattern Enforcement" python3 "$SCRIPT_DIR/check-result-pattern.py")
echo "$RESULT_RESULT" >> "$BASELINE_FILE"

# Close JSON structure
cat >> "$BASELINE_FILE" << EOF
  ],
  "summary": {
    "total_checks": 2,
    "total_violations": $(echo "$LOGGING_RESULT" | jq '.violation_count') + $(echo "$RESULT_RESULT" | jq '.violation_count'),
    "baseline_created": "$(date -Iseconds)"
  }
}
EOF

# Create symlink to latest baseline
ln -sf "baseline_$TIMESTAMP.json" "$BASELINE_DIR/latest.json"

# Generate human-readable summary
SUMMARY_FILE="$BASELINE_DIR/summary_$TIMESTAMP.txt"
cat > "$SUMMARY_FILE" << EOF
Vizion3D Quality Baseline Summary
Generated: $(date)
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo 'unknown')
Git Branch: $(git branch --show-current 2>/dev/null || echo 'unknown')

=== VIOLATION SUMMARY ===

Logging Pattern Compliance:
  Violations: $(echo "$LOGGING_RESULT" | jq '.violation_count')

Result<T> Pattern Enforcement:
  Violations: $(echo "$RESULT_RESULT" | jq '.violation_count')

Total Violations: $(($(echo "$LOGGING_RESULT" | jq '.violation_count') + $(echo "$RESULT_RESULT" | jq '.violation_count')))

=== NEXT STEPS ===

1. Use this baseline to track improvement progress
2. Run ./scripts/pre-commit/check-progress.sh to compare against baseline
3. Focus on fixing Priority 1 violations first:
   - Missing Result<T> includes
   - Try/catch without Result<T>
   - Critical qDebug/qWarning calls

4. Set up quality checks with gradual enforcement:
   ./scripts/setup-quality-checks.sh

=== FILES ===

Detailed baseline: $BASELINE_FILE
Summary report: $SUMMARY_FILE
Latest baseline: $BASELINE_DIR/latest.json

EOF

ln -sf "summary_$TIMESTAMP.txt" "$BASELINE_DIR/latest-summary.txt"

echo ""
echo "✅ Baseline generation complete!"
echo ""
echo "📊 Summary:"
echo "  - Logging violations: $(echo "$LOGGING_RESULT" | jq '.violation_count')"
echo "  - Result<T> violations: $(echo "$RESULT_RESULT" | jq '.violation_count')"
echo "  - Total violations: $(($(echo "$LOGGING_RESULT" | jq '.violation_count') + $(echo "$RESULT_RESULT" | jq '.violation_count')))"
echo ""
echo "📁 Files created:"
echo "  - Detailed: $BASELINE_FILE"
echo "  - Summary: $SUMMARY_FILE"
echo "  - Latest: $BASELINE_DIR/latest.json"
echo ""
echo "🎯 Next steps:"
echo "  1. Review summary: cat $BASELINE_DIR/latest-summary.txt"
echo "  2. Set up quality checks: ./scripts/setup-quality-checks.sh"
echo "  3. Track progress: ./scripts/pre-commit/check-progress.sh"
