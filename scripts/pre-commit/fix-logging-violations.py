#!/usr/bin/env python3
"""
Vizion3D Comprehensive Logging Pattern Fix Tool

This script consolidates all logging violation fixes into a single, comprehensive tool.
It replaces the previous three separate scripts:
- fix_logging_violations.py (root level)
- scripts/pre-commit/fix-complex-logging.py
- scripts/pre-commit/fix-logging.sh

Features:
- Respects infrastructure exceptions using enhanced check-logging.sh logic
- Handles both simple and complex logging patterns
- Supports dry-run, backup, and compilation verification
- Provides comprehensive pattern matching and conversion
- Single source of truth for all logging fixes
"""

import os
import re
import sys
import argparse
import subprocess
import shutil
import tempfile
from pathlib import Path

class LoggingViolationFixer:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.script_dir = self.project_root / "scripts" / "pre-commit"
        self.check_script = self.script_dir / "check-logging.sh"

        # Infrastructure files that have legitimate fprintf usage
        self.infrastructure_files = [
            "src/utils/logger.cpp",
            "src/utils/log_config.cpp"
        ]

        # Valid VLOG categories
        self.valid_categories = [
            "GCode", "Engine", "UI", "OpenGL", "FileIO", "System", "Threading", "Validation"
        ]

    def determine_category(self, file_path):
        """Determine the appropriate VLOG category based on file path"""
        relative_path = str(file_path.relative_to(self.project_root))

        # Path-based category assignment
        if relative_path.startswith("src/ui/"):
            return "UI"
        elif relative_path.startswith("src/engine/"):
            return "Engine"
        elif relative_path.startswith("src/utils/"):
            return "System"
        elif any(keyword in relative_path.lower() for keyword in ["opengl", "render", "gl"]):
            return "OpenGL"
        elif "gcode" in relative_path.lower():
            return "GCode"
        else:
            return "System"

    def is_infrastructure_exception(self, file_path, line_number, line_content):
        """
        Check if a logging violation is a legitimate infrastructure exception.
        Uses the same logic as the enhanced check-logging.sh script.
        """
        try:
            # Read the file to get context
            with open(file_path, 'r') as f:
                lines = f.readlines()

            if line_number >= len(lines):
                return False

            # Get context around the violation (±5 lines)
            start_line = max(0, line_number - 5)
            end_line = min(len(lines), line_number + 6)
            context_lines = lines[start_line:end_line]
            context = ''.join(context_lines).lower()
            current_line = lines[line_number].lower()

            # Check for exception handler context
            if 'catch' in context:
                return True

            # Check for test mode context
            if '#ifdef qt_testlib' in context or '#ifndef qt_testlib' in context:
                return True

            # Check for constructor context (high recursion risk)
            if 'logconfig::logconfig' in context or 'logger::logger' in context:
                return True

            # Check for infrastructure files
            relative_path = str(file_path.relative_to(self.project_root))
            if relative_path in self.infrastructure_files:
                # Check for initialization context
                if '!m_initialized' in context or 'm_initialized.*false' in context:
                    return True

                # Check for initializeWithoutMessageHandler context
                if 'initializewithoutmessagehandler' in context or 'starting logger initialization' in context:
                    return True

                # Check for explicit !m_initialized checks
                if 'logger not initialized' in current_line or '!m_initialized' in current_line:
                    return True

                # Check for circular dependency prevention
                if any(word in context for word in ['circular', 'recursion', 'recursive', 'isapplyingconfiguration']):
                    return True

                # Allow fflush in infrastructure files
                if 'fflush(' in current_line:
                    return True

            # Check for main.cpp critical initialization messages
            if relative_path == "src/main.cpp":
                critical_messages = [
                    'initializing logconfig',
                    'logconfig initialized',
                    'setting category log levels',
                    'category log levels set',
                    'installing qt message handler',
                    'qt message handler installed'
                ]

                for msg in critical_messages:
                    if msg in current_line:
                        return True

                # Allow fflush after critical initialization
                if 'fflush(' in current_line:
                    return True

            return False

        except Exception:
            # If we can't analyze the context, err on the side of caution
            return True

    def convert_fprintf_format_string(self, format_str, args):
        """Convert C-style format string to QString format with .arg() calls"""
        if not args:
            return format_str, ""

        # Convert C format specifiers to Qt format specifiers
        converted_format = format_str
        arg_index = 1

        # Common C format specifiers
        c_specifiers = [r'%s', r'%d', r'%i', r'%f', r'%g', r'%e', r'%c', r'%x', r'%X', r'%o', r'%u']

        for spec in c_specifiers:
            while spec in converted_format:
                converted_format = converted_format.replace(spec, f'%{arg_index}', 1)
                arg_index += 1

        # Build .arg() chain
        arg_chain = ""
        for arg in args:
            arg_chain += f".arg({arg.strip()})"

        return converted_format, arg_chain

    def parse_qdebug_stream(self, stream_content):
        """Parse a qDebug() stream operation and extract text and variable parts"""
        parts = []
        current_part = ""
        in_quotes = False
        paren_depth = 0
        i = 0

        while i < len(stream_content):
            char = stream_content[i]

            if char == '"' and (i == 0 or stream_content[i-1] != '\\'):
                in_quotes = not in_quotes
                current_part += char
            elif char == '(' and not in_quotes:
                paren_depth += 1
                current_part += char
            elif char == ')' and not in_quotes:
                paren_depth -= 1
                current_part += char
            elif char == '<' and not in_quotes and paren_depth == 0:
                # Check if this is a << operator
                if i + 1 < len(stream_content) and stream_content[i + 1] == '<':
                    # End current part and start new one
                    if current_part.strip():
                        parts.append(current_part.strip())
                    current_part = ""
                    i += 1  # Skip the second <
                else:
                    current_part += char
            else:
                current_part += char

            i += 1

        # Add the last part
        if current_part.strip():
            parts.append(current_part.strip())

        return parts

    def convert_qdebug_to_vlog(self, parts, category):
        """Convert parsed qDebug() parts to VLOG_DEBUG format"""
        if not parts:
            return None

        # Check if it's a simple text-only case
        if len(parts) == 1 and parts[0].startswith('"') and parts[0].endswith('"'):
            text = parts[0][1:-1]  # Remove quotes
            return f'VLOG_DEBUG("{category}", "{text}");'

        # Build format string and collect variables
        format_str = ""
        var_parts = []
        var_index = 1

        for i, part in enumerate(parts):
            if part.startswith('"') and part.endswith('"'):
                # Text part - remove quotes and add to format string
                text = part[1:-1]
                format_str += text
            else:
                # Variable part - add placeholder to format string and collect variable
                if i > 0 and format_str and not format_str.endswith(' ') and not format_str.endswith('('):
                    format_str += " "
                format_str += f"%{var_index}"
                var_parts.append(part)
                var_index += 1

        # Build the arg chain
        if var_parts:
            arg_chain = ""
            for var in var_parts:
                arg_chain += f".arg({var})"
            return f'VLOG_DEBUG("{category}", QString("{format_str}"){arg_chain});'
        else:
            # No variables, just text
            return f'VLOG_DEBUG("{category}", "{format_str}");'

    def fix_qt_logging_patterns(self, content, category):
        """Fix Qt logging patterns (qDebug, qWarning, qCritical)"""
        fixes = 0

        # Fix complex qDebug().nospace() patterns
        nospace_pattern = r'qDebug\(\)\.nospace\(\)\s*<<\s*(.+?);'
        def replace_nospace(match):
            nonlocal fixes
            stream_content = match.group(1)
            parts = self.parse_qdebug_stream(stream_content)
            result = self.convert_qdebug_to_vlog(parts, category)
            if result:
                fixes += 1
                return result
            return match.group(0)

        content = re.sub(nospace_pattern, replace_nospace, content, flags=re.DOTALL)

        # Fix regular qDebug() patterns
        regular_pattern = r'qDebug\(\)\s*<<\s*(.+?);'
        def replace_regular(match):
            nonlocal fixes
            stream_content = match.group(1)
            parts = self.parse_qdebug_stream(stream_content)
            result = self.convert_qdebug_to_vlog(parts, category)
            if result:
                fixes += 1
                return result
            return match.group(0)

        content = re.sub(regular_pattern, replace_regular, content, flags=re.DOTALL)

        # Fix qWarning() patterns
        content = re.sub(r'qWarning\(\)\s*<<\s*"([^"]*?)"\s*<<\s*([^;]+?);',
                        rf'VLOG_WARNING("{category}", QString("\1%1").arg(\2));', content)
        content = re.sub(r'qWarning\(\)\s*<<\s*"([^"]*?)"\s*;',
                        rf'VLOG_WARNING("{category}", "\1");', content)

        # Fix qCritical() patterns
        content = re.sub(r'qCritical\(\)\s*<<\s*"([^"]*?)"\s*<<\s*([^;]+?);',
                        rf'VLOG_ERROR("{category}", QString("\1%1").arg(\2));', content)
        content = re.sub(r'qCritical\(\)\s*<<\s*"([^"]*?)"\s*;',
                        rf'VLOG_ERROR("{category}", "\1");', content)

        return content, fixes

    def fix_fprintf_patterns(self, file_path, content, category):
        """Fix fprintf() patterns while respecting infrastructure exceptions"""
        fixes = 0
        lines = content.split('\n')

        for i, line in enumerate(lines):
            # Skip if this is an infrastructure exception
            if self.is_infrastructure_exception(file_path, i, line):
                continue

            # Pattern 1: fprintf(stdout, "simple message");
            pattern1 = r'fprintf\s*\(\s*stdout\s*,\s*"([^"]*?)"\s*\)\s*;'
            match1 = re.search(pattern1, line)
            if match1:
                message = match1.group(1).rstrip('\\n')
                replacement = f'VLOG_INFO("{category}", "{message}");'
                lines[i] = re.sub(pattern1, replacement, line)
                fixes += 1
                continue

            # Pattern 2: fprintf(stderr, "simple message");
            pattern2 = r'fprintf\s*\(\s*stderr\s*,\s*"([^"]*?)"\s*\)\s*;'
            match2 = re.search(pattern2, line)
            if match2:
                message = match2.group(1).rstrip('\\n')
                replacement = f'VLOG_ERROR("{category}", "{message}");'
                lines[i] = re.sub(pattern2, replacement, line)
                fixes += 1
                continue

            # Pattern 3: fprintf(stdout, "format %s %d", var1, var2);
            pattern3 = r'fprintf\s*\(\s*stdout\s*,\s*"([^"]*?)"\s*,\s*([^)]+?)\)\s*;'
            match3 = re.search(pattern3, line)
            if match3:
                format_str = match3.group(1).rstrip('\\n')
                args_str = match3.group(2)
                args = [arg.strip() for arg in args_str.split(',')]

                converted_format, arg_chain = self.convert_fprintf_format_string(format_str, args)
                replacement = f'VLOG_INFO("{category}", QString("{converted_format}"){arg_chain});'
                lines[i] = re.sub(pattern3, replacement, line)
                fixes += 1
                continue

            # Pattern 4: fprintf(stderr, "format %s %d", var1, var2);
            pattern4 = r'fprintf\s*\(\s*stderr\s*,\s*"([^"]*?)"\s*,\s*([^)]+?)\)\s*;'
            match4 = re.search(pattern4, line)
            if match4:
                format_str = match4.group(1).rstrip('\\n')
                args_str = match4.group(2)
                args = [arg.strip() for arg in args_str.split(',')]

                converted_format, arg_chain = self.convert_fprintf_format_string(format_str, args)
                replacement = f'VLOG_ERROR("{category}", QString("{converted_format}"){arg_chain});'
                lines[i] = re.sub(pattern4, replacement, line)
                fixes += 1
                continue

        return '\n'.join(lines), fixes

    def fix_stream_patterns(self, content, category):
        """Fix std::cout and std::cerr patterns"""
        fixes = 0

        # Fix std::cout patterns
        original_cout_count = len(re.findall(r'std::cout', content))
        content = re.sub(r'std::cout\s*<<\s*"([^"]*?)"\s*;', rf'VLOG_INFO("{category}", "\1");', content)
        content = re.sub(r'std::cout\s*<<\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*;', rf'VLOG_INFO("{category}", QString::number(\1));', content)
        new_cout_count = len(re.findall(r'std::cout', content))
        fixes += original_cout_count - new_cout_count

        # Fix std::cerr patterns
        original_cerr_count = len(re.findall(r'std::cerr', content))
        content = re.sub(r'std::cerr\s*<<\s*"([^"]*?)"\s*;', rf'VLOG_ERROR("{category}", "\1");', content)
        content = re.sub(r'std::cerr\s*<<\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*;', rf'VLOG_ERROR("{category}", QString::number(\1));', content)
        new_cerr_count = len(re.findall(r'std::cerr', content))
        fixes += original_cerr_count - new_cerr_count

        return content, fixes

    def ensure_logger_include(self, content, file_path):
        """Ensure that utils/logger.h is included in files that use VLOG_* macros"""
        # Only process .cpp and .h files
        if not (str(file_path).endswith('.cpp') or str(file_path).endswith('.h')):
            return content, False

        # Check if VLOG_* macros are used
        if not re.search(r'VLOG_', content):
            return content, False

        # Check if logger.h is already included
        if '#include "utils/logger.h"' in content:
            return content, False

        # Find the first #include statement and add logger.h after it
        lines = content.split('\n')
        include_added = False

        for i, line in enumerate(lines):
            if line.strip().startswith('#include "') and not include_added:
                # Find the last local include (with quotes)
                last_local_include = i
                for j in range(i + 1, len(lines)):
                    if lines[j].strip().startswith('#include "'):
                        last_local_include = j
                    elif lines[j].strip().startswith('#include <') or not lines[j].strip().startswith('#include'):
                        break

                # Insert logger.h after the last local include
                lines.insert(last_local_include + 1, '#include "utils/logger.h"')
                include_added = True
                break

        if include_added:
            return '\n'.join(lines), True
        else:
            return content, False

    def process_file(self, file_path, dry_run=True, create_backup=False):
        """Process a single file to fix logging violations"""
        try:
            with open(file_path, 'r') as f:
                original_content = f.read()

            content = original_content
            total_fixes = 0
            category = self.determine_category(file_path)

            print(f"📁 Processing: {file_path.relative_to(self.project_root)}")
            print(f"    📂 Category: {category}")

            # Create backup if requested
            if create_backup and not dry_run:
                backup_path = file_path.with_suffix(file_path.suffix + '.bak')
                shutil.copy2(file_path, backup_path)
                print(f"    💾 Created backup: {backup_path.name}")

            # Apply Qt logging fixes
            content, qt_fixes = self.fix_qt_logging_patterns(content, category)
            total_fixes += qt_fixes
            if qt_fixes > 0:
                print(f"    🔧 Fixed {qt_fixes} Qt logging patterns")

            # Apply fprintf fixes
            content, fprintf_fixes = self.fix_fprintf_patterns(file_path, content, category)
            total_fixes += fprintf_fixes
            if fprintf_fixes > 0:
                print(f"    🔧 Fixed {fprintf_fixes} fprintf patterns")

            # Apply stream output fixes
            content, stream_fixes = self.fix_stream_patterns(content, category)
            total_fixes += stream_fixes
            if stream_fixes > 0:
                print(f"    🔧 Fixed {stream_fixes} stream output patterns")

            # Ensure logger include is present if VLOG_* macros are used
            content, include_added = self.ensure_logger_include(content, file_path)
            if include_added:
                print(f"    ✅ Added #include \"utils/logger.h\"")
                total_fixes += 1

            # Apply changes or show what would be changed
            if total_fixes > 0:
                if not dry_run:
                    with open(file_path, 'w') as f:
                        f.write(content)
                    print(f"    ✅ Applied {total_fixes} fixes")
                else:
                    print(f"    📝 Would apply {total_fixes} fixes")
            else:
                print(f"    ℹ️  No violations found to fix")

            return total_fixes

        except Exception as e:
            print(f"    ❌ Error processing file: {e}")
            return 0

    def get_files_to_process(self, file_patterns=None):
        """Get list of files to process"""
        if file_patterns:
            files = []
            for pattern in file_patterns:
                if '*' in pattern:
                    files.extend(self.project_root.glob(pattern))
                else:
                    file_path = self.project_root / pattern
                    if file_path.exists():
                        files.append(file_path)
            return files
        else:
            # Get all .cpp files in src directory
            return list(self.project_root.glob("src/**/*.cpp"))

    def verify_compilation(self):
        """Verify that the project compiles after fixes"""
        print("🔨 Verifying compilation...")

        # Try to build the project
        if (self.project_root / "CMakeLists.txt").exists():
            print("    Using CMake build system...")
            try:
                result = subprocess.run(['cmake', '.'], cwd=self.project_root,
                                      capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    result = subprocess.run(['make', '-j4'], cwd=self.project_root,
                                          capture_output=True, text=True, timeout=300)
                    if result.returncode == 0:
                        print("    ✅ Compilation successful")
                        return True
                    else:
                        print("    ❌ Compilation failed")
                        print(f"    Error: {result.stderr}")
                        return False
                else:
                    print("    ❌ CMake configuration failed")
                    return False
            except subprocess.TimeoutExpired:
                print("    ❌ Compilation timed out")
                return False
        else:
            print("    ⚠️  No CMakeLists.txt found, skipping compilation verification")
            return True


def main():
    parser = argparse.ArgumentParser(description='Comprehensive Logging Pattern Fix Tool')
    parser.add_argument('--apply', action='store_true', help='Apply fixes (default is dry-run)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be changed (default)')
    parser.add_argument('--backup', action='store_true', help='Create backup files before modification')
    parser.add_argument('--verify', action='store_true', help='Verify compilation after applying fixes')
    parser.add_argument('--files', nargs='*', help='Specific files or patterns to process')
    parser.add_argument('--category', help='Override automatic category assignment')

    args = parser.parse_args()

    # Determine if we're in dry-run mode
    dry_run = not args.apply or args.dry_run

    # Find project root
    script_path = Path(__file__).resolve()
    project_root = script_path.parent.parent.parent

    print("🔧 Vizion3D Comprehensive Logging Fix Tool")
    if dry_run:
        print("   Running in DRY-RUN mode (use --apply to make changes)")
    else:
        print("   Running in APPLY mode - files will be modified")
    print()

    # Initialize the fixer
    fixer = LoggingViolationFixer(project_root)

    # Override category if specified
    if args.category:
        fixer.determine_category = lambda _: args.category

    # Get files to process
    files_to_process = fixer.get_files_to_process(args.files)

    if not files_to_process:
        print("❌ No files found to process")
        return 1

    print(f"📊 Found {len(files_to_process)} files to process")
    print()

    # Process files
    total_fixes = 0
    files_with_fixes = 0

    for file_path in files_to_process:
        if file_path.is_file():
            fixes = fixer.process_file(file_path, dry_run, args.backup)
            total_fixes += fixes
            if fixes > 0:
                files_with_fixes += 1
            print()

    # Final reporting
    print("📋 PROCESSING SUMMARY:")
    print(f"   Files processed: {len(files_to_process)}")
    print(f"   Files with fixes: {files_with_fixes}")
    print(f"   Total fixes applied: {total_fixes}")

    if dry_run:
        print()
        print("🔍 DRY-RUN COMPLETE")
        print("   No files were modified (use --apply to make changes)")
    else:
        print()
        print("✅ FIXES APPLIED")

        # Verify compilation if requested
        if args.verify and total_fixes > 0:
            print()
            if fixer.verify_compilation():
                print("✅ All fixes applied successfully and code compiles")
            else:
                print("❌ Compilation failed after applying fixes")
                print("   Please review the changes and fix any compilation errors")
                return 1

        # Suggest next steps
        if total_fixes > 0:
            print()
            print("💡 Next steps:")
            print("   1. Review the applied changes")
            print("   2. Run ./scripts/pre-commit/check-logging.sh to verify violation reduction")
            print("   3. Test your application to ensure functionality is preserved")
            print("   4. Commit the changes")

    print()
    if total_fixes > 0:
        print(f"🎉 Successfully processed {total_fixes} logging violations!")
    else:
        print("ℹ️  No violations found to fix")
        print("   All logging patterns may already be compliant")

    return 0


if __name__ == '__main__':
    sys.exit(main())
