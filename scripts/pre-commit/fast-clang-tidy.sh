#!/bin/bash
# scripts/pre-commit/fast-clang-tidy.sh
# Optimized Clang-Tidy Runner with Caching and Parallel Processing
#
# This script runs clang-tidy analysis with performance optimizations:
# - Cached build directory detection
# - Parallel file analysis
# - Optimized clang-tidy arguments
# - Fast mode for reduced check set

set -e

# Performance configuration
MAX_PARALLEL_JOBS=${CLANG_TIDY_JOBS:-$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 2)}
ENABLE_PARALLEL=${ENABLE_PARALLEL:-false}
FAST_MODE=${CLANG_TIDY_FAST:-true}  # Default to fast mode for pre-commit

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Cache file for build directory
BUILD_CACHE_FILE="$PROJECT_ROOT/.clang-tidy-build-cache"

# Check for timeout command (GNU coreutils)
TIMEOUT_CMD=""
if command -v timeout > /dev/null; then
    TIMEOUT_CMD="timeout"
elif command -v gtimeout > /dev/null; then
    TIMEOUT_CMD="gtimeout"
else
    echo "❌ timeout command not found. Install with: brew install coreutils"
    exit 1
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Get staged files (optimized)
get_staged_files() {
    if git rev-parse --git-dir > /dev/null 2>&1; then
        git diff --cached --name-only --diff-filter=ACM 2>/dev/null | \
            grep -E '\.(cpp|cxx|cc|c\+\+|h|hpp)$' | \
            grep -v -E '(build/|.*_autogen/|ui_.*\.h$|moc_.*\.cpp$|qrc_.*\.cpp$)' || true
    fi
}

# Cached build directory detection
get_build_directory() {
    # Check cache first
    if [[ -f "$BUILD_CACHE_FILE" ]]; then
        local cached_dir=$(cat "$BUILD_CACHE_FILE")
        if [[ -d "$cached_dir" && -f "$cached_dir/compile_commands.json" ]]; then
            echo "$cached_dir"
            return 0
        fi
        # Cache is stale, remove it
        rm -f "$BUILD_CACHE_FILE"
    fi
    
    # Search for build directory
    local build_dirs=("build" "build-debug" "cmake-build-debug" "cmake-build-release")
    
    for build_dir in "${build_dirs[@]}"; do
        local full_path="$PROJECT_ROOT/$build_dir"
        if [[ -d "$full_path" && -f "$full_path/compile_commands.json" ]]; then
            # Cache the result
            echo "$full_path" > "$BUILD_CACHE_FILE"
            echo "$full_path"
            return 0
        fi
    done
    
    return 1
}

# Optimized clang-tidy arguments
get_clang_tidy_args() {
    local args=""
    
    if [[ "$FAST_MODE" == "true" ]]; then
        # Fast mode: reduced check set for performance
        args="--checks=-*,bugprone-*,clang-analyzer-*,readability-identifier-naming,modernize-use-nullptr,modernize-use-override"
    else
        # Full mode: use project .clang-tidy config
        args=""
    fi
    
    # Optimize header filtering
    args="$args --header-filter='^(?!.*/build/)(?!.*moc_)(?!.*ui_).*\.(h|hpp)$'"
    
    # Performance optimizations
    args="$args --use-color=false --quiet"
    
    echo "$args"
}

# Fast single file analysis
analyze_file_fast() {
    local file="$1"
    local build_dir="$2"
    local temp_output=$(mktemp)
    
    # Get optimized clang-tidy arguments
    local clang_tidy_args=$(get_clang_tidy_args)
    
    # Run clang-tidy with timeout
    local exit_code=0
    $TIMEOUT_CMD 30s clang-tidy -p "$build_dir" $clang_tidy_args "$file" > "$temp_output" 2>&1 || exit_code=$?
    
    # Check for issues
    if [[ $exit_code -eq 124 ]]; then
        echo "⏰ TIMEOUT: $file (>30s)"
        rm -f "$temp_output"
        return 1
    elif grep -q -E "(warning:|error:)" "$temp_output"; then
        echo "❌ ISSUES: $(basename "$file")"
        # Show first few issues only for speed
        grep -E "(warning:|error:)" "$temp_output" | head -3 | while read -r line; do
            echo "  $line"
        done
        local issue_count=$(grep -c -E "(warning:|error:)" "$temp_output")
        if [[ $issue_count -gt 3 ]]; then
            echo "  ... and $((issue_count - 3)) more issues"
        fi
        rm -f "$temp_output"
        return 1
    elif [[ $exit_code -ne 0 ]]; then
        echo "❌ ERROR: $(basename "$file") (exit code: $exit_code)"
        rm -f "$temp_output"
        return 1
    else
        echo "✅ CLEAN: $(basename "$file")"
        rm -f "$temp_output"
        return 0
    fi
}

# Parallel analysis
analyze_files_parallel() {
    local build_dir="$1"
    shift
    local files=("$@")
    
    local pids=()
    local temp_dir=$(mktemp -d)
    local job_count=0
    
    echo "🔧 Running parallel analysis (max $MAX_PARALLEL_JOBS jobs)..."
    
    for file in "${files[@]}"; do
        if [[ -f "$file" ]]; then
            # Wait if we've reached max parallel jobs
            while [[ ${#pids[@]} -ge $MAX_PARALLEL_JOBS ]]; do
                for i in "${!pids[@]}"; do
                    if ! kill -0 "${pids[$i]}" 2>/dev/null; then
                        wait "${pids[$i]}"
                        unset pids[$i]
                    fi
                done
                pids=("${pids[@]}")  # Reindex array
                sleep 0.1
            done
            
            # Start new job
            ((job_count++))
            (
                analyze_file_fast "$file" "$build_dir"
                echo $? > "$temp_dir/job_$job_count.result"
            ) &
            pids+=($!)
        fi
    done
    
    # Wait for all jobs
    for pid in "${pids[@]}"; do
        wait "$pid" 2>/dev/null || true
    done
    
    # Count failures
    local failed_files=0
    for result_file in "$temp_dir"/job_*.result; do
        if [[ -f "$result_file" ]]; then
            local exit_code=$(cat "$result_file")
            if [[ $exit_code -ne 0 ]]; then
                ((failed_files++))
            fi
        fi
    done
    
    rm -rf "$temp_dir"
    echo "$failed_files"
}

# Sequential analysis (for better output control)
analyze_files_sequential() {
    local build_dir="$1"
    shift
    local files=("$@")
    
    local failed_files=0
    local file_num=0
    local total_files=${#files[@]}
    
    echo "🔧 Running sequential analysis..."
    
    for file in "${files[@]}"; do
        if [[ -f "$file" ]]; then
            ((file_num++))
            echo "[$file_num/$total_files] $(basename "$file")..."
            
            if ! analyze_file_fast "$file" "$build_dir"; then
                ((failed_files++))
            fi
        fi
    done
    
    echo "$failed_files"
}

# Main execution
main() {
    cd "$PROJECT_ROOT"
    
    # Get staged files
    local staged_files=$(get_staged_files)
    
    if [[ -z "$staged_files" ]]; then
        echo "✅ No C++ files staged for commit"
        exit 0
    fi
    
    # Convert to array
    local files_array=()
    while IFS= read -r line; do
        files_array+=("$line")
    done <<< "$staged_files"
    local file_count=${#files_array[@]}
    
    echo "🔍 Fast clang-tidy analysis on $file_count files..."
    
    # Check for build directory (cached)
    if ! build_dir=$(get_build_directory); then
        echo ""
        echo "❌ No build directory with compile_commands.json found"
        echo ""
        echo "To fix: mkdir build && cd build && cmake .. && cmake --build ."
        echo "To bypass: git commit --no-verify"
        exit 1
    fi
    
    echo "📁 Using build directory: $(basename "$build_dir")"
    
    # Run analysis
    local failed_files
    if [[ "$ENABLE_PARALLEL" == "true" && $file_count -gt 1 ]]; then
        failed_files=$(analyze_files_parallel "$build_dir" "${files_array[@]}")
    else
        failed_files=$(analyze_files_sequential "$build_dir" "${files_array[@]}")
    fi
    
    # Results
    local clean_files=$((file_count - failed_files))
    
    echo ""
    echo "📊 Analysis Summary:"
    echo "  Files analyzed: $file_count"
    echo "  Clean files: $clean_files"
    echo "  Files with issues: $failed_files"
    
    if [[ $failed_files -gt 0 ]]; then
        echo ""
        echo "❌ Clang-tidy found issues in $failed_files file(s)"
        echo ""
        echo "💡 To fix:"
        echo "  1. Review the issues shown above"
        echo "  2. Fix the code issues"
        echo "  3. Re-stage files: git add <files>"
        echo "  4. Commit again: git commit"
        exit 1
    else
        echo ""
        echo "✅ All files passed clang-tidy analysis"
        exit 0
    fi
}

# Check dependencies
if ! command -v clang-tidy > /dev/null; then
    echo "❌ clang-tidy not found. Install with: brew install llvm"
    exit 1
fi

# Run main function
main "$@"
