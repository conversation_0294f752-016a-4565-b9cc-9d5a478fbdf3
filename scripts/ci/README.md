# Vizion3D CI/CD Scripts

This directory contains scripts for Continuous Integration and Continuous Deployment (CI/CD) workflows.

## Scripts

### verify-app-launch.sh

Application launch verification script for CI/CD environments.

**Purpose**: Verifies that the Vizion3D application can start successfully in headless CI environments.

**Usage**:
```bash
./verify-app-launch.sh <executable_path> [timeout_seconds]
```

**Examples**:
```bash
# Basic usage
./verify-app-launch.sh ./build/Vizion3D

# With custom timeout
./verify-app-launch.sh ./build/Release/Vizion3D.exe 45

# For CI environments
./verify-app-launch.sh ./build/bin/Vizion3D 30
```

**Features**:
- Cross-platform support (macOS, Windows, Linux)
- Virtual display setup for headless GUI testing
- Comprehensive log analysis for startup verification
- Proper process cleanup and timeout handling
- Detailed error reporting and diagnostics
- **Performance optimized**: Fast execution with proper timeout handling
- **CI/CD ready**: Designed for GitHub Actions and other CI systems

**Success Criteria**:
The script checks for these startup indicators:
1. Logger initialization: "Logger initialized"
2. MainWindow creation: "MainWindow created successfully"
3. Main event loop: "Entering main event loop"

At least 2 out of 3 indicators must be present for success.

**Exit Codes**:
- `0`: Application launched successfully
- `1`: Application failed to launch or critical errors detected
- `2`: Invalid arguments or setup issues

**Environment Variables**:
- `DISPLAY`: X11 display for GUI applications (Linux/macOS)
- `QT_QPA_PLATFORM`: Qt platform plugin (e.g., 'offscreen' for headless)

## Integration with GitHub Actions

These scripts are used by the GitHub Actions workflow in `.github/workflows/pr-build-verification.yml` to:

1. **Multi-platform builds**: Automated testing on macOS and Windows
2. **Build verification**: Ensures executables are created successfully
3. **Application launch testing**: Tests startup in headless CI environments
4. **Log analysis**: Analyzes startup logs for success indicators
5. **Status reporting**: Reports build status back to pull requests
6. **Artifact management**: Uploads build logs for debugging

**Workflow triggers**:
- Pull request opened, synchronized, or reopened
- Changes to source code, CMake files, or workflow files

**Build matrix**:
- **Platforms**: macOS-latest, Windows-latest
- **Configurations**: Debug, Release
- **Qt version**: 6.5.0

## Platform-Specific Notes

### macOS
- Uses `QT_QPA_PLATFORM=offscreen` for headless operation
- Implements manual timeout when `timeout` command is not available
- Handles process cleanup with proper signal handling

### Windows
- Uses native Windows process management
- Handles executable paths with `.exe` extension
- Uses `taskkill` for process termination

### Linux
- Uses Xvfb for virtual display when needed
- Supports standard `timeout` command
- Full X11 display emulation for GUI testing

## Troubleshooting

### Common Issues

**OpenGL Context Failures**:
```
ERROR: Failed to create OpenGL context
ERROR: OpenGL 4.1 Core Profile is required but not available
```
This is expected in headless CI environments. The verification script considers logger initialization as sufficient for basic startup verification.

**Timeout Issues**:
If the application launch times out, check:
1. Missing dependencies (Qt libraries, system libraries)
2. Platform-specific graphics requirements
3. Application startup time in CI environment

**Permission Issues**:
```bash
# Make scripts executable
chmod +x scripts/ci/verify-app-launch.sh
```

### Debugging

**View Application Logs**:
```bash
# Check the generated log file
cat app_launch_verification.log
```

**Manual Testing**:
```bash
# Test application launch manually
./build/bin/Vizion3D --log-level debug
```

**CI Environment Testing**:
```bash
# Simulate CI environment
export QT_QPA_PLATFORM=offscreen
./scripts/ci/verify-app-launch.sh ./build/bin/Vizion3D
```

## Future Enhancements

- Add performance benchmarking during application launch
- Implement more sophisticated GUI interaction testing
- Add memory leak detection during startup
- Integrate with code coverage reporting
- Add automated screenshot capture for visual regression testing
