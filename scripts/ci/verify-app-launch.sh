#!/bin/bash
# scripts/ci/verify-app-launch.sh
# Application Launch Verification Script for CI/CD
#
# This script verifies that the Vizion3D application can start successfully
# in a CI/CD environment by launching it, checking for expected log patterns,
# and ensuring it reaches the main event loop without critical errors.
#
# Usage:
#   ./verify-app-launch.sh <executable_path> [timeout_seconds]
#
# Exit codes:
#   0 - Application launched successfully
#   1 - Application failed to launch or critical errors detected
#   2 - Invalid arguments or setup issues

set -e

# Default values
DEFAULT_TIMEOUT=30
LOG_FILE="app_launch_verification.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Usage information
usage() {
    echo "Usage: $0 <executable_path> [timeout_seconds]"
    echo ""
    echo "Arguments:"
    echo "  executable_path   Path to the Vizion3D executable"
    echo "  timeout_seconds   Timeout for application startup (default: $DEFAULT_TIMEOUT)"
    echo ""
    echo "Examples:"
    echo "  $0 ./build/Vizion3D"
    echo "  $0 ./build/Release/Vizion3D.exe 45"
    echo ""
    echo "Environment variables:"
    echo "  DISPLAY          X11 display for GUI applications (Linux/macOS)"
    echo "  QT_QPA_PLATFORM  Qt platform plugin (e.g., 'offscreen' for headless)"
}

# Setup virtual display for headless GUI testing
setup_virtual_display() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "🖥️  Setting up virtual display for Linux..."
        export DISPLAY=:99
        Xvfb :99 -screen 0 1024x768x24 &
        XVFB_PID=$!
        sleep 2
        echo "✅ Virtual display started (PID: $XVFB_PID)"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "🖥️  Setting up virtual display for macOS..."
        # macOS in CI typically has a virtual display available
        # but we can set QT_QPA_PLATFORM for headless operation
        export QT_QPA_PLATFORM=offscreen
        echo "✅ Using offscreen platform for macOS"
    else
        echo "ℹ️  No virtual display setup needed for this platform"
    fi
}

# Cleanup function
cleanup() {
    echo ""
    echo "🧹 Cleaning up..."

    # Kill timeout process if running
    if [[ -n "$TIMEOUT_PID" ]] && kill -0 "$TIMEOUT_PID" 2>/dev/null; then
        echo "  Stopping timeout process (PID: $TIMEOUT_PID)..."
        kill "$TIMEOUT_PID" 2>/dev/null || true
    fi

    # Kill application if still running
    if [[ -n "$APP_PID" ]] && kill -0 "$APP_PID" 2>/dev/null; then
        echo "  Terminating application (PID: $APP_PID)..."
        kill -TERM "$APP_PID" 2>/dev/null || true
        sleep 2
        kill -KILL "$APP_PID" 2>/dev/null || true
    fi

    # Kill virtual display if running
    if [[ -n "$XVFB_PID" ]] && kill -0 "$XVFB_PID" 2>/dev/null; then
        echo "  Stopping virtual display (PID: $XVFB_PID)..."
        kill "$XVFB_PID" 2>/dev/null || true
    fi

    echo "✅ Cleanup completed"
}

# Analyze application logs for startup success indicators
analyze_startup_logs() {
    local log_file="$1"
    local success_count=0
    local total_checks=3

    echo ""
    echo "📋 Analyzing startup logs..."

    # Check for logger initialization
    if grep -q "Logger initialized" "$log_file"; then
        echo -e "  ✅ Logger initialization: ${GREEN}SUCCESS${NC}"
        ((success_count++))
    else
        echo -e "  ❌ Logger initialization: ${RED}FAILED${NC}"
    fi

    # Check for MainWindow creation
    if grep -q "MainWindow created successfully" "$log_file"; then
        echo -e "  ✅ MainWindow creation: ${GREEN}SUCCESS${NC}"
        ((success_count++))
    else
        echo -e "  ❌ MainWindow creation: ${RED}FAILED${NC}"
    fi

    # Check for main event loop entry
    if grep -q "Entering main event loop" "$log_file"; then
        echo -e "  ✅ Main event loop: ${GREEN}SUCCESS${NC}"
        ((success_count++))
    else
        echo -e "  ❌ Main event loop: ${RED}FAILED${NC}"
    fi

    echo ""
    echo "📊 Startup verification: $success_count/$total_checks checks passed"

    # Show relevant log excerpts
    echo ""
    echo "📄 Relevant log excerpts:"
    if grep -E "(Logger initialized|MainWindow created|Entering main event loop|FATAL|ERROR)" "$log_file" > /dev/null; then
        grep -E "(Logger initialized|MainWindow created|Entering main event loop|FATAL|ERROR)" "$log_file" | head -20
    else
        echo "  No relevant log patterns found"
    fi

    # Check for critical errors
    if grep -q -E "(FATAL|critical error)" "$log_file"; then
        echo ""
        echo -e "${RED}❌ Critical errors detected in logs${NC}"
        echo "Critical error details:"
        grep -E "(FATAL|critical error)" "$log_file"
        return 1
    fi

    # Require at least 2 out of 3 success indicators
    if [[ $success_count -ge 2 ]]; then
        echo ""
        echo -e "${GREEN}✅ Application startup verification: PASSED${NC}"
        return 0
    else
        echo ""
        echo -e "${RED}❌ Application startup verification: FAILED${NC}"
        echo "Insufficient success indicators ($success_count/$total_checks)"
        return 1
    fi
}

# Main execution
main() {
    # Parse arguments
    if [[ $# -lt 1 ]]; then
        echo "Error: Missing required argument"
        echo ""
        usage
        exit 2
    fi

    local executable="$1"
    local timeout="${2:-$DEFAULT_TIMEOUT}"

    # Validate executable
    if [[ ! -f "$executable" ]]; then
        echo -e "${RED}❌ Executable not found: $executable${NC}"
        exit 2
    fi

    if [[ ! -x "$executable" ]]; then
        echo -e "${RED}❌ File is not executable: $executable${NC}"
        exit 2
    fi

    echo "🚀 Starting application launch verification..."
    echo "  Executable: $executable"
    echo "  Timeout: ${timeout}s"
    echo "  Log file: $LOG_FILE"
    echo ""

    # Setup cleanup trap
    trap cleanup EXIT

    # Setup virtual display if needed
    setup_virtual_display

    # Launch application with timeout and capture logs
    echo "🔄 Launching application..."

    # Check if timeout command is available
    if command -v timeout >/dev/null 2>&1; then
        # Use timeout command (Linux)
        timeout "${timeout}s" "$executable" --log-level debug > "$LOG_FILE" 2>&1 &
        APP_PID=$!
    elif command -v gtimeout >/dev/null 2>&1; then
        # Use gtimeout (macOS with coreutils)
        gtimeout "${timeout}s" "$executable" --log-level debug > "$LOG_FILE" 2>&1 &
        APP_PID=$!
    else
        # Manual timeout implementation (macOS default)
        "$executable" --log-level debug > "$LOG_FILE" 2>&1 &
        APP_PID=$!

        # Implement manual timeout
        (
            sleep "$timeout"
            if kill -0 "$APP_PID" 2>/dev/null; then
                echo "Timeout reached, terminating application..." >> "$LOG_FILE"
                kill -TERM "$APP_PID" 2>/dev/null || true
                sleep 2
                kill -KILL "$APP_PID" 2>/dev/null || true
            fi
        ) &
        TIMEOUT_PID=$!
    fi

    echo "  Application PID: $APP_PID"
    echo "  Waiting for startup (${timeout}s timeout)..."

    # Wait for startup or timeout
    sleep 10

    # Check if application is still running (indicates successful startup)
    if kill -0 "$APP_PID" 2>/dev/null; then
        echo -e "  ${GREEN}✅ Application is running${NC}"

        # Give it a bit more time to fully initialize
        sleep 5

        # Terminate application gracefully
        echo "  Terminating application..."
        kill -TERM "$APP_PID" 2>/dev/null || true
        sleep 2

        # Force kill if still running
        if kill -0 "$APP_PID" 2>/dev/null; then
            kill -KILL "$APP_PID" 2>/dev/null || true
        fi

        APP_PID=""  # Clear PID to avoid double cleanup
    else
        echo -e "  ${YELLOW}⚠️  Application exited during startup${NC}"
    fi

    # Analyze logs for success patterns
    if analyze_startup_logs "$LOG_FILE"; then
        echo ""
        echo -e "${GREEN}🎉 Application launch verification: SUCCESS${NC}"
        exit 0
    else
        echo ""
        echo -e "${RED}❌ Application launch verification: FAILED${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
