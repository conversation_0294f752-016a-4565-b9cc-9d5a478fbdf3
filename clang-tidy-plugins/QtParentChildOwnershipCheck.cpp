#include "QtParentChildOwnershipCheck.h"
#include "clang/ASTMatchers/ASTMatchers.h"
#include "clang/AST/ExprCXX.h"
#include "clang/AST/Type.h"
#include "clang/AST/DeclCXX.h"
#include "clang/ASTMatchers/ASTMatchFinder.h"
#include "clang-tidy/ClangTidyCheck.h"
#include "clang-tidy/ClangTidyModule.h"
#include "clang-tidy/ClangTidyModuleRegistry.h"

using namespace clang::ast_matchers;

namespace clang {
namespace tidy {
namespace Vizion3dQt {

QtParentChildOwnershipCheck::QtParentChildOwnershipCheck(StringRef Name, ClangTidyContext *Context)
    : ClangTidyCheck(Name, Context) {}

void QtParentChildOwnershipCheck::registerMatchers(MatchFinder *Finder) {
    // Only match C++ source files
    // (getLangOpts() may not be available in all plugin builds)
    // if (!getLangOpts().CPlusPlus) return;

    // Match new QWidget or QTreeWidgetItem (or subclasses) without parent argument
    Finder->addMatcher(
        cxxConstructExpr(
            hasDeclaration(cxxConstructorDecl(ofClass(
                recordDecl(
                    anyOf(
                        hasName("QWidget"),
                        hasName("QTreeWidgetItem")
                    )
                )
            ))),
            argumentCountIs(0)
        ).bind("qtNewNoParent"),
        this
    );

    // Match delete on QWidget or QTreeWidgetItem pointer
    Finder->addMatcher(
        cxxDeleteExpr(
            hasDescendant(
                declRefExpr(to(varDecl(hasType(pointsTo(recordDecl(
                    anyOf(
                        hasName("QWidget"),
                        hasName("QTreeWidgetItem")
                    )
                ))))))
            )
        ).bind("qtDelete"),
        this
    );
}

void QtParentChildOwnershipCheck::check(const MatchFinder::MatchResult &Result) {
    if (const auto *ctorExpr = Result.Nodes.getNodeAs<CXXConstructExpr>("qtNewNoParent")) {
        diag(ctorExpr->getBeginLoc(),
             "QWidget or QTreeWidgetItem (or subclass) should be constructed with a parent pointer for Qt ownership. If intentional, add '// NOLINT(cppcoreguidelines-owning-memory) Qt parent-child ownership'.");
    }
    if (const auto *deleteExpr = Result.Nodes.getNodeAs<CXXDeleteExpr>("qtDelete")) {
        diag(deleteExpr->getBeginLoc(),
             "Never use raw delete on a QWidget or QTreeWidgetItem with a parent; let Qt manage deletion.");
    }
}

} // namespace Vizion3dQt
} // namespace tidy
} // namespace clang

// Register the check as a clang-tidy module
namespace {
class Vizion3DQtModule : public clang::tidy::ClangTidyModule {
public:
    void addCheckFactories(clang::tidy::ClangTidyCheckFactories &Factories) override {
        Factories.registerCheck<clang::tidy::Vizion3dQt::QtParentChildOwnershipCheck>("vizion3d-qt-parent-child-ownership");
    }
};
} // anonymous namespace

// Register the Vizion3DQtModule using this statically initialized variable.
const static clang::tidy::ClangTidyModuleRegistry::Add<Vizion3DQtModule>
    kVizion3dQtModuleRegistry("vizion3d-qt-module", "Adds Vizion3D Qt memory management checks.");

// This anchor is used to force the linker to link in the generated object file and thus register the module.
const volatile int kVizion3dQtModuleAnchorSource = 0;
