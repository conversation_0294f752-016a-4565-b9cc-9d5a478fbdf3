#ifndef QT_PARENT_CHILD_OWNERSHIP_CHECK_H
#define QT_PARENT_CHILD_OWNERSHIP_CHECK_H

#include "clang/ASTMatchers/ASTMatchFinder.h"
#include "clang-tidy/ClangTidyCheck.h"

namespace clang {
namespace tidy {
namespace Vizion3dQt {

class QtParentChildOwnershipCheck : public ClangTidyCheck {
public:
    QtParentChildOwnershipCheck(StringRef Name, ClangTidyContext *Context);
    void registerMatchers(ast_matchers::MatchFinder *Finder) override;
    void check(const ast_matchers::MatchFinder::MatchResult &Result) override;
};

} // namespace Vizion3dQt
} // namespace tidy
} // namespace clang

#endif // QT_PARENT_CHILD_OWNERSHIP_CHECK_H
