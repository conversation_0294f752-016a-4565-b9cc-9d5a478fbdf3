# Qt Memory Management Clang-Tidy Plugin

This directory contains a custom clang-tidy check for enforcing Qt parent-child ownership and safe memory management patterns in the Vizion3D project.

## What It Checks
- **QWidget/QTreeWidgetItem Construction:**
  - Flags any `new QWidget` or `new QTreeWidgetItem` (or subclass) that does not pass a parent pointer.
- **Raw Delete:**
  - Flags any use of `delete` on a `QWidget` or `QTreeWidgetItem` pointer (or subclass).

## Rationale
Qt's parent-child ownership model is the standard for memory management. Manual deletion or missing parent pointers can cause memory leaks or crashes. This check enforces best practices and prevents common errors.

## Usage
1. Build the plugin:
   - The main CMakeLists.txt includes this directory. Build the project to produce the plugin shared library.
2. Run clang-tidy with the plugin:
   ```sh
   clang-tidy -load ./clang-tidy-plugins/libQtMemoryManagementClangTidyPlugin.so -checks='-*,vizion3d-qt-parent-child-ownership' <your_file.cpp>
   ```
3. Integrate into your CI or pre-commit hooks as needed.

## Suppressing False Positives
If you intentionally construct a Qt object without a parent, add:
```cpp
// NOLINT(cppcoreguidelines-owning-memory) Qt parent-child ownership
```

## Extending
You can add more Qt types or refine the matchers in `QtParentChildOwnershipCheck.cpp` as needed. 